### 项目描述
- 项目名称:  基于零售站点前置仓的巡检系统

```
# 和开发无关
- 甲方（需求方）： 上海唔唬科技发展有限公司
- 乙方（开发方）： 上海印答机电科技有限公司
- 项目开始时间： 2023-09-14
- 项目结束时间： 2024-09-13
```

### 测试地址

- http://127.0.0.1:8000/ 不需要额外启动 http 服务器。用户自己会测试。
- 网站根目录为 /admin/， 不考虑 admin 这个文件夹的路径
- 后台文件目录：/admin
- 默认用户名为：admin
- 默认用户密码：admin123
- 页面文件：/admin/pages/
- 公共js 目录：/admin/js/
- cdn 本地化目录：/admin/cdn/
- json mock 数据目录：/admin/data/
- 登录页： /admin/index.html
- 登陆后的后台首页： /admin/pages/home.html
- menu.js 的菜单跳的要求是，直接 url 跳转，

### 后台要求

1. mock 数据，人名后面不应该有数据，这样不正常。mock 数据至少 30 行以上
2. menu.js 不需要 icon，因为icon 会影响 menu 的正常显示。
3. html 不应该和 css 和 js 混写，放在单独的文件中。这样可以避免 html 文件冗长。
4. 每个 html 应该有自己单独的 css 文件，css 文件存放在 /admin/css/ 下
5. 每个 html 应该有自己的 js 文件，js 文件存放在 /admin/js/ 下
6. 每个 html 应该有自己的 mock 数据，格式为 json ，文件存放在 /admin/data/ 下


### 功能描述

## 一、智能巡检管理功能
- 1. AI视觉识别巡检
  - 商品有效期检测
  - 货架陈列合规性检查
  - 商品位置与库存同步识别
- 2. 自动化巡检流程
  - AI生成巡检路径
  - 全流程自动化巡检
- 3. 多终端协同操作
  - PDA与AR眼镜双模操作
  - 数据实时同步
- 4. 异常闭环管理
  - 三级响应机制
  - 异常问题追踪处理

## 二、实时监控与预警功能
- 1. 多维度实时监控
  - 设备状态/环境参数/人员动线监控
  - 冷链设备异常报警
- 2. 可视化看板与预警
  - 运营健康度驾驶舱
  - 设备故障预警

## 三、数据采集与分析功能
- 1. 全要素数据采集
  - RFID/IoT传感器数据采集
  - 商品位置/环境/设备数据同步
- 2. 智能数据预处理
  - 数据清洗与异常识别
  - 无效信号过滤
- 3. 分析模型与决策支持
  - 专业分析模型（库存/故障预判等）
  - 运营风险预警
  - 自动生成运营报表

## 四、硬件与技术支持功能
- 1. 边缘计算与通信
  - 边缘计算单元
  - 5G工业通信
- 2. 传感器与终端设备
  - 多模态传感器
  - 巡检机器人
- 3. 软件平台架构
  - 微服务架构
  - 流式计算引擎
  - AI算法引擎

## 五、实施与运维支持功能
- 1. 部署前准备
  - 环境三维建模
  - 定制巡检指标
  - 历史数据迁移
- 2. 系统集成调试
  - 设备联调
  - 压力测试
  - 系统接口打通

## 六、未来扩展功能
- 1. 技术升级
  - 边缘算力提升
  - 全仓数字孪生
  - 区块链应用
- 2. 功能迭代
  - AR辅助巡检
  - 自动化补货
  - 跨仓智能调度
  - 无人化运营

## 七、行业适配与合规功能
- 1. 多场景应用
  - 生鲜冷链损耗控制
  - 医药冷链温控合规
  - 日用品仓运营优化
- 2. 合规与安全保障
  - 数据存证追溯
  - 双活数据中心
  - 安全生产认证支持

### 后台管理
- 后台目录：admin
- 默认用户名为：admin
- 默认用户密码：admin123

## 需要制作的页面

### 智能巡检管理
- **AI视觉识别巡检**: `ai_inspection.html` 【已完成】
- **自动化巡检流程**: `auto_inspection.html`
- **多终端协同操作**: `multi_terminal.html`
- **异常闭环管理**: `exception_management.html`

### 实时监控与预警
- **多维度实时监控**: `realtime_monitoring.html`
- **可视化看板与预警**: `visual_dashboard.html`

### 数据采集与分析
- **全要素数据采集**: `data_collection.html`
- **智能数据预处理**: `data_preprocessing.html`
- **分析模型与决策支持**: `analysis_model.html`

### 硬件与技术支持
- **边缘计算与通信**: `edge_computing.html`
- **传感器与终端设备**: `sensor_device.html`
- **软件平台架构**: `platform_architecture.html`

### 实施与运维支持
- **部署前准备**: `deployment_preparation.html`
- **系统集成调试**: `system_integration.html`

### 未来扩展功能
- **技术升级**: `technology_upgrade.html`
- **功能迭代**: `function_iteration.html`

### 行业适配与合规
- **多场景应用**: `multi_scenario.html`
- **合规与安全保障**: `compliance_security.html`