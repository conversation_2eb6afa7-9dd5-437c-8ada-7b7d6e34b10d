/* ========================================
   系统管理弹窗样式
   ======================================== */

/* 日志容器 */
.logs-container {
  max-height: 400px;
  overflow-y: auto;
}

.logs-toolbar {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e5e5e5;
}

.logs-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.log-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px;
  border-radius: 6px;
  font-size: 14px;
  border-left: 4px solid #ccc;
}

.log-item.log-info {
  background-color: #f0f9ff;
  border-left-color: #3b82f6;
}

.log-item.log-warn {
  background-color: #fffbeb;
  border-left-color: #f59e0b;
}

.log-item.log-error {
  background-color: #fef2f2;
  border-left-color: #ef4444;
}

.log-time {
  color: #666;
  font-size: 12px;
  min-width: 140px;
}

.log-level {
  font-weight: 600;
  min-width: 60px;
  text-align: center;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.log-item.log-info .log-level {
  background-color: #dbeafe;
  color: #1d4ed8;
}

.log-item.log-warn .log-level {
  background-color: #fef3c7;
  color: #d97706;
}

.log-item.log-error .log-level {
  background-color: #fee2e2;
  color: #dc2626;
}

.log-module {
  font-weight: 500;
  min-width: 80px;
}

.log-message {
  flex: 1;
  color: #374151;
}

/* 告警容器 */
.alarms-container {
  max-height: 400px;
  overflow-y: auto;
}

.alarms-toolbar {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e5e5e5;
}

.alarms-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.alarm-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px;
  border-radius: 6px;
  font-size: 14px;
  border-left: 4px solid #ccc;
}

.alarm-item.alarm-高 {
  background-color: #fef2f2;
  border-left-color: #ef4444;
}

.alarm-item.alarm-中 {
  background-color: #fffbeb;
  border-left-color: #f59e0b;
}

.alarm-item.alarm-低 {
  background-color: #f0f9ff;
  border-left-color: #3b82f6;
}

.alarm-time {
  color: #666;
  font-size: 12px;
  min-width: 140px;
}

.alarm-level {
  font-weight: 600;
  min-width: 40px;
  text-align: center;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.alarm-item.alarm-高 .alarm-level {
  background-color: #fee2e2;
  color: #dc2626;
}

.alarm-item.alarm-中 .alarm-level {
  background-color: #fef3c7;
  color: #d97706;
}

.alarm-item.alarm-低 .alarm-level {
  background-color: #dbeafe;
  color: #1d4ed8;
}

.alarm-type {
  font-weight: 500;
  min-width: 80px;
}

.alarm-device {
  font-weight: 500;
  min-width: 80px;
}

.alarm-status {
  flex: 1;
  text-align: right;
  font-weight: 500;
}

/* 权限容器 */
.permissions-container {
  max-height: 400px;
  overflow-y: auto;
}

.permissions-toolbar {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e5e5e5;
}

.permissions-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.user-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px;
  border-radius: 6px;
  font-size: 14px;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
}

.user-id {
  font-weight: 600;
  min-width: 80px;
  color: #374151;
}

.user-name {
  font-weight: 500;
  min-width: 80px;
}

.user-role {
  font-weight: 500;
  min-width: 100px;
  color: #059669;
}

.user-permissions {
  flex: 1;
  color: #6b7280;
  font-size: 13px;
}

.user-status {
  font-weight: 500;
  color: #059669;
}

/* 固件容器 */
.firmware-container {
  max-height: 400px;
  overflow-y: auto;
}

.firmware-toolbar {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e5e5e5;
}

.firmware-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.firmware-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px;
  border-radius: 6px;
  font-size: 14px;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
}

.firmware-version {
  font-weight: 600;
  min-width: 80px;
  color: #374151;
}

.firmware-type {
  font-weight: 500;
  min-width: 80px;
}

.firmware-date {
  font-weight: 500;
  min-width: 100px;
  color: #6b7280;
}

.firmware-status {
  flex: 1;
  font-weight: 500;
}

.firmware-status:contains('最新') {
  color: #059669;
}

.firmware-status:contains('稳定') {
  color: #3b82f6;
}

.firmware-status:contains('旧') {
  color: #f59e0b;
}

.firmware-downloads {
  font-weight: 500;
  color: #6b7280;
  min-width: 80px;
  text-align: right;
}

/* =============================
   TDesign风格通用样式补充
   ============================= */

/* 弹窗样式 */
.td-modal {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 32px 0 rgba(0,0,0,0.18);
  padding: 32px 28px 20px 28px;
  min-width: 340px;
  max-width: 96vw;
  position: relative;
  font-size: 15px;
}
.td-modal-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 18px;
  color: #222;
}
.td-modal-close {
  position: absolute;
  right: 18px;
  top: 18px;
  font-size: 20px;
  color: #999;
  cursor: pointer;
  transition: color 0.2s;
}
.td-modal-close:hover {
  color: #0052d9;
}
.td-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

/* 表单控件 */
.td-form-item {
  display: flex;
  align-items: center;
  margin-bottom: 18px;
}
.td-form-label {
  min-width: 90px;
  text-align: right;
  margin-right: 12px;
  color: #666;
  font-size: 15px;
}
.td-input, .td-select, .td-number, .td-checkbox {
  border: 1px solid #dcdcdc;
  border-radius: 6px;
  padding: 7px 12px;
  font-size: 15px;
  outline: none;
  transition: border 0.2s;
}
.td-input:focus, .td-select:focus, .td-number:focus {
  border-color: #0052d9;
}
.td-checkbox {
  width: 18px;
  height: 18px;
  accent-color: #0052d9;
  margin-right: 6px;
}
.td-form-actions {
  display: flex;
  gap: 16px;
  margin-top: 10px;
}

/* 按钮 */
.btn-primary {
  background: #0052d9;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 7px 22px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}
.btn-primary:hover:not(:disabled) {
  background: #1765ff;
}
.btn-secondary {
  background: #f4f6fa;
  color: #0052d9;
  border: 1px solid #dcdcdc;
  border-radius: 6px;
  padding: 7px 22px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.btn-secondary:hover:not(:disabled) {
  background: #e9f2ff;
  color: #1765ff;
}
.btn-danger {
  background: #ff4d4f;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 7px 22px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}
.btn-danger:hover:not(:disabled) {
  background: #d9363e;
}
.btn[disabled], .btn-primary[disabled], .btn-secondary[disabled], .btn-danger[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 统计卡片 */
.td-stat-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.06);
  padding: 22px 28px 18px 28px;
  min-width: 180px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 18px;
  transition: box-shadow 0.2s;
}
.td-stat-card:hover {
  box-shadow: 0 4px 16px 0 rgba(0,82,217,0.10);
}
.td-stat-title {
  font-size: 15px;
  color: #666;
  margin-bottom: 8px;
}
.td-stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #0052d9;
  margin-bottom: 4px;
}
.td-stat-desc {
  font-size: 13px;
  color: #999;
}

/* 主卡片 */
.td-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.06);
  padding: 28px 32px 22px 32px;
  margin-bottom: 24px;
}
.td-card-title {
  font-size: 20px;
  font-weight: 600;
  color: #222;
  margin-bottom: 18px;
}

/* 工具栏 */
.td-toolbar {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 18px;
}

/* 分页控件 */
.td-pagination {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 18px;
  justify-content: flex-end;
}
.td-pagination-btn {
  background: #f4f6fa;
  color: #0052d9;
  border: 1px solid #dcdcdc;
  border-radius: 6px;
  padding: 5px 14px;
  font-size: 15px;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.td-pagination-btn.active, .td-pagination-btn:hover:not(:disabled) {
  background: #e9f2ff;
  color: #1765ff;
}
.td-pagination-btn[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 表格样式 */
.td-table {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 18px;
}
.td-table th, .td-table td {
  padding: 10px 14px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 15px;
}
.td-table th {
  background: #f4f6fa;
  color: #222;
  font-weight: 600;
}
.td-table tr:last-child td {
  border-bottom: none;
}

/* 响应式布局 */
@media (max-width: 900px) {
  .td-card, .td-modal, .td-stat-card {
    padding: 16px 8px;
    min-width: 0;
  }
  .td-card-title, .td-modal-title {
    font-size: 16px;
  }
  .td-stat-value {
    font-size: 22px;
  }
}

/* ========================================
   图表样式 - 解决超出容器问题
   ======================================== */

/* 图表网格布局 */
.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

/* 图表卡片容器 */
.chart-card {
  position: relative;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.06);
  overflow: hidden;
  margin-bottom: 20px;
  transition: box-shadow 0.2s;
}

.chart-card:hover {
  box-shadow: 0 4px 16px 0 rgba(0,82,217,0.10);
}

/* 图表标题区域 */
.chart-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e7e7e7;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
}

.chart-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #000000;
}

.chart-actions {
  display: flex;
  gap: 8px;
}

/* 图表容器 */
.chart-container {
  position: relative;
  width: 100%;
  height: 400px;
  padding: 20px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 图表画布样式 - 关键修复 */
.chart-card canvas {
  max-width: 100% !important;
  max-height: 100% !important;
  width: auto !important;
  height: auto !important;
  display: block;
  object-fit: contain;
}

/* 确保图表不会超出容器 */
.chart-card canvas[width][height] {
  max-width: 100% !important;
  max-height: 100% !important;
  width: auto !important;
  height: auto !important;
  object-fit: contain;
}

/* 图表加载状态 */
.chart-card.loading {
  position: relative;
}

.chart-card.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 30px;
  height: 30px;
  margin: -15px 0 0 -15px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #0052d9;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 图表空状态 */
.chart-card.empty {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: #999;
  font-size: 14px;
}

/* 响应式图表 */
@media (max-width: 768px) {
  .charts-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .chart-container {
    height: 350px;
    padding: 15px;
  }
  
  .chart-header {
    padding: 12px 15px;
  }
  
  .chart-header h3 {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .chart-container {
    height: 300px;
    padding: 10px;
  }
  
  .chart-header {
    padding: 10px 12px;
  }
  
  .chart-actions {
    gap: 4px;
  }
  
  .btn-small {
    padding: 4px 8px;
    font-size: 12px;
  }
}

/* ========================================
   图表超出容器问题修复 - 额外样式
   ======================================== */

/* 强制图表适应容器 */
.chart-container canvas {
  max-width: 100% !important;
  max-height: 100% !important;
  width: auto !important;
  height: auto !important;
  object-fit: contain !important;
  display: block !important;
}

/* 针对Chart.js的特定样式 */
.chart-container canvas[width="400"][height="200"] {
  max-width: 100% !important;
  max-height: 100% !important;
  width: auto !important;
  height: auto !important;
  object-fit: contain !important;
}

/* 确保图表卡片有足够空间 */
.chart-card {
  min-height: 450px;
  display: flex;
  flex-direction: column;
}

.chart-container {
  flex: 1;
  min-height: 0;
}

/* 图表网格优化 */
.charts-grid {
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

/* 大屏幕优化 */
@media (min-width: 1200px) {
  .charts-grid {
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  }
  
  .chart-container {
    height: 450px;
  }
}

/* 确保图表在所有情况下都不会超出 */
* {
  box-sizing: border-box;
}

.chart-container * {
  max-width: 100% !important;
  max-height: 100% !important;
} 