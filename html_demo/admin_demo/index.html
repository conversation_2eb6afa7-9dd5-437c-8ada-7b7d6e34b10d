<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>共享充电宝市场热点分析系统</title>
  <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
  <style>
    /* TDesign Starter 风格重置 */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
      background: var(--td-bg-color-page);
      color: var(--td-text-color-primary);
      line-height: 1.6;
      font-size: 14px;
    }

    /* TDesign 颜色变量 */
    :root {
      /* 品牌色 */
      --td-brand-color: #0052d9;
      --td-brand-color-hover: #266fe8;
      --td-brand-color-active: #0034b5;
      --td-brand-color-light: #e3f2fd;
      --td-brand-color-focus: #e3f2fd;
      
      /* 功能色 */
      --td-success-color: #00a870;
      --td-success-color-hover: #00b870;
      --td-success-color-light: #e8f5e8;
      --td-warning-color: #ed7b2f;
      --td-warning-color-hover: #f57c00;
      --td-warning-color-light: #fff3e0;
      --td-error-color: #d54941;
      --td-error-color-hover: #c62828;
      --td-error-color-light: #ffebee;
      --td-info-color: #0052d9;
      --td-info-color-hover: #266fe8;
      --td-info-color-light: #e3f2fd;
      
      /* 文字色 */
      --td-text-color-primary: #000000;
      --td-text-color-secondary: #666666;
      --td-text-color-placeholder: #bbbbbb;
      --td-text-color-disabled: #bbbbbb;
      --td-text-color-anti: #ffffff;
      --td-text-color-brand: #0052d9;
      
      /* 背景色 */
      --td-bg-color-page: #f5f5f5;
      --td-bg-color-container: #ffffff;
      --td-bg-color-secondarycontainer: #f8f9fa;
      --td-bg-color-tertiarycontainer: #f3f3f3;
      --td-bg-color-container-hover: #f8f9fa;
      --td-bg-color-container-active: #f3f3f3;
      
      /* 边框色 */
      --td-border-color: #e7e7e7;
      --td-border-color-light: #f0f0f0;
      --td-border-color-focus: #0052d9;
      
      /* 阴影 */
      --td-shadow-1: 0 1px 10px rgba(0, 0, 0, 0.05);
      --td-shadow-2: 0 3px 14px 2px rgba(0, 0, 0, 0.05);
      --td-shadow-3: 0 6px 30px 5px rgba(0, 0, 0, 0.05);
      
      /* 圆角 */
      --td-radius-default: 6px;
      --td-radius-medium: 8px;
      --td-radius-large: 12px;
      --td-radius-extra-large: 16px;
      
      /* 间距系统 */
      --td-comp-size-xxs: 4px;
      --td-comp-size-xs: 8px;
      --td-comp-size-s: 12px;
      --td-comp-size-m: 16px;
      --td-comp-size-l: 20px;
      --td-comp-size-xl: 24px;
      --td-comp-size-xxl: 32px;
      --td-comp-size-xxxl: 48px;
      
      /* 字体大小 */
      --td-font-size-body-small: 12px;
      --td-font-size-body-medium: 14px;
      --td-font-size-body-large: 16px;
      --td-font-size-title-small: 18px;
      --td-font-size-title-medium: 20px;
      --td-font-size-title-large: 24px;
      --td-font-size-headline-small: 28px;
      --td-font-size-headline-medium: 32px;
      --td-font-size-headline-large: 36px;
      
      /* 行高 */
      --td-line-height-body-small: 1.4;
      --td-line-height-body-medium: 1.6;
      --td-line-height-body-large: 1.6;
      --td-line-height-title-small: 1.4;
      --td-line-height-title-medium: 1.4;
      --td-line-height-title-large: 1.3;
      
      /* 响应式断点 */
      --td-breakpoint-xs: 480px;
      --td-breakpoint-sm: 768px;
      --td-breakpoint-md: 992px;
      --td-breakpoint-lg: 1200px;
      --td-breakpoint-xl: 1400px;
      --td-breakpoint-xxl: 1600px;
    }

    /* 响应式断点 */
    @media (max-width: 480px) {
      :root {
        --td-comp-size-m: 12px;
        --td-comp-size-l: 16px;
        --td-comp-size-xl: 20px;
        --td-comp-size-xxl: 24px;
      }
    }

    @media (max-width: 768px) {
      .sidebar {
        transform: translateX(-100%);
      }
      
      .sidebar.show {
        transform: translateX(0);
      }
      
      .main-content {
        margin-left: 0;
      }
      
      .menu-toggle {
        display: block;
      }
      
      .stats-grid {
        grid-template-columns: 1fr;
      }
      
      .function-grid {
        grid-template-columns: 1fr;
      }
    }

    @media (max-width: 992px) {
      .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
      }
    }

    /* 布局容器 */
    .app-container {
      display: flex;
      min-height: 100vh;
      background: var(--td-bg-color-page);
    }

    /* 侧边栏 */
    .sidebar {
      width: 240px;
      background: var(--td-bg-color-container);
      border-right: 1px solid var(--td-border-color);
      box-shadow: var(--td-shadow-1);
      position: fixed;
      height: 100vh;
      z-index: 1000;
      transition: all 0.3s ease;
    }

    .sidebar-header {
      padding: var(--td-comp-size-xl) var(--td-comp-size-l);
      border-bottom: 1px solid var(--td-border-color-light);
      background: linear-gradient(135deg, var(--td-brand-color) 0%, var(--td-brand-color-hover) 100%);
      color: var(--td-text-color-anti);
    }

    .sidebar-header h1 {
      font-size: 18px;
      font-weight: 600;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .sidebar-header .logo-icon {
      font-size: 24px;
      color: #ffffff;
    }

    .nav-menu {
      padding: 16px 0;
    }

    .nav-item {
      display: flex;
      align-items: center;
      padding: 12px 20px;
      color: var(--td-text-color-secondary);
      text-decoration: none;
      transition: all 0.2s ease;
      border-left: 3px solid transparent;
      margin: 4px 0;
    }

    .nav-item:hover {
      background: var(--td-bg-color-secondarycontainer);
      color: var(--td-brand-color);
      border-left-color: var(--td-brand-color);
    }

    .nav-item.active {
      background: var(--td-brand-color-light);
      color: var(--td-brand-color);
      border-left-color: var(--td-brand-color);
      font-weight: 500;
    }

    .nav-item i {
      margin-right: 12px;
      font-size: 18px;
      width: 20px;
      text-align: center;
    }

    /* 主内容区 */
    .main-content {
      flex: 1;
      margin-left: 240px;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }

    /* 顶部导航栏 */
    .top-navbar {
      background: var(--td-bg-color-container);
      border-bottom: 1px solid var(--td-border-color);
      padding: 0 24px;
      height: 64px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-shadow: var(--td-shadow-1);
      position: sticky;
      top: 0;
      z-index: 999;
    }

    .navbar-left {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .menu-toggle {
      display: none;
      background: none;
      border: none;
      font-size: 20px;
      color: var(--td-text-color-secondary);
      cursor: pointer;
      padding: 8px;
      border-radius: var(--td-radius-default);
      transition: all 0.2s ease;
    }

    .menu-toggle:hover {
      background: var(--td-bg-color-secondarycontainer);
      color: var(--td-brand-color);
    }

    .breadcrumb {
      display: flex;
      align-items: center;
      gap: 8px;
      color: var(--td-text-color-secondary);
      font-size: 14px;
    }

    .breadcrumb-separator {
      color: var(--td-text-color-placeholder);
    }

    .navbar-right {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: var(--td-radius-default);
      transition: all 0.2s ease;
    }

    .user-info:hover {
      background: var(--td-bg-color-secondarycontainer);
    }

    .user-avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: var(--td-brand-color);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 500;
    }

    .user-name {
      color: var(--td-text-color-primary);
      font-weight: 500;
    }

    .dropdown-menu {
      position: absolute;
      top: 100%;
      right: 0;
      background: var(--td-bg-color-container);
      border: 1px solid var(--td-border-color);
      border-radius: var(--td-radius-medium);
      box-shadow: var(--td-shadow-2);
      min-width: 160px;
      z-index: 1001;
      opacity: 0;
      visibility: hidden;
      transform: translateY(-10px);
      transition: all 0.2s ease;
    }

    .dropdown-menu.show {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }

    .dropdown-item {
      padding: 12px 16px;
      color: var(--td-text-color-primary);
      text-decoration: none;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: all 0.2s ease;
    }

    .dropdown-item:hover {
      background: var(--td-bg-color-secondarycontainer);
      color: var(--td-brand-color);
    }

    .dropdown-item i {
      font-size: 16px;
    }

    /* 页面内容 */
    .page-content {
      flex: 1;
      padding: 24px;
      overflow-y: auto;
    }

    /* 页面头部 */
    .page-header {
      background: var(--td-bg-color-container);
      border-radius: var(--td-radius-large);
      padding: 24px;
      margin-bottom: 24px;
      box-shadow: var(--td-shadow-1);
      border: 1px solid var(--td-border-color);
    }

    .page-header h2 {
      font-size: 24px;
      font-weight: 600;
      color: var(--td-text-color-primary);
      margin: 0 0 8px 0;
    }

    .page-header .header-actions {
      display: flex;
      gap: 12px;
      margin-top: 16px;
    }

    /* 统计卡片网格 */
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 24px;
      margin-bottom: 24px;
    }

    .stat-card {
      background: var(--td-bg-color-container);
      border-radius: var(--td-radius-large);
      padding: 24px;
      box-shadow: var(--td-shadow-1);
      border: 1px solid var(--td-border-color);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .stat-card:hover {
      transform: translateY(-4px);
      box-shadow: var(--td-shadow-2);
    }

    .stat-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, var(--td-brand-color) 0%, var(--td-brand-color-hover) 100%);
    }

    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: var(--td-radius-medium);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      margin-bottom: 16px;
    }

    .stat-icon.blue {
      background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
      color: var(--td-brand-color);
    }

    .stat-icon.green {
      background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
      color: var(--td-success-color);
    }

    .stat-icon.orange {
      background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
      color: var(--td-warning-color);
    }

    .stat-icon.red {
      background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
      color: var(--td-error-color);
    }

    .stat-content h4 {
      font-size: 14px;
      color: var(--td-text-color-secondary);
      margin: 0 0 8px 0;
      font-weight: 500;
    }

    .stat-content .value {
      font-size: 32px;
      font-weight: 700;
      color: var(--td-text-color-primary);
      margin: 0 0 4px 0;
      line-height: 1;
    }

    .stat-content .subtitle {
      font-size: 12px;
      color: var(--td-text-color-placeholder);
      margin: 0;
    }

    /* 模块卡片 */
    .module-card {
      background: var(--td-bg-color-container);
      border-radius: var(--td-radius-large);
      padding: 24px;
      margin-bottom: 24px;
      box-shadow: var(--td-shadow-1);
      border: 1px solid var(--td-border-color);
    }

    .module-card h3 {
      font-size: 18px;
      font-weight: 600;
      color: var(--td-text-color-primary);
      margin: 0 0 20px 0;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .module-card h3 i {
      color: var(--td-brand-color);
    }

    /* 按钮样式 - TDesign规范 */
    .btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: var(--td-comp-size-xxs);
      padding: var(--td-comp-size-s) var(--td-comp-size-m);
      border: 1px solid transparent;
      border-radius: var(--td-radius-default);
      font-size: var(--td-font-size-body-medium);
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s cubic-bezier(0.38, 0, 0.24, 1);
      text-decoration: none;
      line-height: 1;
      white-space: nowrap;
      position: relative;
      overflow: hidden;
    }

    .btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      pointer-events: none;
    }

    .btn:focus {
      outline: none;
      box-shadow: 0 0 0 3px var(--td-brand-color-focus);
    }

    /* 主要按钮 */
    .btn-primary {
      background: var(--td-brand-color);
      color: var(--td-text-color-anti);
      border-color: var(--td-brand-color);
    }

    .btn-primary:hover:not(:disabled) {
      background: var(--td-brand-color-hover);
      border-color: var(--td-brand-color-hover);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 82, 217, 0.3);
    }

    .btn-primary:active:not(:disabled) {
      background: var(--td-brand-color-active);
      border-color: var(--td-brand-color-active);
      transform: translateY(0);
    }

    /* 次要按钮 */
    .btn-secondary {
      background: var(--td-bg-color-container);
      color: var(--td-text-color-primary);
      border-color: var(--td-border-color);
    }

    .btn-secondary:hover:not(:disabled) {
      background: var(--td-bg-color-container-hover);
      border-color: var(--td-brand-color);
      color: var(--td-brand-color);
    }

    .btn-secondary:active:not(:disabled) {
      background: var(--td-bg-color-container-active);
    }

    /* 成功按钮 */
    .btn-success {
      background: var(--td-success-color);
      color: var(--td-text-color-anti);
      border-color: var(--td-success-color);
    }

    .btn-success:hover:not(:disabled) {
      background: var(--td-success-color-hover);
      border-color: var(--td-success-color-hover);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 168, 112, 0.3);
    }

    /* 警告按钮 */
    .btn-warning {
      background: var(--td-warning-color);
      color: var(--td-text-color-anti);
      border-color: var(--td-warning-color);
    }

    .btn-warning:hover:not(:disabled) {
      background: var(--td-warning-color-hover);
      border-color: var(--td-warning-color-hover);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(237, 123, 47, 0.3);
    }

    /* 危险按钮 */
    .btn-danger {
      background: var(--td-error-color);
      color: var(--td-text-color-anti);
      border-color: var(--td-error-color);
    }

    .btn-danger:hover:not(:disabled) {
      background: var(--td-error-color-hover);
      border-color: var(--td-error-color-hover);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(213, 73, 65, 0.3);
    }

    /* 信息按钮 */
    .btn-info {
      background: var(--td-info-color);
      color: var(--td-text-color-anti);
      border-color: var(--td-info-color);
    }

    .btn-info:hover:not(:disabled) {
      background: var(--td-info-color-hover);
      border-color: var(--td-info-color-hover);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 82, 217, 0.3);
    }

    /* 按钮尺寸 */
    .btn-xs {
      padding: var(--td-comp-size-xxs) var(--td-comp-size-s);
      font-size: var(--td-font-size-body-small);
      border-radius: calc(var(--td-radius-default) - 2px);
    }

    .btn-sm {
      padding: var(--td-comp-size-xs) var(--td-comp-size-s);
      font-size: var(--td-font-size-body-small);
    }

    .btn-lg {
      padding: var(--td-comp-size-m) var(--td-comp-size-xl);
      font-size: var(--td-font-size-body-large);
    }

    .btn-xl {
      padding: var(--td-comp-size-l) var(--td-comp-size-xxl);
      font-size: var(--td-font-size-title-small);
    }

    /* 按钮变体 */
    .btn-outline {
      background: transparent;
    }

    .btn-outline.btn-primary {
      color: var(--td-brand-color);
      border-color: var(--td-brand-color);
    }

    .btn-outline.btn-primary:hover:not(:disabled) {
      background: var(--td-brand-color);
      color: var(--td-text-color-anti);
    }

    .btn-outline.btn-success {
      color: var(--td-success-color);
      border-color: var(--td-success-color);
    }

    .btn-outline.btn-success:hover:not(:disabled) {
      background: var(--td-success-color);
      color: var(--td-text-color-anti);
    }

    .btn-outline.btn-warning {
      color: var(--td-warning-color);
      border-color: var(--td-warning-color);
    }

    .btn-outline.btn-warning:hover:not(:disabled) {
      background: var(--td-warning-color);
      color: var(--td-text-color-anti);
    }

    .btn-outline.btn-danger {
      color: var(--td-error-color);
      border-color: var(--td-error-color);
    }

    .btn-outline.btn-danger:hover:not(:disabled) {
      background: var(--td-error-color);
      color: var(--td-text-color-anti);
    }

    .btn-outline.btn-info {
      color: var(--td-info-color);
      border-color: var(--td-info-color);
    }

    .btn-outline.btn-info:hover:not(:disabled) {
      background: var(--td-info-color);
      color: var(--td-text-color-anti);
    }

    /* 表格样式 - TDesign规范 */
    .table-container {
      background: var(--td-bg-color-container);
      border-radius: var(--td-radius-medium);
      overflow: hidden;
      border: 1px solid var(--td-border-color);
      box-shadow: var(--td-shadow-1);
    }

    .data-table {
      width: 100%;
      border-collapse: collapse;
      font-size: var(--td-font-size-body-medium);
      line-height: var(--td-line-height-body-medium);
    }

    .data-table th {
      background: var(--td-bg-color-secondarycontainer);
      padding: var(--td-comp-size-m) var(--td-comp-size-s);
      text-align: left;
      font-weight: 600;
      color: var(--td-text-color-primary);
      border-bottom: 1px solid var(--td-border-color);
      font-size: var(--td-font-size-body-small);
      text-transform: uppercase;
      letter-spacing: 0.5px;
      position: sticky;
      top: 0;
      z-index: 10;
    }

    .data-table td {
      padding: var(--td-comp-size-m) var(--td-comp-size-s);
      border-bottom: 1px solid var(--td-border-color-light);
      color: var(--td-text-color-primary);
      vertical-align: middle;
    }

    .data-table tr:hover {
      background: var(--td-bg-color-container-hover);
    }

    .data-table tr:last-child td {
      border-bottom: none;
    }

    .data-table tr.selected {
      background: var(--td-brand-color-light);
    }

    .data-table tr.selected:hover {
      background: rgba(0, 82, 217, 0.15);
    }

    /* 表格操作列 */
    .table-actions {
      display: flex;
      gap: var(--td-comp-size-xs);
      align-items: center;
    }

    .table-actions .btn {
      padding: var(--td-comp-size-xxs) var(--td-comp-size-xs);
      font-size: var(--td-font-size-body-small);
    }

    /* 表格分页 */
    .table-pagination {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--td-comp-size-m);
      background: var(--td-bg-color-secondarycontainer);
      border-top: 1px solid var(--td-border-color);
    }

    .pagination-info {
      color: var(--td-text-color-secondary);
      font-size: var(--td-font-size-body-small);
    }

    .pagination-controls {
      display: flex;
      align-items: center;
      gap: var(--td-comp-size-xs);
    }

    .pagination-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border: 1px solid var(--td-border-color);
      background: var(--td-bg-color-container);
      color: var(--td-text-color-primary);
      border-radius: var(--td-radius-default);
      cursor: pointer;
      transition: all 0.2s cubic-bezier(0.38, 0, 0.24, 1);
    }

    .pagination-btn:hover:not(:disabled) {
      background: var(--td-bg-color-container-hover);
      border-color: var(--td-brand-color);
      color: var(--td-brand-color);
    }

    .pagination-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .pagination-btn.active {
      background: var(--td-brand-color);
      border-color: var(--td-brand-color);
      color: var(--td-text-color-anti);
    }

    /* 表格空状态 */
    .table-empty {
      text-align: center;
      padding: var(--td-comp-size-xxxl);
      color: var(--td-text-color-secondary);
    }

    .table-empty-icon {
      font-size: 48px;
      margin-bottom: var(--td-comp-size-m);
      opacity: 0.5;
    }

    .table-empty-text {
      font-size: var(--td-font-size-body-medium);
      margin-bottom: var(--td-comp-size-m);
    }

    .table-empty-action {
      margin-top: var(--td-comp-size-m);
    }

    /* 徽章样式 - TDesign规范 */
    .badge {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: var(--td-comp-size-xxs) var(--td-comp-size-xs);
      border-radius: 12px;
      font-size: var(--td-font-size-body-small);
      font-weight: 500;
      line-height: 1;
      white-space: nowrap;
      border: 1px solid transparent;
    }

    .badge-primary {
      background: var(--td-brand-color-light);
      color: var(--td-brand-color);
      border-color: rgba(0, 82, 217, 0.2);
    }

    .badge-success {
      background: var(--td-success-color-light);
      color: var(--td-success-color);
      border-color: rgba(0, 168, 112, 0.2);
    }

    .badge-warning {
      background: var(--td-warning-color-light);
      color: var(--td-warning-color);
      border-color: rgba(237, 123, 47, 0.2);
    }

    .badge-danger {
      background: var(--td-error-color-light);
      color: var(--td-error-color);
      border-color: rgba(213, 73, 65, 0.2);
    }

    .badge-info {
      background: var(--td-info-color-light);
      color: var(--td-info-color);
      border-color: rgba(0, 82, 217, 0.2);
    }

    .badge-secondary {
      background: var(--td-bg-color-secondarycontainer);
      color: var(--td-text-color-secondary);
      border-color: var(--td-border-color);
    }

    /* 徽章尺寸 */
    .badge-sm {
      padding: 2px var(--td-comp-size-xxs);
      font-size: 11px;
      border-radius: 8px;
    }

    .badge-lg {
      padding: var(--td-comp-size-xs) var(--td-comp-size-s);
      font-size: var(--td-font-size-body-medium);
      border-radius: 16px;
    }

    /* 徽章变体 */
    .badge-outline {
      background: transparent;
    }

    .badge-outline.badge-primary {
      color: var(--td-brand-color);
      border-color: var(--td-brand-color);
    }

    .badge-outline.badge-success {
      color: var(--td-success-color);
      border-color: var(--td-success-color);
    }

    .badge-outline.badge-warning {
      color: var(--td-warning-color);
      border-color: var(--td-warning-color);
    }

    .badge-outline.badge-danger {
      color: var(--td-error-color);
      border-color: var(--td-error-color);
    }

    .badge-outline.badge-info {
      color: var(--td-info-color);
      border-color: var(--td-info-color);
    }

    .badge-outline.badge-secondary {
      color: var(--td-text-color-secondary);
      border-color: var(--td-border-color);
    }

    /* 状态徽章 */
    .badge-status {
      position: relative;
      padding-left: var(--td-comp-size-m);
    }

    .badge-status::before {
      content: '';
      position: absolute;
      left: var(--td-comp-size-xs);
      top: 50%;
      transform: translateY(-50%);
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background: currentColor;
    }

    /* 表单样式 - TDesign规范 */
    .form-group {
      margin-bottom: var(--td-comp-size-xl);
    }

    .form-group label {
      display: block;
      margin-bottom: var(--td-comp-size-xs);
      font-weight: 500;
      color: var(--td-text-color-primary);
      font-size: var(--td-font-size-body-medium);
      line-height: var(--td-line-height-body-medium);
    }

    .form-group label.required::after {
      content: '*';
      color: var(--td-error-color);
      margin-left: var(--td-comp-size-xxs);
    }

    .form-input,
    .form-select,
    .form-textarea {
      width: 100%;
      padding: var(--td-comp-size-s) var(--td-comp-size-m);
      border: 1px solid var(--td-border-color);
      border-radius: var(--td-radius-default);
      font-size: var(--td-font-size-body-medium);
      line-height: var(--td-line-height-body-medium);
      transition: all 0.2s cubic-bezier(0.38, 0, 0.24, 1);
      background: var(--td-bg-color-container);
      color: var(--td-text-color-primary);
    }

    .form-input:focus,
    .form-select:focus,
    .form-textarea:focus {
      outline: none;
      border-color: var(--td-border-color-focus);
      box-shadow: 0 0 0 3px var(--td-brand-color-focus);
    }

    .form-input:hover,
    .form-select:hover,
    .form-textarea:hover {
      border-color: var(--td-border-color-focus);
    }

    .form-input::placeholder,
    .form-textarea::placeholder {
      color: var(--td-text-color-placeholder);
    }

    .form-input:disabled,
    .form-select:disabled,
    .form-textarea:disabled {
      background: var(--td-bg-color-secondarycontainer);
      color: var(--td-text-color-disabled);
      cursor: not-allowed;
    }

    .form-textarea {
      resize: vertical;
      min-height: 80px;
    }

    .form-select {
      cursor: pointer;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
      background-position: right var(--td-comp-size-s) center;
      background-repeat: no-repeat;
      background-size: 16px 12px;
      padding-right: var(--td-comp-size-xxl);
    }

    /* 表单验证状态 */
    .form-input.is-valid,
    .form-select.is-valid,
    .form-textarea.is-valid {
      border-color: var(--td-success-color);
    }

    .form-input.is-valid:focus,
    .form-select.is-valid:focus,
    .form-textarea.is-valid:focus {
      box-shadow: 0 0 0 3px rgba(0, 168, 112, 0.1);
    }

    .form-input.is-invalid,
    .form-select.is-invalid,
    .form-textarea.is-invalid {
      border-color: var(--td-error-color);
    }

    .form-input.is-invalid:focus,
    .form-select.is-invalid:focus,
    .form-textarea.is-invalid:focus {
      box-shadow: 0 0 0 3px rgba(213, 73, 65, 0.1);
    }

    .form-feedback {
      margin-top: var(--td-comp-size-xs);
      font-size: var(--td-font-size-body-small);
      line-height: var(--td-line-height-body-small);
    }

    .form-feedback.is-valid {
      color: var(--td-success-color);
    }

    .form-feedback.is-invalid {
      color: var(--td-error-color);
    }

    /* 表单布局 */
    .form-row {
      display: flex;
      gap: var(--td-comp-size-m);
      margin-bottom: var(--td-comp-size-xl);
    }

    .form-col {
      flex: 1;
    }

    .form-col-2 {
      flex: 2;
    }

    .form-col-3 {
      flex: 3;
    }

    /* 复选框和单选框 */
    .form-check {
      display: flex;
      align-items: center;
      margin-bottom: var(--td-comp-size-s);
    }

    .form-check-input {
      margin-right: var(--td-comp-size-s);
      width: 16px;
      height: 16px;
      border: 1px solid var(--td-border-color);
      border-radius: 2px;
      cursor: pointer;
    }

    .form-check-input[type="radio"] {
      border-radius: 50%;
    }

    .form-check-input:checked {
      background: var(--td-brand-color);
      border-color: var(--td-brand-color);
    }

    .form-check-label {
      cursor: pointer;
      font-size: var(--td-font-size-body-medium);
      color: var(--td-text-color-primary);
    }

    /* 模态框样式 - TDesign规范 */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
      padding: var(--td-comp-size-xl);
      backdrop-filter: blur(4px);
    }

    .modal-content {
      background: var(--td-bg-color-container);
      border-radius: var(--td-radius-large);
      box-shadow: var(--td-shadow-3);
      max-width: 600px;
      width: 100%;
      max-height: 90vh;
      overflow-y: auto;
      animation: modalSlideIn 0.3s cubic-bezier(0.38, 0, 0.24, 1);
      border: 1px solid var(--td-border-color);
    }

    .modal-large {
      max-width: 900px;
    }

    .modal-xl {
      max-width: 1200px;
    }

    .modal-header {
      padding: var(--td-comp-size-xl) var(--td-comp-size-xl) 0 var(--td-comp-size-xl);
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid var(--td-border-color-light);
      margin-bottom: var(--td-comp-size-xl);
    }

    .modal-header h3 {
      font-size: var(--td-font-size-title-medium);
      font-weight: 600;
      color: var(--td-text-color-primary);
      margin: 0;
      line-height: var(--td-line-height-title-medium);
    }

    .modal-close {
      background: none;
      border: none;
      font-size: 24px;
      color: var(--td-text-color-placeholder);
      cursor: pointer;
      padding: var(--td-comp-size-xxs);
      border-radius: var(--td-radius-default);
      transition: all 0.2s cubic-bezier(0.38, 0, 0.24, 1);
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
    }

    .modal-close:hover {
      background: var(--td-bg-color-secondarycontainer);
      color: var(--td-text-color-primary);
    }

    .modal-body {
      padding: 0 var(--td-comp-size-xl) var(--td-comp-size-xl) var(--td-comp-size-xl);
    }

    .form-actions {
      padding: var(--td-comp-size-xl);
      display: flex;
      gap: var(--td-comp-size-s);
      justify-content: flex-end;
      border-top: 1px solid var(--td-border-color-light);
      background: var(--td-bg-color-secondarycontainer);
      margin: 0;
    }

    .form-actions .btn {
      min-width: 80px;
    }

    /* 模态框动画 */
    @keyframes modalSlideIn {
      from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
      }
      to {
        opacity: 1;
        transform: scale(1) translateY(0);
      }
    }

    @keyframes modalSlideOut {
      from {
        opacity: 1;
        transform: scale(1) translateY(0);
      }
      to {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
      }
    }

    /* 响应式模态框 */
    @media (max-width: 768px) {
      .modal-overlay {
        padding: var(--td-comp-size-m);
      }
      
      .modal-content {
        max-width: none;
        margin: var(--td-comp-size-m);
      }
      
      .modal-header,
      .modal-body,
      .form-actions {
        padding-left: var(--td-comp-size-m);
        padding-right: var(--td-comp-size-m);
      }
      
      .form-actions {
        flex-direction: column;
      }
      
      .form-actions .btn {
        width: 100%;
      }
    }

    /* 图表容器 */
    .chart-container {
      position: relative;
      height: 320px;
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--td-bg-color-secondarycontainer);
      border-radius: var(--td-radius-large);
      border: 1px solid var(--td-border-color-light);
      overflow: hidden;
      padding: var(--td-comp-size-m);
    }

    /* 图表画布样式 - 修复超出问题 */
    .chart-container canvas {
      position: relative;
      z-index: 2;
      max-width: 100% !important;
      max-height: 100% !important;
      width: 100% !important;
      height: auto !important;
      object-fit: contain;
    }

    /* 强制修复图表超出问题 */
    canvas {
      max-width: 100% !important;
      max-height: 100% !important;
      width: 100% !important;
      height: auto !important;
    }

    /* Chart.js 特定修复 */
    .chartjs-render-monitor {
      max-width: 100% !important;
      max-height: 100% !important;
      width: 100% !important;
      height: auto !important;
    }

    /* 搜索和筛选 */
    .search-container {
      display: flex;
      gap: 16px;
      margin-bottom: 20px;
      flex-wrap: wrap;
    }

    .filter-container {
      display: flex;
      gap: 12px;
      align-items: center;
      flex-wrap: wrap;
    }

    .filter-group {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .filter-group label {
      font-weight: 500;
      color: var(--td-text-color-primary);
      white-space: nowrap;
    }

    .filter-group select {
      padding: 8px 12px;
      border: 1px solid var(--td-border-color);
      border-radius: var(--td-radius-default);
      background: var(--td-bg-color-container);
      font-size: 14px;
    }

    /* 登录页面 */
    .login-container {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, var(--td-brand-color) 0%, var(--td-brand-color-hover) 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
    }

    .login-card {
      background: var(--td-bg-color-container);
      border-radius: var(--td-radius-large);
      padding: 48px;
      box-shadow: var(--td-shadow-3);
      width: 100%;
      max-width: 400px;
      text-align: center;
    }

    .login-header {
      margin-bottom: 32px;
    }

    .login-header h1 {
      font-size: 28px;
      font-weight: 700;
      color: var(--td-text-color-primary);
      margin: 0 0 8px 0;
    }

    .login-header p {
      color: var(--td-text-color-secondary);
      margin: 0;
    }

    .login-form {
      text-align: left;
    }

    .login-form .form-group {
      margin-bottom: 24px;
    }

    .login-form .btn {
      width: 100%;
      padding: 12px;
      font-size: 16px;
      margin-top: 8px;
    }

    /* 系统功能卡片样式 */
    .function-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap: 24px;
      margin-top: 20px;
    }

    .function-card {
      background: var(--td-bg-color-container);
      border: 1px solid var(--td-border-color);
      border-radius: var(--td-radius-large);
      padding: 24px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: left;
      display: flex;
      align-items: center;
      gap: 20px;
      box-shadow: var(--td-shadow-1);
    }

    .function-card:hover {
      border-color: var(--td-brand-color);
      box-shadow: var(--td-shadow-2);
      transform: translateY(-4px);
    }

    .function-icon {
      width: 56px;
      height: 56px;
      background: linear-gradient(135deg, var(--td-brand-color-light) 0%, #e3f2fd 100%);
      border-radius: var(--td-radius-medium);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28px;
      color: var(--td-brand-color);
      flex-shrink: 0;
      transition: all 0.3s ease;
    }

    .function-card:hover .function-icon {
      background: linear-gradient(135deg, var(--td-brand-color) 0%, var(--td-brand-color-hover) 100%);
      color: white;
      transform: scale(1.1);
    }

    .function-content h4 {
      margin: 0 0 8px 0;
      color: var(--td-text-color-primary);
      font-size: 18px;
      font-weight: 600;
    }

    .function-content p {
      margin: 0;
      color: var(--td-text-color-secondary);
      font-size: 14px;
      line-height: 1.5;
    }

    /* 报警通知样式 */
    .alarm-notification {
      position: fixed;
      top: 24px;
      right: 24px;
      width: 380px;
      background: var(--td-bg-color-container);
      border-radius: var(--td-radius-large);
      box-shadow: var(--td-shadow-3);
      z-index: 10000;
      border-left: 4px solid var(--td-error-color);
      animation: slideInRight 0.3s ease;
      overflow: hidden;
    }

    .alarm-notification.alarm-high {
      border-left-color: var(--td-error-color);
    }

    .alarm-notification.alarm-medium {
      border-left-color: var(--td-warning-color);
    }

    .alarm-notification.alarm-low {
      border-left-color: var(--td-success-color);
    }

    .alarm-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 20px;
      background: var(--td-bg-color-secondarycontainer);
      font-weight: 600;
      color: var(--td-text-color-primary);
    }

    .alarm-header button {
      background: none;
      border: none;
      font-size: 20px;
      cursor: pointer;
      color: var(--td-text-color-placeholder);
      padding: 4px;
      border-radius: var(--td-radius-default);
      transition: all 0.2s ease;
    }

    .alarm-header button:hover {
      background: var(--td-bg-color-tertiarycontainer);
      color: var(--td-text-color-primary);
    }

    .alarm-content {
      padding: 20px;
    }

    .alarm-device {
      font-weight: 600;
      color: var(--td-text-color-primary);
      margin-bottom: 8px;
      font-size: 16px;
    }

    .alarm-message {
      color: var(--td-error-color);
      margin-bottom: 8px;
      font-weight: 500;
    }

    .alarm-location {
      color: var(--td-text-color-secondary);
      font-size: 14px;
      margin-bottom: 8px;
    }

    .alarm-time {
      color: var(--td-text-color-placeholder);
      font-size: 12px;
    }

    .alarm-actions {
      padding: 16px 20px;
      display: flex;
      gap: 12px;
      border-top: 1px solid var(--td-border-color-light);
      background: var(--td-bg-color-secondarycontainer);
    }

    .alarm-actions button {
      flex: 1;
      padding: 10px 16px;
      border: none;
      border-radius: var(--td-radius-default);
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s ease;
    }

    .alarm-actions button:first-child {
      background: var(--td-brand-color);
      color: white;
    }

    .alarm-actions button:first-child:hover {
      background: var(--td-brand-color-hover);
      transform: translateY(-1px);
    }

    .alarm-actions button:last-child {
      background: var(--td-bg-color-container);
      color: var(--td-text-color-primary);
      border: 1px solid var(--td-border-color);
    }

    .alarm-actions button:last-child:hover {
      background: var(--td-bg-color-secondarycontainer);
      border-color: var(--td-brand-color);
      color: var(--td-brand-color);
    }

    /* 权限管理样式 */
    .permission-controls {
      margin-bottom: 24px;
      padding: 20px;
      background: var(--td-bg-color-secondarycontainer);
      border-radius: var(--td-radius-medium);
      border: 1px solid var(--td-border-color);
    }

    .permission-matrix {
      margin-bottom: 24px;
    }

    .permission-matrix h4 {
      margin-bottom: 16px;
      color: var(--td-text-color-primary);
      font-weight: 600;
    }

    .role-descriptions {
      margin-top: 24px;
    }

    .role-item {
      background: var(--td-bg-color-secondarycontainer);
      padding: 20px;
      border-radius: var(--td-radius-medium);
      margin-bottom: 12px;
      border: 1px solid var(--td-border-color);
    }

    .role-item h5 {
      margin: 0 0 8px 0;
      color: var(--td-text-color-primary);
      font-weight: 600;
    }

    .role-item p {
      margin: 0;
      color: var(--td-text-color-secondary);
      font-size: 14px;
      line-height: 1.5;
    }

    /* 固件升级样式 */
    .firmware-overview {
      margin-bottom: 32px;
    }

    .version-list {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .version-item {
      background: var(--td-bg-color-container);
      border: 1px solid var(--td-border-color);
      border-radius: var(--td-radius-large);
      padding: 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: var(--td-shadow-1);
      transition: all 0.3s ease;
    }

    .version-item:hover {
      box-shadow: var(--td-shadow-2);
      transform: translateY(-2px);
    }

    .version-item.latest {
      border-color: var(--td-success-color);
      background: linear-gradient(135deg, #f1f8e9 0%, #e8f5e8 100%);
    }

    .version-info h5 {
      margin: 0 0 8px 0;
      color: var(--td-text-color-primary);
      font-weight: 600;
      font-size: 16px;
    }

    .version-info p {
      margin: 0 0 12px 0;
      color: var(--td-text-color-secondary);
      line-height: 1.5;
    }

    .version-meta {
      display: flex;
      gap: 24px;
      font-size: 12px;
      color: var(--td-text-color-placeholder);
    }

    .version-actions {
      display: flex;
      gap: 12px;
    }

    .progress-bar {
      width: 120px;
      height: 8px;
      background: var(--td-bg-color-secondarycontainer);
      border-radius: 4px;
      overflow: hidden;
      margin-right: 12px;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, var(--td-brand-color) 0%, var(--td-brand-color-hover) 100%);
      transition: width 0.3s ease;
    }

    /* 系统状态样式 */
    .system-status {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
      gap: 16px;
    }

    .status-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      background: var(--td-bg-color-secondarycontainer);
      border-radius: var(--td-radius-medium);
      border: 1px solid var(--td-border-color);
    }

    .status-label {
      color: var(--td-text-color-secondary);
      font-size: 14px;
    }

    .status-value {
      font-weight: 600;
      color: var(--td-text-color-primary);
    }

    .status-normal {
      color: var(--td-success-color);
    }

    /* 设置页面样式 */
    .settings-section {
      margin-bottom: 32px;
      padding-bottom: 24px;
      border-bottom: 1px solid var(--td-border-color-light);
    }

    .settings-section:last-child {
      border-bottom: none;
    }

    .settings-section h4 {
      margin: 0 0 16px 0;
      color: var(--td-text-color-primary);
      font-weight: 600;
    }

    /* 备份管理样式 */
    .backup-section {
      margin-bottom: 32px;
      padding-bottom: 24px;
      border-bottom: 1px solid var(--td-border-color-light);
    }

    .backup-section:last-child {
      border-bottom: none;
    }

    .backup-section h4 {
      margin: 0 0 12px 0;
      color: var(--td-text-color-primary);
      font-weight: 600;
    }

    .backup-list {
      margin-top: 16px;
    }

    .backup-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      background: var(--td-bg-color-secondarycontainer);
      border-radius: var(--td-radius-medium);
      margin-bottom: 12px;
      border: 1px solid var(--td-border-color);
    }

    .backup-name {
      font-weight: 600;
      color: var(--td-text-color-primary);
      margin-bottom: 4px;
    }

    .backup-meta {
      font-size: 12px;
      color: var(--td-text-color-placeholder);
    }

    .backup-actions {
      display: flex;
      gap: 8px;
    }

    /* 日志样式 */
    .log-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
      gap: 16px;
      margin-top: 24px;
      padding: 20px;
      background: var(--td-bg-color-secondarycontainer);
      border-radius: var(--td-radius-medium);
      border: 1px solid var(--td-border-color);
    }

    .log-row.log-error {
      background: #fff5f5;
    }

    .log-row.log-warning {
      background: #fffbf0;
    }

    .log-row.log-info {
      background: #f0f7ff;
    }

    /* 动画效果 */
    @keyframes modalSlideIn {
      from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
      }
      to {
        opacity: 1;
        transform: scale(1) translateY(0);
      }
    }

    @keyframes modalSlideOut {
      from {
        opacity: 1;
        transform: scale(1) translateY(0);
      }
      to {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
      }
    }

    @keyframes slideInRight {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }

    /* 响应式设计 */
    @media (max-width: 1024px) {
      .sidebar {
        transform: translateX(-100%);
      }

      .sidebar.show {
        transform: translateX(0);
      }

      .main-content {
        margin-left: 0;
      }

      .menu-toggle {
        display: block;
      }
    }

    @media (max-width: 768px) {
      .stats-grid {
        grid-template-columns: 1fr;
      }

      .function-grid {
        grid-template-columns: 1fr;
      }

      .function-card {
        flex-direction: column;
        text-align: center;
      }

      .version-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
      }

      .version-actions {
        width: 100%;
        justify-content: center;
      }

      .alarm-notification {
        width: calc(100% - 48px);
        right: 24px;
        left: 24px;
      }

      .page-content {
        padding: 16px;
      }

      .modal-content {
        margin: 20px;
        max-width: calc(100% - 40px);
      }

      .profile-info {
        flex-direction: column;
        align-items: center;
        text-align: center;
      }
      
      .monitor-grid {
        grid-template-columns: 1fr;
      }
      
      .monitor-stats {
        grid-template-columns: 1fr;
      }
      
      .settings-section {
        margin-bottom: 16px;
      }
    }

    @media (max-width: 480px) {
      .login-card {
        margin: 20px;
        padding: 32px 24px;
      }

      .navbar-left {
        gap: 8px;
      }

      .user-name {
        display: none;
      }
    }

    /* TDesign 搜索框样式 */
    .td-search {
      position: relative;
      display: flex;
      align-items: center;
      max-width: 320px;
      width: 100%;
    }

    .td-search-input {
      width: 100%;
      padding: 12px 16px 12px 40px;
      border: 1px solid var(--td-border-color);
      border-radius: var(--td-radius-medium);
      font-size: 14px;
      background: var(--td-bg-color-container);
      transition: all 0.2s ease;
      color: var(--td-text-color-primary);
    }

    .td-search-input:focus {
      outline: none;
      border-color: var(--td-brand-color);
      box-shadow: 0 0 0 3px rgba(0, 82, 217, 0.1);
    }

    .td-search-input::placeholder {
      color: var(--td-text-color-placeholder);
    }

    .td-search-icon {
      position: absolute;
      left: 12px;
      color: var(--td-text-color-placeholder);
      font-size: 16px;
      pointer-events: none;
    }

    .td-search-clear {
      position: absolute;
      right: 12px;
      background: none;
      border: none;
      color: var(--td-text-color-placeholder);
      cursor: pointer;
      padding: 4px;
      border-radius: var(--td-radius-default);
      transition: all 0.2s ease;
      display: none;
    }

    .td-search-clear:hover {
      background: var(--td-bg-color-secondarycontainer);
      color: var(--td-text-color-primary);
    }

    .td-search-clear.show {
      display: block;
    }

    /* TDesign 下拉选择器样式 */
    .td-select {
      position: relative;
      display: inline-block;
      min-width: 120px;
    }

    .td-select select {
      width: 100%;
      padding: 12px 16px;
      border: 1px solid var(--td-border-color);
      border-radius: var(--td-radius-default);
      background: var(--td-bg-color-container);
      font-size: 14px;
      color: var(--td-text-color-primary);
      cursor: pointer;
      appearance: none;
      transition: all 0.2s ease;
    }

    .td-select select:focus {
      outline: none;
      border-color: var(--td-brand-color);
      box-shadow: 0 0 0 3px rgba(0, 82, 217, 0.1);
    }

    .td-select::after {
      content: '';
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
      width: 0;
      height: 0;
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;
      border-top: 4px solid var(--td-text-color-placeholder);
      pointer-events: none;
    }

    /* TDesign 表格样式 */
    .td-table-container {
      background: var(--td-bg-color-container);
      border-radius: var(--td-radius-large);
      overflow: hidden;
      border: 1px solid var(--td-border-color);
      box-shadow: var(--td-shadow-1);
    }

    .td-table {
      width: 100%;
      border-collapse: collapse;
      font-size: 14px;
    }

    .td-table th {
      background: var(--td-bg-color-secondarycontainer);
      padding: 16px 12px;
      text-align: left;
      font-weight: 600;
      color: var(--td-text-color-primary);
      border-bottom: 1px solid var(--td-border-color);
      font-size: 13px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .td-table td {
      padding: 16px 12px;
      border-bottom: 1px solid var(--td-border-color-light);
      color: var(--td-text-color-primary);
    }

    .td-table tr:hover {
      background: var(--td-bg-color-secondarycontainer);
    }

    .td-table tr:last-child td {
      border-bottom: none;
    }

    .td-table tbody tr {
      transition: all 0.2s ease;
    }

    /* 表格操作按钮组 */
    .td-table-actions {
      display: flex;
      gap: 8px;
      align-items: center;
    }

    .td-table-actions .btn {
      padding: 6px 12px;
      font-size: 12px;
      min-width: auto;
    }

    /* 表格分页 */
    .td-pagination {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 24px;
      background: var(--td-bg-color-secondarycontainer);
      border-top: 1px solid var(--td-border-color-light);
    }

    .td-pagination-info {
      color: var(--td-text-color-secondary);
      font-size: 14px;
    }

    .td-pagination-controls {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .td-pagination-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border: 1px solid var(--td-border-color);
      background: var(--td-bg-color-container);
      border-radius: var(--td-radius-default);
      cursor: pointer;
      transition: all 0.2s ease;
      color: var(--td-text-color-primary);
    }

    .td-pagination-btn:hover:not(:disabled) {
      border-color: var(--td-brand-color);
      color: var(--td-brand-color);
    }

    .td-pagination-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .td-pagination-btn.active {
      background: var(--td-brand-color);
      border-color: var(--td-brand-color);
      color: white;
    }

    /* 表格工具栏 */
    .td-table-toolbar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 24px;
      background: var(--td-bg-color-secondarycontainer);
      border-bottom: 1px solid var(--td-border-color-light);
      flex-wrap: wrap;
      gap: 16px;
    }

    .td-table-toolbar-left {
      display: flex;
      align-items: center;
      gap: 16px;
      flex-wrap: wrap;
    }

    .td-table-toolbar-right {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;
    }

    /* 空状态 */
    .td-empty {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 48px 24px;
      color: var(--td-text-color-secondary);
      text-align: center;
    }

    .td-empty-icon {
      font-size: 48px;
      color: var(--td-text-color-placeholder);
      margin-bottom: 16px;
    }

    .td-empty-title {
      font-size: 16px;
      font-weight: 500;
      color: var(--td-text-color-primary);
      margin-bottom: 8px;
    }

    .td-empty-description {
      font-size: 14px;
      color: var(--td-text-color-secondary);
      margin-bottom: 16px;
    }

    /* 加载状态 */
    .td-loading {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 48px 24px;
      color: var(--td-text-color-secondary);
    }

    .td-loading-spinner {
      width: 24px;
      height: 24px;
      border: 2px solid var(--td-border-color);
      border-top: 2px solid var(--td-brand-color);
      border-radius: 50%;
      animation: td-spin 1s linear infinite;
      margin-right: 12px;
    }

    @keyframes td-spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* 批量操作栏 */
    .td-batch-actions {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 24px;
      background: var(--td-brand-color-light);
      border-bottom: 1px solid var(--td-border-color-light);
      color: var(--td-brand-color);
      font-weight: 500;
    }

    .td-batch-actions .btn {
      padding: 6px 12px;
      font-size: 12px;
    }

    /* 表格行选择 */
    .td-table-checkbox {
      width: 16px;
      height: 16px;
      border: 1px solid var(--td-border-color);
      border-radius: 2px;
      background: var(--td-bg-color-container);
      cursor: pointer;
      transition: all 0.2s ease;
      position: relative;
    }

    .td-table-checkbox:checked {
      background: var(--td-brand-color);
      border-color: var(--td-brand-color);
    }

    .td-table-checkbox:checked::after {
      content: '';
      position: absolute;
      left: 2px;
      top: 1px;
      width: 10px;
      height: 6px;
      border: 2px solid white;
      border-top: none;
      border-right: none;
      transform: rotate(-45deg);
    }

    /* 搜索和筛选容器 */
    .td-search-container {
      display: flex;
      gap: 16px;
      margin-bottom: 20px;
      flex-wrap: wrap;
      align-items: center;
    }

    .td-filter-container {
      display: flex;
      gap: 12px;
      align-items: center;
      flex-wrap: wrap;
    }

    .td-filter-group {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .td-filter-group label {
      font-weight: 500;
      color: var(--td-text-color-primary);
      white-space: nowrap;
      font-size: 14px;
    }

    /* 响应式表格 */
    @media (max-width: 768px) {
      .td-table-container {
        overflow-x: auto;
      }
      
      .td-table {
        min-width: 600px;
      }
      
      .td-table-toolbar {
        flex-direction: column;
        align-items: stretch;
      }
      
      .td-table-toolbar-left,
      .td-table-toolbar-right {
        justify-content: center;
      }
      
      .td-pagination {
        flex-direction: column;
        gap: 12px;
        text-align: center;
      }

      .td-search-container {
        flex-direction: column;
        align-items: stretch;
      }

      .td-filter-container {
        justify-content: center;
      }
    }

    /* 修复Chart.js图表无限放大问题 */
    .chart-card {
      height: 320px;
      min-height: 240px;
      max-height: 400px;
      position: relative;
    }
    .chart-card canvas {
      width: 100% !important;
      height: 100% !important;
      display: block;
    }

    /* 个人资料样式 */
    .profile-info {
      display: flex;
      gap: 20px;
      align-items: flex-start;
    }
    
    .profile-avatar {
      flex-shrink: 0;
    }
    
    .avatar-circle {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: var(--td-brand-color);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32px;
      font-weight: bold;
    }
    
    .profile-details {
      flex: 1;
    }
    
    .profile-item {
      margin-bottom: 12px;
      display: flex;
      align-items: center;
    }
    
    .profile-item label {
      font-weight: 500;
      width: 80px;
      color: var(--td-text-color-secondary);
    }
    
    .profile-item span {
      color: var(--td-text-color-primary);
    }

    /* 系统设置样式 */
    .settings-container {
      max-height: 500px;
      overflow-y: auto;
    }
    
    .settings-section {
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid var(--td-border-color-light);
    }
    
    .settings-section:last-child {
      border-bottom: none;
    }
    
    .settings-section h4 {
      margin-bottom: 16px;
      color: var(--td-text-color-primary);
      font-size: 16px;
      font-weight: 600;
    }

    /* 高级搜索样式 */
    .export-options {
      max-height: 400px;
      overflow-y: auto;
    }
    
    .export-options h4 {
      margin: 16px 0 12px 0;
      color: var(--td-text-color-primary);
      font-size: 14px;
      font-weight: 600;
    }
    
    .export-options h4:first-child {
      margin-top: 0;
    }

    /* 系统监控样式 */
    .system-monitor {
      max-width: 600px;
    }
    
    .monitor-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;
      margin-bottom: 24px;
    }
    
    .monitor-item {
      background: var(--td-bg-color-secondarycontainer);
      padding: 16px;
      border-radius: var(--td-radius-default);
      border: 1px solid var(--td-border-color-light);
    }
    
    .monitor-label {
      font-size: 14px;
      color: var(--td-text-color-secondary);
      margin-bottom: 8px;
    }
    
    .monitor-value {
      font-size: 24px;
      font-weight: 600;
      color: var(--td-brand-color);
      margin-bottom: 12px;
    }
    
    .monitor-bar {
      height: 8px;
      background: var(--td-bg-color-tertiarycontainer);
      border-radius: 4px;
      overflow: hidden;
    }
    
    .monitor-bar-fill {
      height: 100%;
      background: linear-gradient(90deg, var(--td-brand-color), var(--td-brand-color-hover));
      transition: width 0.3s ease;
    }
    
    .monitor-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
    }
    
    .stat-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background: var(--td-bg-color-secondarycontainer);
      border-radius: var(--td-radius-default);
      border: 1px solid var(--td-border-color-light);
    }
    
    .stat-label {
      color: var(--td-text-color-secondary);
      font-size: 14px;
    }
    
    .stat-value {
      color: var(--td-text-color-primary);
      font-weight: 500;
    }

    /* 深色主题 */
    .dark-theme {
      background: #1a1a1a;
      color: #ffffff;
    }
    
    .dark-theme .chart-card {
      background: #2a2a2a;
      border-color: #404040;
    }
    
    .dark-theme .stat-card {
      background: #2a2a2a;
      border-color: #404040;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .profile-info {
        flex-direction: column;
        align-items: center;
        text-align: center;
      }
      
      .monitor-grid {
        grid-template-columns: 1fr;
      }
      
      .monitor-stats {
        grid-template-columns: 1fr;
      }
      
      .settings-section {
        margin-bottom: 16px;
      }
    }

    /* 设备详情样式 */
    .device-detail {
      max-height: 500px;
      overflow-y: auto;
    }
    
    .detail-section {
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid var(--td-border-color-light);
    }
    
    .detail-section:last-child {
      border-bottom: none;
    }
    
    .detail-section h4 {
      margin-bottom: 16px;
      color: var(--td-text-color-primary);
      font-size: 16px;
      font-weight: 600;
    }
    
    .detail-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 12px;
    }
    
    .detail-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background: var(--td-bg-color-secondarycontainer);
      border-radius: var(--td-radius-default);
      border: 1px solid var(--td-border-color-light);
    }
    
    .detail-item label {
      font-weight: 500;
      color: var(--td-text-color-secondary);
    }
    
    .detail-item span {
      color: var(--td-text-color-primary);
    }
    
    .maintenance-history {
      max-height: 200px;
      overflow-y: auto;
    }
    
    .history-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      margin-bottom: 8px;
      background: var(--td-bg-color-secondarycontainer);
      border-radius: var(--td-radius-default);
      border: 1px solid var(--td-border-color-light);
    }
    
    .history-date {
      color: var(--td-text-color-secondary);
      font-size: 14px;
    }
    
    .history-action {
      color: var(--td-text-color-primary);
      font-weight: 500;
    }
    
    .history-status {
      color: var(--td-success-color);
      font-size: 14px;
    }

    /* 系统健康检查样式 */
    .health-check {
      max-width: 600px;
    }
    
    .health-status {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 16px;
      margin-bottom: 20px;
      border-radius: var(--td-radius-default);
      font-weight: 600;
    }
    
    .health-status.healthy {
      background: var(--td-success-color-light);
      color: var(--td-success-color);
      border: 1px solid var(--td-success-color);
    }
    
    .health-status.warning {
      background: var(--td-warning-color-light);
      color: var(--td-warning-color);
      border: 1px solid var(--td-warning-color);
    }
    
    .health-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
      margin-bottom: 24px;
    }
    
    .health-item {
      padding: 16px;
      border-radius: var(--td-radius-default);
      border: 1px solid var(--td-border-color-light);
      text-align: center;
    }
    
    .health-item.healthy {
      background: var(--td-success-color-light);
      border-color: var(--td-success-color);
    }
    
    .health-item.error {
      background: var(--td-error-color-light);
      border-color: var(--td-error-color);
    }
    
    .health-label {
      font-size: 14px;
      color: var(--td-text-color-secondary);
      margin-bottom: 8px;
    }
    
    .health-value {
      font-size: 18px;
      font-weight: 600;
    }
    
    .health-item.healthy .health-value {
      color: var(--td-success-color);
    }
    
    .health-item.error .health-value {
      color: var(--td-error-color);
    }
    
    .health-recommendations {
      background: var(--td-bg-color-secondarycontainer);
      padding: 16px;
      border-radius: var(--td-radius-default);
      border: 1px solid var(--td-border-color-light);
    }
    
    .health-recommendations h4 {
      margin-bottom: 12px;
      color: var(--td-text-color-primary);
      font-size: 16px;
      font-weight: 600;
    }
    
    .health-recommendations ul {
      margin: 0;
      padding-left: 20px;
    }
    
    .health-recommendations li {
      margin-bottom: 8px;
      color: var(--td-text-color-secondary);
    }

    /* 数据备份管理样式 */
    .backup-management {
      max-height: 500px;
      overflow-y: auto;
    }
    
    .backup-actions {
      display: flex;
      gap: 12px;
      margin-bottom: 20px;
      flex-wrap: wrap;
    }
    
    .backup-list h4 {
      margin-bottom: 16px;
      color: var(--td-text-color-primary);
      font-size: 16px;
      font-weight: 600;
    }
    
    .backup-list .table {
      width: 100%;
      border-collapse: collapse;
    }
    
    .backup-list .table th,
    .backup-list .table td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid var(--td-border-color-light);
    }
    
    .backup-list .table th {
      background: var(--td-bg-color-secondarycontainer);
      font-weight: 600;
      color: var(--td-text-color-primary);
    }
    
    .backup-list .table td {
      color: var(--td-text-color-secondary);
    }

    /* 历史图表样式 */
    .history-chart {
      margin-bottom: 20px;
      height: 200px;
    }
    
    .history-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 12px;
    }

    /* 批量操作样式 */
    .batch-actions {
      display: flex;
      gap: 8px;
      margin-bottom: 16px;
      flex-wrap: wrap;
    }
    
    .batch-actions .btn {
      font-size: 12px;
      padding: 6px 12px;
    }

    /* 增强的表格样式 */
    .table-container {
      overflow-x: auto;
      border-radius: var(--td-radius-default);
      border: 1px solid var(--td-border-color-light);
    }
    
    .table {
      width: 100%;
      border-collapse: collapse;
      background: var(--td-bg-color-container);
    }
    
    .table th {
      background: var(--td-bg-color-secondarycontainer);
      padding: 12px 16px;
      text-align: left;
      font-weight: 600;
      color: var(--td-text-color-primary);
      border-bottom: 1px solid var(--td-border-color-light);
    }
    
    .table td {
      padding: 12px 16px;
      border-bottom: 1px solid var(--td-border-color-light);
      color: var(--td-text-color-secondary);
    }
    
    .table tbody tr:hover {
      background: var(--td-bg-color-secondarycontainer);
    }
    
    .table tbody tr:last-child td {
      border-bottom: none;
    }

    /* 状态徽章增强 */
    .status-badge {
      display: inline-flex;
      align-items: center;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
      text-transform: uppercase;
    }
    
    .status-badge.status-online {
      background: var(--td-success-color-light);
      color: var(--td-success-color);
    }
    
    .status-badge.status-offline {
      background: var(--td-warning-color-light);
      color: var(--td-warning-color);
    }
    
    .status-badge.status-error {
      background: var(--td-error-color-light);
      color: var(--td-error-color);
    }
    
    .status-badge.status-maintenance {
      background: var(--td-info-color-light);
      color: var(--td-info-color);
    }

    /* 按钮增强 */
    .btn-small {
      padding: 4px 8px;
      font-size: 12px;
      border-radius: var(--td-radius-small);
    }
    
    .btn-group {
      display: flex;
      gap: 4px;
    }
    
    .btn-group .btn {
      border-radius: 0;
    }
    
    .btn-group .btn:first-child {
      border-top-left-radius: var(--td-radius-default);
      border-bottom-left-radius: var(--td-radius-default);
    }
    
    .btn-group .btn:last-child {
      border-top-right-radius: var(--td-radius-default);
      border-bottom-right-radius: var(--td-radius-default);
    }

    /* 响应式增强 */
    @media (max-width: 768px) {
      .detail-grid {
        grid-template-columns: 1fr;
      }
      
      .health-grid {
        grid-template-columns: 1fr;
      }
      
      .backup-actions {
        flex-direction: column;
      }
      
      .batch-actions {
        flex-direction: column;
      }
      
      .btn-group {
        flex-direction: column;
      }
      
      .btn-group .btn {
        border-radius: var(--td-radius-default);
      }
      
      .table-container {
        font-size: 14px;
      }
      
      .table th,
      .table td {
        padding: 8px 12px;
      }
    }

    /* 系统工具栏样式 */
    .system-toolbar {
      margin-bottom: 24px;
      padding: 20px;
      background: var(--td-bg-color-secondarycontainer);
      border-radius: var(--td-radius-default);
      border: 1px solid var(--td-border-color-light);
    }
    
    .toolbar-section {
      margin-bottom: 20px;
    }
    
    .toolbar-section:last-child {
      margin-bottom: 0;
    }
    
    .toolbar-section h3 {
      margin-bottom: 12px;
      color: var(--td-text-color-primary);
      font-size: 16px;
      font-weight: 600;
    }
    
    .toolbar-section .btn-group {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }
    
    .toolbar-section .btn {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 14px;
      padding: 8px 16px;
    }

    /* 表格头部样式 */
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid var(--td-border-color-light);
    }
    
    .table-header h3 {
      margin: 0;
      color: var(--td-text-color-primary);
      font-size: 18px;
      font-weight: 600;
    }
    
    .table-actions {
      display: flex;
      gap: 8px;
      align-items: center;
      flex-wrap: wrap;
    }
    
    .table-actions select {
      padding: 6px 12px;
      border: 1px solid var(--td-border-color-light);
      border-radius: var(--td-radius-default);
      background: var(--td-bg-color-container);
      color: var(--td-text-color-primary);
      font-size: 14px;
    }

    /* 日志详情样式 */
    .log-detail {
      max-height: 400px;
      overflow-y: auto;
    }
    
    .log-detail .detail-item {
      margin-bottom: 16px;
      padding: 12px;
      background: var(--td-bg-color-secondarycontainer);
      border-radius: var(--td-radius-default);
      border: 1px solid var(--td-border-color-light);
    }
    
    .log-detail .detail-item label {
      display: block;
      font-weight: 600;
      color: var(--td-text-color-secondary);
      margin-bottom: 4px;
      font-size: 14px;
    }
    
    .log-detail .detail-item span {
      color: var(--td-text-color-primary);
      font-size: 14px;
    }
    
    .log-details {
      background: var(--td-bg-color-tertiarycontainer);
      padding: 12px;
      border-radius: var(--td-radius-default);
      font-family: 'Courier New', monospace;
      font-size: 12px;
      color: var(--td-text-color-primary);
      white-space: pre-wrap;
      max-height: 200px;
      overflow-y: auto;
    }

    /* 日志级别样式 */
    .log-level-error {
      background: var(--td-error-color-light);
    }
    
    .log-level-warning {
      background: var(--td-warning-color-light);
    }
    
    .log-level-info {
      background: var(--td-info-color-light);
    }
    
    .status-badge.status-warning {
      background: var(--td-warning-color-light);
      color: var(--td-warning-color);
    }
    
    .status-badge.status-info {
      background: var(--td-info-color-light);
      color: var(--td-info-color);
    }

    /* 复选框样式 */
    input[type="checkbox"] {
      width: 16px;
      height: 16px;
      cursor: pointer;
    }
    
    .user-checkbox {
      margin: 0;
    }

    /* 响应式增强 */
    @media (max-width: 768px) {
      .system-toolbar {
        padding: 16px;
      }
      
      .toolbar-section .btn-group {
        flex-direction: column;
      }
      
      .toolbar-section .btn {
        justify-content: center;
      }
      
      .table-header {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
      }
      
      .table-actions {
        justify-content: center;
      }
      
      .log-detail .detail-item {
        padding: 8px;
      }
    }

    /* TDesign标准组件样式 */
    
    /* 卡片组件 */
    .t-card {
      background: var(--td-bg-color-container);
      border-radius: var(--td-radius-large);
      border: 1px solid var(--td-border-color);
      box-shadow: var(--td-shadow-1);
      overflow: hidden;
      transition: all 0.3s cubic-bezier(0.38, 0, 0.24, 1);
    }

    .t-card:hover {
      box-shadow: var(--td-shadow-2);
      transform: translateY(-2px);
    }

    .t-card-header {
      padding: var(--td-comp-size-xl);
      border-bottom: 1px solid var(--td-border-color-light);
      background: var(--td-bg-color-secondarycontainer);
    }

    .t-card-header h4 {
      margin: 0;
      font-size: var(--td-font-size-title-small);
      font-weight: 600;
      color: var(--td-text-color-primary);
    }

    .t-card-body {
      padding: var(--td-comp-size-xl);
    }

    .t-card-footer {
      padding: var(--td-comp-size-xl);
      border-top: 1px solid var(--td-border-color-light);
      background: var(--td-bg-color-secondarycontainer);
    }

    /* 加载状态 */
    .t-loading {
      position: relative;
      pointer-events: none;
    }

    .t-loading::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }

    .t-loading::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 32px;
      height: 32px;
      border: 3px solid var(--td-border-color);
      border-top-color: var(--td-brand-color);
      border-radius: 50%;
      animation: t-spin 1s linear infinite;
      z-index: 1001;
    }

    @keyframes t-spin {
      to {
        transform: translate(-50%, -50%) rotate(360deg);
      }
    }

    /* 空状态 */
    .t-empty {
      text-align: center;
      padding: var(--td-comp-size-xxxl);
      color: var(--td-text-color-secondary);
    }

    .t-empty-icon {
      font-size: 64px;
      margin-bottom: var(--td-comp-size-xl);
      opacity: 0.3;
    }

    .t-empty-title {
      font-size: var(--td-font-size-title-small);
      font-weight: 600;
      margin-bottom: var(--td-comp-size-s);
      color: var(--td-text-color-primary);
    }

    .t-empty-description {
      font-size: var(--td-font-size-body-medium);
      margin-bottom: var(--td-comp-size-xl);
      line-height: var(--td-line-height-body-medium);
    }

    .t-empty-action {
      margin-top: var(--td-comp-size-xl);
    }

    /* 分割线 */
    .t-divider {
      margin: var(--td-comp-size-xl) 0;
      border: none;
      border-top: 1px solid var(--td-border-color-light);
      position: relative;
    }

    .t-divider-text {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: var(--td-bg-color-page);
      padding: 0 var(--td-comp-size-m);
      color: var(--td-text-color-secondary);
      font-size: var(--td-font-size-body-small);
    }

    /* 标签页 */
    .t-tabs {
      border-bottom: 1px solid var(--td-border-color);
      margin-bottom: var(--td-comp-size-xl);
    }

    .t-tab-list {
      display: flex;
      list-style: none;
      margin: 0;
      padding: 0;
    }

    .t-tab-item {
      margin-right: var(--td-comp-size-xl);
    }

    .t-tab-button {
      background: none;
      border: none;
      padding: var(--td-comp-size-m) 0;
      color: var(--td-text-color-secondary);
      font-size: var(--td-font-size-body-medium);
      font-weight: 500;
      cursor: pointer;
      border-bottom: 2px solid transparent;
      transition: all 0.2s cubic-bezier(0.38, 0, 0.24, 1);
    }

    .t-tab-button:hover {
      color: var(--td-brand-color);
    }

    .t-tab-button.active {
      color: var(--td-brand-color);
      border-bottom-color: var(--td-brand-color);
    }

    /* 步骤条 */
    .t-steps {
      display: flex;
      align-items: center;
      margin-bottom: var(--td-comp-size-xl);
    }

    .t-step {
      display: flex;
      align-items: center;
      flex: 1;
    }

    .t-step-icon {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: var(--td-bg-color-secondarycontainer);
      border: 2px solid var(--td-border-color);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: var(--td-font-size-body-small);
      font-weight: 600;
      color: var(--td-text-color-secondary);
      margin-right: var(--td-comp-size-s);
    }

    .t-step.active .t-step-icon {
      background: var(--td-brand-color);
      border-color: var(--td-brand-color);
      color: var(--td-text-color-anti);
    }

    .t-step.completed .t-step-icon {
      background: var(--td-success-color);
      border-color: var(--td-success-color);
      color: var(--td-text-color-anti);
    }

    .t-step-title {
      font-size: var(--td-font-size-body-medium);
      color: var(--td-text-color-primary);
      font-weight: 500;
    }

    .t-step.active .t-step-title {
      color: var(--td-brand-color);
    }

    .t-step-line {
      flex: 1;
      height: 2px;
      background: var(--td-border-color);
      margin: 0 var(--td-comp-size-s);
    }

    .t-step.completed .t-step-line {
      background: var(--td-success-color);
    }

    /* 进度条 */
    .t-progress {
      width: 100%;
      margin-bottom: var(--td-comp-size-m);
    }

    .t-progress-bar {
      width: 100%;
      height: 8px;
      background: var(--td-bg-color-secondarycontainer);
      border-radius: 4px;
      overflow: hidden;
    }

    .t-progress-fill {
      height: 100%;
      background: linear-gradient(90deg, var(--td-brand-color) 0%, var(--td-brand-color-hover) 100%);
      transition: width 0.3s cubic-bezier(0.38, 0, 0.24, 1);
    }

    .t-progress-text {
      font-size: var(--td-font-size-body-small);
      color: var(--td-text-color-secondary);
      margin-top: var(--td-comp-size-xxs);
    }

    /* 标签 */
    .t-tag {
      display: inline-flex;
      align-items: center;
      padding: var(--td-comp-size-xxs) var(--td-comp-size-xs);
      border-radius: var(--td-radius-default);
      font-size: var(--td-font-size-body-small);
      font-weight: 500;
      line-height: 1;
      border: 1px solid transparent;
    }

    .t-tag-primary {
      background: var(--td-brand-color-light);
      color: var(--td-brand-color);
      border-color: rgba(0, 82, 217, 0.2);
    }

    .t-tag-success {
      background: var(--td-success-color-light);
      color: var(--td-success-color);
      border-color: rgba(0, 168, 112, 0.2);
    }

    .t-tag-warning {
      background: var(--td-warning-color-light);
      color: var(--td-warning-color);
      border-color: rgba(237, 123, 47, 0.2);
    }

    .t-tag-error {
      background: var(--td-error-color-light);
      color: var(--td-error-color);
      border-color: rgba(213, 73, 65, 0.2);
    }

    /* 数据概览页面 - 现代化TDesign样式 */
    .dashboard-container {
      padding: var(--td-comp-size-xl);
      background: linear-gradient(135deg, var(--td-bg-color-page) 0%, rgba(0, 82, 217, 0.02) 100%);
      min-height: calc(100vh - 64px);
    }

    /* 欢迎横幅 */
    .welcome-banner {
      background: linear-gradient(135deg, var(--td-brand-color) 0%, var(--td-brand-color-hover) 100%);
      border-radius: var(--td-radius-extra-large);
      padding: var(--td-comp-size-xxl);
      margin-bottom: var(--td-comp-size-xxl);
      color: var(--td-text-color-anti);
      position: relative;
      overflow: hidden;
      box-shadow: var(--td-shadow-2);
    }

    .welcome-banner::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
      animation: float 6s ease-in-out infinite;
    }

    .welcome-banner h1 {
      font-size: var(--td-font-size-headline-medium);
      font-weight: 700;
      margin: 0 0 var(--td-comp-size-s) 0;
      position: relative;
      z-index: 1;
    }

    .welcome-banner p {
      font-size: var(--td-font-size-body-large);
      margin: 0;
      opacity: 0.9;
      position: relative;
      z-index: 1;
    }

    .welcome-stats {
      display: flex;
      gap: var(--td-comp-size-xl);
      margin-top: var(--td-comp-size-xl);
      position: relative;
      z-index: 1;
    }

    .welcome-stat {
      text-align: center;
      flex: 1;
    }

    .welcome-stat-value {
      font-size: var(--td-font-size-headline-small);
      font-weight: 700;
      display: block;
      margin-bottom: var(--td-comp-size-xxs);
    }

    .welcome-stat-label {
      font-size: var(--td-font-size-body-small);
      opacity: 0.8;
    }

    @keyframes float {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      50% { transform: translateY(-20px) rotate(180deg); }
    }

    /* 统计卡片网格 - 现代化设计 */
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: var(--td-comp-size-xl);
      margin-bottom: var(--td-comp-size-xxl);
    }

    .stat-card {
      background: var(--td-bg-color-container);
      border-radius: var(--td-radius-extra-large);
      padding: var(--td-comp-size-xxl);
      box-shadow: var(--td-shadow-1);
      border: 1px solid var(--td-border-color);
      transition: all 0.4s cubic-bezier(0.38, 0, 0.24, 1);
      position: relative;
      overflow: hidden;
      cursor: pointer;
    }

    .stat-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, var(--td-brand-color) 0%, var(--td-brand-color-hover) 100%);
      transform: scaleX(0);
      transition: transform 0.3s cubic-bezier(0.38, 0, 0.24, 1);
    }

    .stat-card:hover {
      transform: translateY(-8px);
      box-shadow: var(--td-shadow-3);
    }

    .stat-card:hover::before {
      transform: scaleX(1);
    }

    .stat-card.revenue::before {
      background: linear-gradient(90deg, var(--td-success-color) 0%, var(--td-success-color-hover) 100%);
    }

    .stat-card.orders::before {
      background: linear-gradient(90deg, var(--td-brand-color) 0%, var(--td-brand-color-hover) 100%);
    }

    .stat-card.devices::before {
      background: linear-gradient(90deg, var(--td-warning-color) 0%, var(--td-warning-color-hover) 100%);
    }

    .stat-card.users::before {
      background: linear-gradient(90deg, var(--td-info-color) 0%, var(--td-info-color-hover) 100%);
    }

    .stat-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--td-comp-size-l);
    }

    .stat-icon {
      width: 64px;
      height: 64px;
      border-radius: var(--td-radius-large);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28px;
      position: relative;
      overflow: hidden;
    }

    .stat-icon::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
      border-radius: inherit;
    }

    .stat-icon.revenue {
      background: linear-gradient(135deg, var(--td-success-color) 0%, var(--td-success-color-hover) 100%);
      color: var(--td-text-color-anti);
    }

    .stat-icon.orders {
      background: linear-gradient(135deg, var(--td-brand-color) 0%, var(--td-brand-color-hover) 100%);
      color: var(--td-text-color-anti);
    }

    .stat-icon.devices {
      background: linear-gradient(135deg, var(--td-warning-color) 0%, var(--td-warning-color-hover) 100%);
      color: var(--td-text-color-anti);
    }

    .stat-icon.users {
      background: linear-gradient(135deg, var(--td-info-color) 0%, var(--td-info-color-hover) 100%);
      color: var(--td-text-color-anti);
    }

    .stat-trend {
      display: flex;
      align-items: center;
      gap: var(--td-comp-size-xxs);
      font-size: var(--td-font-size-body-small);
      padding: var(--td-comp-size-xxs) var(--td-comp-size-xs);
      border-radius: var(--td-radius-default);
      background: var(--td-success-color-light);
      color: var(--td-success-color);
    }

    .stat-trend.up {
      background: var(--td-success-color-light);
      color: var(--td-success-color);
    }

    .stat-trend.down {
      background: var(--td-error-color-light);
      color: var(--td-error-color);
    }

    .stat-content {
      flex: 1;
    }

    .stat-value {
      font-size: var(--td-font-size-headline-medium);
      font-weight: 700;
      color: var(--td-text-color-primary);
      margin: 0 0 var(--td-comp-size-xxs) 0;
      line-height: 1.2;
    }

    .stat-label {
      font-size: var(--td-font-size-body-medium);
      color: var(--td-text-color-secondary);
      margin: 0 0 var(--td-comp-size-s) 0;
      font-weight: 500;
    }

    .stat-subtitle {
      font-size: var(--td-font-size-body-small);
      color: var(--td-text-color-placeholder);
      margin: 0;
    }

    /* 图表网格 */
    .charts-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(480px, 1fr));
      gap: var(--td-comp-size-xxl);
      margin-bottom: var(--td-comp-size-xxl);
    }

    .chart-card {
      background: var(--td-bg-color-container);
      border-radius: var(--td-radius-extra-large);
      padding: var(--td-comp-size-xxl);
      box-shadow: var(--td-shadow-1);
      border: 1px solid var(--td-border-color);
      transition: all 0.3s cubic-bezier(0.38, 0, 0.24, 1);
      min-height: 420px;
      display: flex;
      flex-direction: column;
    }

    .chart-card:hover {
      box-shadow: var(--td-shadow-2);
      transform: translateY(-2px);
    }

    .chart-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--td-comp-size-xl);
      flex-shrink: 0;
    }

    .chart-title {
      font-size: var(--td-font-size-title-medium);
      font-weight: 600;
      color: var(--td-text-color-primary);
      margin: 0;
      display: flex;
      align-items: center;
      gap: var(--td-comp-size-s);
    }

    .chart-title::before {
      content: '';
      width: 3px;
      height: 18px;
      background: linear-gradient(135deg, var(--td-brand-color) 0%, var(--td-brand-color-hover) 100%);
      border-radius: 2px;
    }

    .chart-actions {
      display: flex;
      gap: var(--td-comp-size-xs);
    }

    .chart-action-btn {
      width: 36px;
      height: 36px;
      border: 1px solid var(--td-border-color);
      background: var(--td-bg-color-container);
      border-radius: var(--td-radius-default);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s cubic-bezier(0.38, 0, 0.24, 1);
      color: var(--td-text-color-secondary);
      font-size: 16px;
    }

    .chart-action-btn:hover {
      background: var(--td-brand-color);
      border-color: var(--td-brand-color);
      color: var(--td-text-color-anti);
      transform: scale(1.05);
    }

    .chart-container {
      position: relative;
      height: 320px;
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--td-bg-color-secondarycontainer);
      border-radius: var(--td-radius-large);
      border: 1px solid var(--td-border-color-light);
      overflow: hidden;
    }

    /* 快速操作区域 */
    .quick-actions {
      background: var(--td-bg-color-container);
      border-radius: var(--td-radius-extra-large);
      padding: var(--td-comp-size-xxl);
      box-shadow: var(--td-shadow-1);
      border: 1px solid var(--td-border-color);
    }

    .quick-actions h3 {
      font-size: var(--td-font-size-title-medium);
      font-weight: 600;
      color: var(--td-text-color-primary);
      margin: 0 0 var(--td-comp-size-xl) 0;
      display: flex;
      align-items: center;
      gap: var(--td-comp-size-s);
    }

    .quick-actions h3::before {
      content: '';
      width: 4px;
      height: 24px;
      background: linear-gradient(135deg, var(--td-brand-color) 0%, var(--td-brand-color-hover) 100%);
      border-radius: 2px;
    }

    .action-buttons {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--td-comp-size-m);
    }

    .action-btn {
      display: flex;
      align-items: center;
      gap: var(--td-comp-size-s);
      padding: var(--td-comp-size-l) var(--td-comp-size-xl);
      background: var(--td-bg-color-secondarycontainer);
      border: 1px solid var(--td-border-color);
      border-radius: var(--td-radius-large);
      color: var(--td-text-color-primary);
      text-decoration: none;
      font-weight: 500;
      transition: all 0.3s cubic-bezier(0.38, 0, 0.24, 1);
      cursor: pointer;
    }

    .action-btn:hover {
      background: var(--td-brand-color);
      border-color: var(--td-brand-color);
      color: var(--td-text-color-anti);
      transform: translateY(-2px);
      box-shadow: var(--td-shadow-2);
    }

    .action-btn span {
      font-size: 24px;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .dashboard-container {
        padding: var(--td-comp-size-m);
      }
      
      .welcome-banner {
        padding: var(--td-comp-size-xl);
      }
      
      .welcome-banner h1 {
        font-size: var(--td-font-size-title-large);
      }
      
      .welcome-stats {
        flex-direction: column;
        gap: var(--td-comp-size-m);
      }
      
      .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--td-comp-size-m);
      }
      
      .charts-grid {
        grid-template-columns: 1fr;
        gap: var(--td-comp-size-m);
      }
      
      .action-buttons {
        grid-template-columns: 1fr;
      }
    }

    /* 统计详情模态框样式 */
    .stat-detail-content {
      padding: var(--td-comp-size-m) 0;
    }

    .stat-detail-content .detail-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--td-comp-size-m) 0;
      border-bottom: 1px solid var(--td-border-color-light);
    }

    .stat-detail-content .detail-item:last-child {
      border-bottom: none;
    }

    .stat-detail-content .detail-label {
      font-size: var(--td-font-size-body-medium);
      color: var(--td-text-color-secondary);
      font-weight: 500;
    }

    .stat-detail-content .detail-value {
      font-size: var(--td-font-size-body-large);
      color: var(--td-text-color-primary);
      font-weight: 600;
    }

    .stat-detail-content .detail-value.success {
      color: var(--td-success-color);
    }

    .stat-detail-content .detail-value.warning {
      color: var(--td-warning-color);
    }

    .stat-detail-content .detail-value.error {
      color: var(--td-error-color);
    }

    /* 统计卡片悬停效果增强 */
    .stat-card {
      position: relative;
      overflow: hidden;
    }

    .stat-card::after {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      transition: left 0.6s ease;
    }

    .stat-card:hover::after {
      left: 100%;
    }

    /* 图表加载动画优化 */
    .chart-container::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 48px;
      height: 48px;
      margin: -24px 0 0 -24px;
      border: 4px solid var(--td-border-color-light);
      border-top: 4px solid var(--td-brand-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      z-index: 1;
      opacity: 1;
      transition: opacity 0.3s ease;
    }

    .chart-container.loaded::before {
      opacity: 0;
      pointer-events: none;
    }

    .chart-container canvas {
      position: relative;
      z-index: 2;
      max-width: 100%;
      max-height: 100%;
    }

    /* 图表空状态 */
    .chart-container.empty {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: var(--td-text-color-placeholder);
      font-size: var(--td-font-size-body-medium);
      gap: var(--td-comp-size-s);
    }

    .chart-container.empty::before {
      display: none;
    }

    .chart-container.empty::after {
      content: '📊';
      font-size: 48px;
      opacity: 0.5;
    }

    /* 图表工具提示优化 */
    .chart-tooltip {
      background: var(--td-bg-color-container) !important;
      border: 1px solid var(--td-border-color) !important;
      border-radius: var(--td-radius-medium) !important;
      box-shadow: var(--td-shadow-2) !important;
      padding: var(--td-comp-size-m) !important;
      font-size: var(--td-font-size-body-medium) !important;
      color: var(--td-text-color-primary) !important;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* 快速操作按钮增强效果 */
    .action-btn {
      position: relative;
      overflow: hidden;
    }

    .action-btn::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      transition: width 0.3s ease, height 0.3s ease;
    }

    .action-btn:hover::before {
      width: 300px;
      height: 300px;
    }

    .action-btn span {
      position: relative;
      z-index: 1;
    }

    /* 欢迎横幅增强效果 */
    .welcome-banner {
      position: relative;
      overflow: hidden;
    }

    .welcome-banner::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
      animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
      0% { transform: translateX(-100%); }
      100% { transform: translateX(100%); }
    }

    .welcome-banner h1,
    .welcome-banner p,
    .welcome-stats {
      position: relative;
      z-index: 2;
    }

    /* 响应式设计 */
    @media (max-width: 1200px) {
      .charts-grid {
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: var(--td-comp-size-xl);
      }
      
      .chart-card {
        min-height: 380px;
        padding: var(--td-comp-size-xl);
      }
      
      .chart-container {
        height: 280px;
      }
    }

    @media (max-width: 768px) {
      .dashboard-container {
        padding: var(--td-comp-size-m);
      }
      
      .welcome-banner {
        padding: var(--td-comp-size-xl);
      }
      
      .welcome-banner h1 {
        font-size: var(--td-font-size-title-large);
      }
      
      .welcome-stats {
        flex-direction: column;
        gap: var(--td-comp-size-m);
      }
      
      .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--td-comp-size-m);
      }
      
      .charts-grid {
        grid-template-columns: 1fr;
        gap: var(--td-comp-size-l);
      }
      
      .chart-card {
        min-height: 340px;
        padding: var(--td-comp-size-l);
      }
      
      .chart-container {
        height: 240px;
      }
      
      .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--td-comp-size-s);
      }
      
      .chart-actions {
        align-self: flex-end;
      }
      
      .action-buttons {
        grid-template-columns: 1fr;
      }
    }

    @media (max-width: 480px) {
      .chart-card {
        min-height: 300px;
        padding: var(--td-comp-size-m);
      }
      
      .chart-container {
        height: 200px;
      }
      
      .chart-title {
        font-size: var(--td-font-size-title-small);
      }
      
      .chart-action-btn {
        width: 32px;
        height: 32px;
        font-size: 14px;
      }
    }

    /* 图表卡片动画效果 */
    .chart-card {
      animation: fadeInUp 0.6s cubic-bezier(0.38, 0, 0.24, 1);
    }

    .chart-card:nth-child(1) {
      animation-delay: 0.1s;
    }

    .chart-card:nth-child(2) {
      animation-delay: 0.2s;
    }

    .chart-card:nth-child(3) {
      animation-delay: 0.3s;
    }

    .chart-card:nth-child(4) {
      animation-delay: 0.4s;
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* 图表容器悬停效果增强 */
    .chart-container:hover {
      background: var(--td-bg-color-tertiarycontainer);
      border-color: var(--td-brand-color-light);
      transition: all 0.3s cubic-bezier(0.38, 0, 0.24, 1);
    }

    /* 图表操作按钮组悬停效果 */
    .chart-actions:hover .chart-action-btn {
      transform: scale(1.05);
    }

    .chart-actions .chart-action-btn {
      transition: all 0.2s cubic-bezier(0.38, 0, 0.24, 1);
    }

    /* ======= 弹窗表单控件美化（TDesign风格） ======= */
    .modal-content .form-group,
    .modal-content .detail-item {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
    }
    .modal-content .form-group label,
    .modal-content .detail-label {
      min-width: 90px;
      color: var(--td-text-color-secondary);
      font-size: 14px;
      margin-right: 12px;
      text-align: right;
      flex-shrink: 0;
    }
    .modal-content .form-group input[type="text"],
    .modal-content .form-group input[type="password"],
    .modal-content .form-group input[type="number"],
    .modal-content .form-group select {
      flex: 1;
      padding: 8px 12px;
      border: 1px solid var(--td-border-color);
      border-radius: var(--td-radius-default);
      font-size: 14px;
      background: var(--td-bg-color-container);
      color: var(--td-text-color-primary);
      transition: border-color 0.2s;
      outline: none;
      box-sizing: border-box;
    }
    .modal-content .form-group input:focus,
    .modal-content .form-group select:focus {
      border-color: var(--td-brand-color);
      box-shadow: 0 0 0 2px var(--td-brand-color-light);
    }
    .modal-content .form-group input[readonly] {
      background: #f5f5f5;
      color: #aaa;
      cursor: not-allowed;
    }
    .modal-content .form-group input[type="checkbox"] {
      width: 18px;
      height: 18px;
      accent-color: var(--td-brand-color);
      margin-right: 8px;
      vertical-align: middle;
    }
    .modal-content .form-group select {
      min-width: 120px;
    }
    .modal-content .form-group {
      gap: 8px;
    }
    .modal-content .settings-section {
      margin-bottom: 20px;
    }
    .modal-content .settings-section h4 {
      font-size: 15px;
      color: var(--td-brand-color);
      margin-bottom: 10px;
      font-weight: 600;
    }
    /* 统计详情弹窗美化 */
    .stat-detail-content {
      display: flex;
      flex-direction: column;
      gap: 10px;
      margin-bottom: 10px;
    }
    .stat-detail-content .detail-item {
      justify-content: flex-start;
      font-size: 15px;
    }
    .stat-detail-content .detail-label {
      min-width: 80px;
      color: var(--td-text-color-secondary);
      font-weight: 400;
    }
    .stat-detail-content .detail-value {
      color: var(--td-brand-color);
      font-weight: 600;
      font-size: 18px;
      margin-left: 8px;
    }
    .stat-detail-content .detail-value.negative {
      color: var(--td-error-color);
    }
    .stat-detail-content .detail-value.positive {
      color: var(--td-success-color);
    }
    /* 弹窗底部按钮组美化 */
    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 10px;
    }

    /* 设备管理和点位管理页面样式 */
    .devices-container,
    .locations-container {
      background: var(--td-bg-color-container);
      border-radius: var(--td-radius-large);
      box-shadow: var(--td-shadow-1);
      overflow: hidden;
      margin: 24px;
    }

    /* 筛选器区域 */
    .filter-section {
      display: flex;
      align-items: center;
      gap: var(--td-comp-size-m);
      padding: var(--td-comp-size-xl);
      background: var(--td-bg-color-secondarycontainer);
      border-bottom: 1px solid var(--td-border-color);
      flex-wrap: wrap;
    }

    .filter-group {
      display: flex;
      align-items: center;
      gap: var(--td-comp-size-xs);
      min-width: 200px;
    }

    .filter-group label {
      font-weight: 500;
      color: var(--td-text-color-primary);
      white-space: nowrap;
      font-size: var(--td-font-size-body-medium);
    }

    .filter-group select,
    .filter-group input {
      padding: var(--td-comp-size-s) var(--td-comp-size-m);
      border: 1px solid var(--td-border-color);
      border-radius: var(--td-radius-default);
      background: var(--td-bg-color-container);
      font-size: var(--td-font-size-body-medium);
      color: var(--td-text-color-primary);
      transition: all 0.2s cubic-bezier(0.38, 0, 0.24, 1);
      min-width: 120px;
    }

    .filter-group select:focus,
    .filter-group input:focus {
      outline: none;
      border-color: var(--td-brand-color);
      box-shadow: 0 0 0 2px var(--td-brand-color-light);
    }

    .filter-group select:hover,
    .filter-group input:hover {
      border-color: var(--td-brand-color-hover);
    }

    /* 表格容器 */
    .table-container {
      overflow-x: auto;
      max-height: calc(100vh - 300px);
    }

    /* 状态徽章样式 */
    .status-badge {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: var(--td-comp-size-xxs) var(--td-comp-size-s);
      border-radius: 12px;
      font-size: var(--td-font-size-body-small);
      font-weight: 500;
      line-height: 1;
      white-space: nowrap;
      border: 1px solid transparent;
      min-width: 60px;
      text-align: center;
    }

    .status-badge.status-online {
      background: var(--td-success-color-light);
      color: var(--td-success-color);
      border-color: rgba(0, 168, 112, 0.2);
    }

    .status-badge.status-offline {
      background: var(--td-warning-color-light);
      color: var(--td-warning-color);
      border-color: rgba(237, 123, 47, 0.2);
    }

    .status-badge.status-error {
      background: var(--td-error-color-light);
      color: var(--td-error-color);
      border-color: rgba(213, 73, 65, 0.2);
    }

    .status-badge.status-normal {
      background: var(--td-success-color-light);
      color: var(--td-success-color);
      border-color: rgba(0, 168, 112, 0.2);
    }

    .status-badge.status-disabled {
      background: var(--td-bg-color-secondarycontainer);
      color: var(--td-text-color-secondary);
      border-color: var(--td-border-color);
    }

    /* 操作按钮样式 */
    .btn-small {
      padding: var(--td-comp-size-xxs) var(--td-comp-size-s);
      font-size: var(--td-font-size-body-small);
      border-radius: var(--td-radius-default);
      border: 1px solid transparent;
      cursor: pointer;
      transition: all 0.2s cubic-bezier(0.38, 0, 0.24, 1);
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: var(--td-comp-size-xxs);
      font-weight: 500;
      min-width: 60px;
    }

    .btn-small:hover {
      transform: translateY(-1px);
      box-shadow: var(--td-shadow-1);
    }

    .btn-small:active {
      transform: translateY(0);
    }

    /* 表格行悬停效果增强 */
    .data-table tr:hover .btn-small {
      opacity: 1;
    }

    .data-table tr .btn-small {
      opacity: 0.7;
      transition: opacity 0.2s ease;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .filter-section {
        flex-direction: column;
        align-items: stretch;
        gap: var(--td-comp-size-m);
      }

      .filter-group {
        min-width: auto;
        width: 100%;
      }

      .filter-group select,
      .filter-group input {
        flex: 1;
        min-width: 0;
      }

      .devices-container,
      .locations-container {
        margin: 12px;
      }

      .table-container {
        max-height: calc(100vh - 200px);
      }

      .data-table {
        font-size: var(--td-font-size-body-small);
      }

      .data-table th,
      .data-table td {
        padding: var(--td-comp-size-s);
      }
    }

    @media (max-width: 480px) {
      .filter-section {
        padding: var(--td-comp-size-m);
      }

      .data-table th,
      .data-table td {
        padding: var(--td-comp-size-xs) var(--td-comp-size-xxs);
        font-size: 12px;
      }

      .status-badge {
        font-size: 11px;
        padding: 2px var(--td-comp-size-xxs);
        min-width: 50px;
      }

      .btn-small {
        padding: 2px var(--td-comp-size-xxs);
        font-size: 11px;
        min-width: 50px;
      }
    }

    /* 空状态样式 */
    .devices-container .table-empty,
    .locations-container .table-empty {
      text-align: center;
      padding: var(--td-comp-size-xxxl);
      color: var(--td-text-color-secondary);
    }

    .devices-container .table-empty-icon,
    .locations-container .table-empty-icon {
      font-size: 48px;
      margin-bottom: var(--td-comp-size-m);
      opacity: 0.5;
    }

    .devices-container .table-empty-text,
    .locations-container .table-empty-text {
      font-size: var(--td-font-size-body-medium);
      margin-bottom: var(--td-comp-size-m);
    }

    /* 加载状态 */
    .devices-container.loading,
    .locations-container.loading {
      position: relative;
    }

    .devices-container.loading::after,
    .locations-container.loading::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
    }

    .devices-container.loading::before,
    .locations-container.loading::before {
      content: '加载中...';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 11;
      color: var(--td-text-color-primary);
      font-weight: 500;
    }

    /* 表格滚动条样式 */
    .table-container::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    .table-container::-webkit-scrollbar-track {
      background: var(--td-bg-color-secondarycontainer);
      border-radius: 4px;
    }

    .table-container::-webkit-scrollbar-thumb {
      background: var(--td-border-color);
      border-radius: 4px;
    }

    .table-container::-webkit-scrollbar-thumb:hover {
      background: var(--td-text-color-secondary);
    }

    /* ========================================
     * 用户行为分析页面样式
     * ======================================== */
    
    .user-behavior-container {
      padding: var(--td-comp-size-l);
    }

    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: var(--td-comp-size-xl);
      padding-bottom: var(--td-comp-size-l);
      border-bottom: 1px solid var(--td-border-color);
    }

    .page-title h2 {
      margin: 0 0 var(--td-comp-size-xs) 0;
      font-size: var(--td-font-size-headline-medium);
      font-weight: 600;
      color: var(--td-text-color-primary);
    }

    .page-title p {
      margin: 0;
      color: var(--td-text-color-secondary);
      font-size: var(--td-font-size-body-medium);
    }

    .page-actions {
      display: flex;
      gap: var(--td-comp-size-s);
    }

    .stat-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--td-comp-size-s);
    }

    .stat-trend {
      display: flex;
      align-items: center;
      gap: 2px;
      font-size: var(--td-font-size-body-small);
      font-weight: 500;
    }

    .stat-trend.up {
      color: var(--td-success-color);
    }

    .stat-trend.down {
      color: var(--td-error-color);
    }

    .stat-subtitle {
      font-size: var(--td-font-size-body-small);
      color: var(--td-text-color-secondary);
      margin-top: var(--td-comp-size-xs);
    }

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--td-comp-size-m);
    }

    .chart-actions {
      display: flex;
      gap: var(--td-comp-size-xs);
    }

    .table-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--td-comp-size-l);
      padding: var(--td-comp-size-m);
      background: var(--td-bg-color-container);
      border-radius: var(--td-radius-default);
      border: 1px solid var(--td-border-color);
    }

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: var(--td-comp-size-m);
    }

    .search-box {
      position: relative;
      display: flex;
      align-items: center;
    }

    .search-box i {
      position: absolute;
      left: var(--td-comp-size-s);
      color: var(--td-text-color-secondary);
      font-size: var(--td-font-size-body-medium);
    }

    .search-box input {
      padding: var(--td-comp-size-s) var(--td-comp-size-s) var(--td-comp-size-s) var(--td-comp-size-xl);
      border: 1px solid var(--td-border-color);
      border-radius: var(--td-radius-default);
      font-size: var(--td-font-size-body-medium);
      width: 250px;
      background: var(--td-bg-color-container);
      color: var(--td-text-color-primary);
    }

    .search-box input:focus {
      outline: none;
      border-color: var(--td-brand-color);
      box-shadow: 0 0 0 2px var(--td-brand-color-focus);
    }

    .filter-group {
      display: flex;
      gap: var(--td-comp-size-s);
    }

    .filter-group select {
      padding: var(--td-comp-size-s) var(--td-comp-size-m);
      border: 1px solid var(--td-border-color);
      border-radius: var(--td-radius-default);
      font-size: var(--td-font-size-body-medium);
      background: var(--td-bg-color-container);
      color: var(--td-text-color-primary);
      cursor: pointer;
    }

    .filter-group select:focus {
      outline: none;
      border-color: var(--td-brand-color);
    }

    .toolbar-right {
      display: flex;
      gap: var(--td-comp-size-s);
    }

    .user-type-badge {
      padding: var(--td-comp-size-xs) var(--td-comp-size-s);
      border-radius: var(--td-radius-small);
      font-size: var(--td-font-size-body-small);
      font-weight: 500;
    }

    .user-type-personal {
      background: var(--td-success-color-light);
      color: var(--td-success-color);
    }

    .user-type-business {
      background: var(--td-warning-color-light);
      color: var(--td-warning-color);
    }

    .user-type-student {
      background: var(--td-info-color-light);
      color: var(--td-info-color);
    }

    .satisfaction-rating {
      display: flex;
      align-items: center;
      gap: var(--td-comp-size-xs);
    }

    .rating-stars {
      color: var(--td-warning-color);
      font-size: var(--td-font-size-body-small);
    }

    .rating-text {
      font-size: var(--td-font-size-body-small);
      color: var(--td-text-color-secondary);
    }

    .table-actions {
      display: flex;
      gap: var(--td-comp-size-xs);
    }

    .pagination {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: var(--td-comp-size-l);
      padding: var(--td-comp-size-m);
      background: var(--td-bg-color-container);
      border-radius: var(--td-radius-default);
      border: 1px solid var(--td-border-color);
    }

    .pagination-info {
      color: var(--td-text-color-secondary);
      font-size: var(--td-font-size-body-medium);
    }

    .pagination-controls {
      display: flex;
      gap: var(--td-comp-size-xs);
    }

    /* ========================================
     * 市场热点分析页面样式
     * ======================================== */

    .market-analysis-container {
      padding: var(--td-comp-size-l);
    }

    .ranking-badge {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      font-size: var(--td-font-size-body-small);
      font-weight: 600;
    }

    .ranking-top {
      background: var(--td-warning-color);
      color: white;
    }

    .ranking-normal {
      background: var(--td-bg-color-secondarycontainer);
      color: var(--td-text-color-secondary);
    }

    .location-info {
      display: flex;
      flex-direction: column;
      gap: 2px;
    }

    .location-name {
      font-weight: 500;
      color: var(--td-text-color-primary);
    }

    .location-address {
      font-size: var(--td-font-size-body-small);
      color: var(--td-text-color-secondary);
    }

    .heat-indicator {
      display: flex;
      align-items: center;
      gap: var(--td-comp-size-xs);
    }

    .heat-bar {
      width: 60px;
      height: 6px;
      background: var(--td-bg-color-secondarycontainer);
      border-radius: 3px;
      overflow: hidden;
    }

    .heat-fill {
      height: 100%;
      background: linear-gradient(90deg, var(--td-success-color), var(--td-warning-color), var(--td-error-color));
      border-radius: 3px;
      transition: width 0.3s ease;
    }

    .heat-value {
      font-size: var(--td-font-size-body-small);
      font-weight: 500;
      color: var(--td-text-color-primary);
      min-width: 30px;
    }

    .revenue-info {
      display: flex;
      flex-direction: column;
      gap: 2px;
    }

    .revenue-amount {
      font-weight: 500;
      color: var(--td-text-color-primary);
    }

    .revenue-trend {
      font-size: var(--td-font-size-body-small);
      font-weight: 500;
    }

    .revenue-trend.up {
      color: var(--td-success-color);
    }

    .revenue-trend.down {
      color: var(--td-error-color);
    }

    .usage-rate {
      display: flex;
      align-items: center;
      gap: var(--td-comp-size-xs);
    }

    .usage-bar {
      width: 60px;
      height: 6px;
      background: var(--td-bg-color-secondarycontainer);
      border-radius: 3px;
      overflow: hidden;
    }

    .usage-fill {
      height: 100%;
      background: var(--td-brand-color);
      border-radius: 3px;
      transition: width 0.3s ease;
    }

    .usage-value {
      font-size: var(--td-font-size-body-small);
      font-weight: 500;
      color: var(--td-text-color-primary);
      min-width: 30px;
    }

    .growth-badge {
      padding: var(--td-comp-size-xs) var(--td-comp-size-s);
      border-radius: var(--td-radius-small);
      font-size: var(--td-font-size-body-small);
      font-weight: 500;
    }

    .growth-positive {
      background: var(--td-success-color-light);
      color: var(--td-success-color);
    }

    .growth-negative {
      background: var(--td-error-color-light);
      color: var(--td-error-color);
    }

    /* ========================================
     * 竞争对手分析页面样式
     * ======================================== */

    .competitor-analysis-container {
      padding: var(--td-comp-size-l);
    }

    .competitor-info {
      display: flex;
      flex-direction: column;
      gap: 2px;
    }

    .competitor-name {
      font-weight: 500;
      color: var(--td-text-color-primary);
    }

    .competitor-desc {
      font-size: var(--td-font-size-body-small);
      color: var(--td-text-color-secondary);
    }

    .market-share-info {
      display: flex;
      align-items: center;
      gap: var(--td-comp-size-xs);
    }

    .share-bar {
      width: 60px;
      height: 6px;
      background: var(--td-bg-color-secondarycontainer);
      border-radius: 3px;
      overflow: hidden;
    }

    .share-fill {
      height: 100%;
      background: var(--td-brand-color);
      border-radius: 3px;
      transition: width 0.3s ease;
    }

    .share-value {
      font-size: var(--td-font-size-body-small);
      font-weight: 500;
      color: var(--td-text-color-primary);
      min-width: 30px;
    }

    .device-count {
      display: flex;
      flex-direction: column;
      gap: 2px;
    }

    .count-number {
      font-weight: 500;
      color: var(--td-text-color-primary);
    }

    .count-trend {
      font-size: var(--td-font-size-body-small);
      font-weight: 500;
    }

    .count-trend.up {
      color: var(--td-success-color);
    }

    .count-trend.down {
      color: var(--td-error-color);
    }

    .strategy-badge {
      padding: var(--td-comp-size-xs) var(--td-comp-size-s);
      border-radius: var(--td-radius-small);
      font-size: var(--td-font-size-body-small);
      font-weight: 500;
    }

    .strategy-low {
      background: var(--td-success-color-light);
      color: var(--td-success-color);
    }

    .strategy-medium {
      background: var(--td-warning-color-light);
      color: var(--td-warning-color);
    }

    .strategy-high {
      background: var(--td-error-color-light);
      color: var(--td-error-color);
    }

    .threat-badge {
      padding: var(--td-comp-size-xs) var(--td-comp-size-s);
      border-radius: var(--td-radius-small);
      font-size: var(--td-font-size-body-small);
      font-weight: 500;
    }

    .threat-high {
      background: var(--td-error-color-light);
      color: var(--td-error-color);
    }

    .threat-medium {
      background: var(--td-warning-color-light);
      color: var(--td-warning-color);
    }

    .threat-low {
      background: var(--td-success-color-light);
      color: var(--td-success-color);
    }

    .position-badge {
      padding: var(--td-comp-size-xs) var(--td-comp-size-s);
      border-radius: var(--td-radius-small);
      font-size: var(--td-font-size-body-small);
      font-weight: 500;
    }

    .position-leader {
      background: var(--td-brand-color-light);
      color: var(--td-brand-color);
    }

    .position-challenger {
      background: var(--td-warning-color-light);
      color: var(--td-warning-color);
    }

    .position-follower {
      background: var(--td-bg-color-secondarycontainer);
      color: var(--td-text-color-secondary);
    }

    .advantage-analysis {
      display: flex;
      flex-direction: column;
      gap: 2px;
    }

    .advantage-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: var(--td-font-size-body-small);
    }

    .advantage-label {
      color: var(--td-text-color-secondary);
    }

    .advantage-value {
      font-weight: 500;
      color: var(--td-text-color-primary);
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .page-header {
        flex-direction: column;
        gap: var(--td-comp-size-m);
        align-items: stretch;
      }

      .page-actions {
        justify-content: flex-end;
      }

      .table-toolbar {
        flex-direction: column;
        gap: var(--td-comp-size-m);
        align-items: stretch;
      }

      .toolbar-left {
        flex-direction: column;
        gap: var(--td-comp-size-s);
      }

      .search-box input {
        width: 100%;
      }

      .filter-group {
        flex-direction: column;
      }

      .toolbar-right {
        justify-content: flex-end;
      }

      .pagination {
        flex-direction: column;
        gap: var(--td-comp-size-m);
        align-items: center;
      }

      .table-actions {
        flex-direction: column;
        gap: var(--td-comp-size-xs);
      }

      .btn-small {
        padding: var(--td-comp-size-xs) var(--td-comp-size-s);
        font-size: var(--td-font-size-body-small);
      }
    }
  </style>
</head>
<body>
  <!-- 登录页面 -->
  <div class="login-container" id="loginPage">
    <div class="login-card">
      <div class="login-header">
        <div class="login-logo">
          <i class="ri-flashlight-line" style="font-size: 48px; color: var(--td-brand-color);"></i>
        </div>
        <h1>数据分析下的共享充电宝</h1>
        <p>市场热点分析后台管理</p>
      </div>
      <form class="login-form" onsubmit="handleLogin(event)">
        <div class="form-group">
          <label for="username">用户名</label>
          <input type="text" id="username" name="username" class="form-input" placeholder="请输入用户名" required>
        </div>
        <div class="form-group">
          <label for="password">密码</label>
          <input type="password" id="password" name="password" class="form-input" placeholder="请输入密码" required>
        </div>
        <button type="submit" class="btn btn-primary btn-lg">登录</button>
      </form>
    </div>
  </div>

  <!-- 主应用容器 -->
  <div class="app-container" id="appContainer">
    <!-- 侧边栏 -->
    <nav class="sidebar" id="sidebar">
      <div class="sidebar-header">
        <h1>
          <i class="ri-flashlight-line logo-icon"></i>
          数据分析下的共享充电宝市场热点分析系统
        </h1>
      </div>
      <div class="nav-menu">
        <a href="#" class="nav-item active" data-page="dashboard" onclick="loadPage('dashboard')">
          <i class="ri-dashboard-line"></i>数据概览
        </a>
        <a href="#" class="nav-item" data-page="devices" onclick="loadPage('devices')">
          <i class="ri-device-line"></i>设备管理
        </a>
        <a href="#" class="nav-item" data-page="locations" onclick="loadPage('locations')">
          <i class="ri-map-pin-line"></i>点位管理
        </a>
        <a href="#" class="nav-item" data-page="user-behavior" onclick="loadPage('user-behavior')">
          <i class="ri-user-line"></i>用户行为分析
        </a>
        <a href="#" class="nav-item" data-page="market-analysis" onclick="loadPage('market-analysis')">
          <i class="ri-fire-line"></i>市场热点分析
        </a>
        <a href="#" class="nav-item" data-page="competitor-analysis" onclick="loadPage('competitor-analysis')">
          <i class="ri-user-star-line"></i>竞争对手分析
        </a>
        <a href="#" class="nav-item" data-page="financial-analysis" onclick="loadPage('financial-analysis')">
          <i class="ri-money-dollar-circle-line"></i>收入与财务分析
        </a>
        <a href="#" class="nav-item" data-page="maintenance" onclick="loadPage('maintenance')">
          <i class="ri-tools-line"></i>维护工单
        </a>
        <a href="#" class="nav-item" data-page="system" onclick="loadPage('system')">
          <i class="ri-settings-line"></i>系统管理
        </a>
      </div>
    </nav>

    <!-- 主内容区 -->
    <div class="main-content">
      <!-- 顶部导航栏 -->
      <header class="top-navbar">
        <div class="navbar-left">
          <button class="menu-toggle" onclick="toggleSidebar()">
            <i class="ri-menu-line"></i>
          </button>
          <div class="breadcrumb">
            <span id="breadcrumbText">数据概览</span>
          </div>
        </div>
        <div class="navbar-right">
          <div class="user-info" onclick="toggleUserDropdown()">
            <div class="user-avatar" id="userAvatar">A</div>
            <span class="user-name">管理员</span>
            <i class="ri-arrow-down-s-line"></i>
          </div>
          <div class="dropdown-menu" id="userDropdownMenu">
            <a href="#" class="dropdown-item" onclick="showProfile()">
              <i class="ri-user-line"></i>个人资料
            </a>
            <a href="#" class="dropdown-item" onclick="showSettings()">
              <i class="ri-settings-line"></i>系统设置
            </a>
            <a href="#" class="dropdown-item" onclick="logout()">
              <i class="ri-logout-box-line"></i>退出登录
            </a>
          </div>
        </div>
      </header>

      <!-- 页面内容 -->
      <div class="page-content" id="pageContent">
        <!-- 页面内容将通过JavaScript动态加载 -->
      </div>
    </div>
  </div>

  <!-- 脚本引用 -->
  <script src="data/mock-data.js"></script>
  <script src="js/main.js"></script>
</body>
</html> 