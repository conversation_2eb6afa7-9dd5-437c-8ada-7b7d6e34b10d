// ========================================
// 共享充电宝市场热点分析系统 - main.js
// 注意：模拟数据从 data/mock-data.js 中引用
// ========================================

// ========================================
// 数据管理模块 - 真正的数据操作
// ========================================

// 数据存储管理
const DataManager = {
  // 从localStorage获取数据，如果没有则使用mockData
  getData(key) {
    const stored = localStorage.getItem(`charging_system_${key}`);
    if (stored) {
      try {
        return JSON.parse(stored);
      } catch (e) {
        console.warn(`解析存储数据失败: ${key}`, e);
      }
    }
    return mockData[key] || [];
  },

  // 保存数据到localStorage
  saveData(key, data) {
    try {
      localStorage.setItem(`charging_system_${key}`, JSON.stringify(data));
      return true;
    } catch (e) {
      console.error(`保存数据失败: ${key}`, e);
      return false;
    }
  },

  // 获取下一个ID
  getNextId(key) {
    const data = this.getData(key);
    if (Array.isArray(data) && data.length > 0) {
      return Math.max(...data.map(item => item.id || 0)) + 1;
    }
    return 1;
  },

  // 添加数据
  addItem(key, item) {
    const data = this.getData(key);
    if (Array.isArray(data)) {
      const newItem = { ...item, id: this.getNextId(key) };
      data.push(newItem);
      this.saveData(key, data);
      return newItem;
    }
    return null;
  },

  // 更新数据
  updateItem(key, id, updates) {
    const data = this.getData(key);
    if (Array.isArray(data)) {
      const index = data.findIndex(item => item.id == id);
      if (index !== -1) {
        data[index] = { ...data[index], ...updates };
        this.saveData(key, data);
        return data[index];
      }
    }
    return null;
  },

  // 删除数据
  deleteItem(key, id) {
    const data = this.getData(key);
    if (Array.isArray(data)) {
      const index = data.findIndex(item => item.id == id);
      if (index !== -1) {
        const deleted = data.splice(index, 1)[0];
        this.saveData(key, data);
        return deleted;
      }
    }
    return null;
  },

  // 搜索数据
  searchItems(key, searchTerm, fields = []) {
    const data = this.getData(key);
    if (!Array.isArray(data) || !searchTerm) return data;
    
    const term = searchTerm.toLowerCase();
    return data.filter(item => {
      if (fields.length === 0) {
        // 搜索所有字符串字段
        return Object.values(item).some(value => 
          typeof value === 'string' && value.toLowerCase().includes(term)
        );
      } else {
        // 搜索指定字段
        return fields.some(field => {
          const value = item[field];
          return typeof value === 'string' && value.toLowerCase().includes(term);
        });
      }
    });
  },

  // 筛选数据
  filterItems(key, filters) {
    const data = this.getData(key);
    if (!Array.isArray(data)) return data;
    
    return data.filter(item => {
      return Object.entries(filters).every(([field, value]) => {
        if (!value || value === '') return true;
        return item[field] === value;
      });
    });
  },

  // 分页数据
  paginateItems(items, page = 1, pageSize = 10) {
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    return {
      data: items.slice(start, end),
      total: items.length,
      page: page,
      pageSize: pageSize,
      totalPages: Math.ceil(items.length / pageSize)
    };
  },

  // 初始化数据（如果localStorage中没有数据）
  initializeData() {
    const keys = ['devices', 'locations', 'userBehavior', 'market', 'competitors', 'financial', 'maintenance'];
    keys.forEach(key => {
      if (!localStorage.getItem(`charging_system_${key}`)) {
        this.saveData(key, mockData[key]);
      }
    });
  }
};

// 页面状态管理
const PageState = {
  currentPage: 1,
  pageSize: 10,
  searchTerm: '',
  filters: {},
  
  reset() {
    this.currentPage = 1;
    this.searchTerm = '';
    this.filters = {};
  },
  
  setPage(page) {
    this.currentPage = page;
  },
  
  setSearch(term) {
    this.searchTerm = term;
    this.currentPage = 1; // 搜索时重置到第一页
  },
  
  setFilters(filters) {
    this.filters = filters;
    this.currentPage = 1; // 筛选时重置到第一页
  }
};

// ========================================
// 全局变量定义
// ========================================
let currentPage = 'dashboard';
let isLoggedIn = false;
let userInfo = null;

// 设备筛选缓存
let deviceFilter = {
  status: '',
  group: '',
  keyword: ''
};

// 点位筛选缓存
let locationFilter = {
  area: '',
  keyword: ''
};

// 图表实例存储
if (!window.chartInstances) {
  window.chartInstances = {};
}

// ========================================
// 基础工具函数
// ========================================

// 销毁图表实例
function destroyChart(chartId) {
  try {
    if (window.chartInstances && window.chartInstances[chartId]) {
      const chart = window.chartInstances[chartId];
      if (chart && typeof chart.destroy === 'function') {
        chart.destroy();
      }
      delete window.chartInstances[chartId];
    }
    
    // 额外清理：重置canvas元素
    const canvas = document.getElementById(chartId);
    if (canvas) {
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
      }
    }
  } catch (error) {
    console.warn(`销毁图表 ${chartId} 时出现错误:`, error);
  }
}

// 新增：销毁所有图表实例
function destroyAllCharts() {
  if (window.chartInstances) {
    Object.keys(window.chartInstances).forEach(id => {
      destroyChart(id);
    });
  }
}

// 生成头像
function generateAvatar() {
  const avatar = document.getElementById('userAvatar');
  if (!avatar) return;
  
  const colors = [
    '#0052d9', '#1765ad', '#1890ff', '#52c41a', 
    '#fa8c16', '#f5222d', '#722ed1', '#13c2c2'
  ];
  const color = colors[Math.floor(Math.random() * colors.length)];
  avatar.style.background = color;
  avatar.textContent = 'A';
}

// 切换侧边栏
function toggleSidebar() {
  const sidebar = document.getElementById('sidebar');
  if (sidebar) {
    sidebar.classList.toggle('show');
  }
}

// 切换用户下拉菜单
function toggleUserDropdown() {
  const dropdown = document.getElementById('userDropdownMenu');
  if (dropdown) {
    dropdown.classList.toggle('show');
  }
}

// 关闭用户下拉菜单
function closeUserDropdown() {
  const dropdown = document.getElementById('userDropdownMenu');
  if (dropdown) {
    dropdown.classList.remove('show');
  }
}

// 更新面包屑导航
function updateBreadcrumb(pageName) {
  const breadcrumbText = document.getElementById('breadcrumbText');
  const pageTitles = {
    'dashboard': '数据概览',
    'devices': '设备管理',
    'locations': '点位管理',
    'user-behavior': '用户行为分析',
    'market-analysis': '市场热点分析',
    'competitor-analysis': '竞争对手分析',
    'financial-analysis': '收入与财务分析',
    'maintenance': '维护工单',
    'system': '系统管理'
  };
  
  if (breadcrumbText && pageTitles[pageName]) {
    breadcrumbText.textContent = pageTitles[pageName];
  }
}

// ========================================
// 登录与权限管理
// ========================================

// 初始化应用
function initApp() {
  // 初始化数据管理模块
  DataManager.initializeData();
  
  // 检查登录状态
  if (checkLogin()) {
    showMainApp();
    generateAvatar();
  } else {
    showLoginPage();
  }
}

// 检查登录状态
function checkLogin() {
  const isLoggedIn = localStorage.getItem('isLoggedIn');
  return isLoggedIn === 'true';
}

// 显示登录页面
function showLoginPage() {
  const loginPage = document.getElementById('loginPage');
  const appContainer = document.getElementById('appContainer');
  if (loginPage) loginPage.style.display = 'flex';
  if (appContainer) appContainer.style.display = 'none';
}

// 显示主应用
function showMainApp() {
  const loginPage = document.getElementById('loginPage');
  const appContainer = document.getElementById('appContainer');
  if (loginPage) loginPage.style.display = 'none';
  if (appContainer) appContainer.style.display = 'flex';
  loadPage('dashboard');
}

// 处理登录
function handleLogin(event) {
  event.preventDefault();
  const username = document.getElementById('username')?.value;
  const password = document.getElementById('password')?.value;
  
  // 使用外部模拟数据
  if (typeof mockData !== 'undefined' && mockData.userInfo) {
    if (username === mockData.userInfo.username && password === mockData.userInfo.password) {
      // 保存登录状态
      localStorage.setItem('isLoggedIn', 'true');
      localStorage.setItem('username', username);
      showMainApp();
      showToast('登录成功！', 'success');
    } else {
      showToast('用户名或密码错误！', 'error');
    }
  } else {
    showToast('系统数据加载失败，请刷新页面重试', 'error');
  }
}

// 退出登录
function logout() {
  localStorage.removeItem('isLoggedIn');
  localStorage.removeItem('username');
  showLoginPage();
  showToast('已退出登录', 'info');
}

// ========================================
// 消息提示系统
// ========================================

// 显示提示消息 - TDesign规范
function showToast(message, type = 'info') {
  try {
    // 查找或创建toast容器
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
      toastContainer = document.createElement('div');
      toastContainer.id = 'toastContainer';
      toastContainer.className = 'toast-container';
      document.body.appendChild(toastContainer);
    }
    
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    
    // 创建toast内容
    const iconMap = {
      'info': 'ri-information-line',
      'success': 'ri-check-line',
      'warning': 'ri-alert-line',
      'error': 'ri-close-circle-line'
    };
    
    const icon = iconMap[type] || iconMap['info'];
    
    toast.innerHTML = `
      <div class="toast-content">
        <i class="${icon} toast-icon"></i>
        <span class="toast-message">${message}</span>
      </div>
      <button class="toast-close" onclick="this.parentElement.remove()">
        <i class="ri-close-line"></i>
      </button>
    `;
    
    // 添加toast样式 - TDesign规范
    if (!document.querySelector('#toast-styles')) {
      const style = document.createElement('style');
      style.id = 'toast-styles';
      style.textContent = `
        .toast-container {
          position: fixed;
          top: var(--td-comp-size-xl);
          right: var(--td-comp-size-xl);
          z-index: 9999;
          pointer-events: none;
          display: flex;
          flex-direction: column;
          gap: var(--td-comp-size-xs);
        }
        
        .toast {
          background: var(--td-bg-color-container);
          border-radius: var(--td-radius-medium);
          box-shadow: var(--td-shadow-2);
          padding: var(--td-comp-size-m);
          min-width: 280px;
          max-width: 400px;
          font-size: var(--td-font-size-body-medium);
          line-height: var(--td-line-height-body-medium);
          pointer-events: auto;
          animation: toastSlideIn 0.3s cubic-bezier(0.38, 0, 0.24, 1);
          border: 1px solid var(--td-border-color);
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: var(--td-comp-size-s);
        }
        
        .toast-content {
          display: flex;
          align-items: center;
          gap: var(--td-comp-size-s);
          flex: 1;
        }
        
        .toast-icon {
          font-size: 18px;
          flex-shrink: 0;
        }
        
        .toast-message {
          color: var(--td-text-color-primary);
          flex: 1;
        }
        
        .toast-close {
          background: none;
          border: none;
          color: var(--td-text-color-placeholder);
          cursor: pointer;
          padding: var(--td-comp-size-xxs);
          border-radius: var(--td-radius-default);
          transition: all 0.2s cubic-bezier(0.38, 0, 0.24, 1);
          flex-shrink: 0;
        }
        
        .toast-close:hover {
          background: var(--td-bg-color-secondarycontainer);
          color: var(--td-text-color-primary);
        }
        
        .toast-info {
          border-left: 4px solid var(--td-info-color);
        }
        
        .toast-info .toast-icon {
          color: var(--td-info-color);
        }
        
        .toast-success {
          border-left: 4px solid var(--td-success-color);
        }
        
        .toast-success .toast-icon {
          color: var(--td-success-color);
        }
        
        .toast-warning {
          border-left: 4px solid var(--td-warning-color);
        }
        
        .toast-warning .toast-icon {
          color: var(--td-warning-color);
        }
        
        .toast-error {
          border-left: 4px solid var(--td-error-color);
        }
        
        .toast-error .toast-icon {
          color: var(--td-error-color);
        }
        
        @keyframes toastSlideIn {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
        
        @keyframes toastSlideOut {
          from {
            transform: translateX(0);
            opacity: 1;
          }
          to {
            transform: translateX(100%);
            opacity: 0;
          }
        }
        
        @media (max-width: 768px) {
          .toast-container {
            top: var(--td-comp-size-m);
            right: var(--td-comp-size-m);
            left: var(--td-comp-size-m);
          }
          
          .toast {
            min-width: auto;
            max-width: none;
          }
        }
      `;
      document.head.appendChild(style);
    }
    
    toastContainer.appendChild(toast);
    
    // 自动移除时间
    const duration = type === 'error' ? 5000 : 3000;
    
    setTimeout(() => {
      if (toast.parentNode) {
        toast.style.animation = 'toastSlideOut 0.3s cubic-bezier(0.38, 0, 0.24, 1)';
        setTimeout(() => {
          if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
          }
        }, 300);
      }
    }, duration);
    
  } catch (error) {
    console.error('显示toast失败:', error);
    // 降级到alert
    alert(message);
  }
}

// ========================================
// 模态框系统
// ========================================

// 全局模态框管理
let currentModal = null;

// 显示模态框
function showModal(title, content, buttons = []) {
  // 如果已存在模态框，先移除
  const existingModal = document.querySelector('.modal-overlay');
  if (existingModal) {
    existingModal.remove();
  }
  
  const modal = document.createElement('div');
  modal.className = 'modal-overlay';
  modal.innerHTML = `
    <div class="modal-content">
      <div class="modal-header">
        <h3>${title}</h3>
        <button class="modal-close" onclick="closeModal()">×</button>
      </div>
      <div class="modal-body">
        ${content}
      </div>
      <div class="form-actions">
        ${buttons.map(btn => `
          <button class="btn btn-${btn.class || 'secondary'}" onclick="handleModalButton('${btn.text}', '${btn.class || 'secondary'}')">
            ${btn.text}
          </button>
        `).join('')}
      </div>
    </div>
  `;
  
  document.body.appendChild(modal);
  
  // 存储按钮配置
  window.modalButtons = buttons;
  
  // 添加动画效果
  setTimeout(() => {
    modal.classList.add('active');
  }, 10);
}

// 关闭所有模态框
function closeAllModals() {
  const modals = document.querySelectorAll('.modal-overlay');
  modals.forEach(modal => {
    modal.remove();
  });
}

// 关闭模态框
function closeModal(closeBtn) {
  let modal;
  
  if (closeBtn && closeBtn.closest) {
    // 如果传入了关闭按钮，通过按钮找到模态框
    modal = closeBtn.closest('.modal-overlay');
  } else {
    // 如果没有传入参数或参数无效，查找当前模态框
    modal = document.querySelector('.modal-overlay');
  }
  
  if (modal) {
    modal.style.animation = 'modalSlideOut 0.3s ease';
    setTimeout(() => {
      modal.remove();
    }, 300);
  } else {
    console.warn('未找到要关闭的模态框');
  }
}

// ========================================
// 事件绑定
// ========================================

// 绑定事件
function bindEvents() {
  // 点击外部关闭用户下拉菜单
  document.addEventListener('click', function(e) {
    const userInfo = document.querySelector('.user-info');
    const dropdownMenu = document.getElementById('userDropdownMenu');
    
    if (userInfo && !userInfo.contains(e.target) && dropdownMenu) {
      closeUserDropdown();
    }
  });

  // 绑定导航菜单事件
  document.addEventListener('click', function(e) {
    if (e.target && e.target.closest) {
      const navItem = e.target.closest('.nav-item');
      if (navItem) {
        const page = navItem.getAttribute('data-page');
        if (page) {
          loadPage(page);
        }
      }
    }
  });
}

// ========================================
// 页面加载与初始化
// ========================================

// 加载页面
function loadPage(pageName) {
  try {
    // 关闭所有模态框
    closeAllModals();
    
    destroyAllCharts(); // 页面切换前先销毁所有图表
    currentPage = pageName;
    
    // 更新导航激活状态
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
      item.classList.remove('active');
      if (item.getAttribute('data-page') === pageName) {
        item.classList.add('active');
      }
    });

    // 更新面包屑导航
    updateBreadcrumb(pageName);

    // 获取页面内容区域
    const pageContent = document.getElementById('pageContent');
    if (!pageContent) {
      console.error('找不到页面内容区域');
      return;
    }

    // 根据页面名称加载对应内容
    let content = '';
    switch (pageName) {
      case 'dashboard':
        content = renderDashboard();
        break;
      case 'devices':
        content = renderDevicesPage();
        break;
      case 'locations':
        content = renderLocationsPage();
        break;
      case 'user-behavior':
        content = renderUserBehavior();
        break;
      case 'market-analysis':
        content = renderMarketAnalysis();
        break;
      case 'competitor-analysis':
        content = renderCompetitorAnalysis();
        break;
      case 'financial-analysis':
        content = renderFinancialAnalysis();
        break;
      case 'maintenance':
        content = renderMaintenance();
        break;
      case 'system':
        content = renderSystem();
        break;
      default:
        content = '<div class="error-page">页面不存在</div>';
    }

    pageContent.innerHTML = content;

    // 初始化页面特定功能
    initializePageFunctions(pageName);

  } catch (error) {
    console.error('加载页面失败:', error);
    showToast('页面加载失败', 'error');
  }
}

// 初始化页面特定功能
function initializePageFunctions(pageName) {
  setTimeout(() => {
    switch (pageName) {
      case 'dashboard':
        if (typeof initDashboardCharts === 'function') initDashboardCharts();
        break;
      case 'user-behavior':
        if (typeof initUserBehaviorCharts === 'function') initUserBehaviorCharts();
        break;
      case 'market-analysis':
        if (typeof initMarketAnalysisCharts === 'function') initMarketAnalysisCharts();
        break;
      case 'competitor-analysis':
        if (typeof initCompetitorCharts === 'function') initCompetitorCharts();
        break;
      case 'financial-analysis':
        if (typeof initFinancialCharts === 'function') initFinancialCharts();
        break;
    }
  }, 100);
}

// ========================================
// 页面渲染函数
// ========================================

// 渲染数据概览页面
function renderDashboard() {
  if (typeof mockData === 'undefined') {
    return '<div class="error-message">数据加载失败，请刷新页面重试</div>';
  }

  // 从现有数据计算统计信息
  const devices = mockData.devices || [];
  const financial = mockData.financial || {};
  const userBehavior = mockData.userBehavior || {};
  
  // 计算设备统计
  const totalDevices = devices.length;
  const onlineDevices = devices.filter(d => d.status === '在线').length;
  
  // 计算今日收入（使用最近一天的收入数据）
  const dailyRevenue = financial.dailyRevenue || [];
  const todayRevenue = dailyRevenue.length > 0 ? dailyRevenue[dailyRevenue.length - 1].revenue : 0;
  
  // 计算今日订单（使用最近一天的订单数据）
  const todayOrders = dailyRevenue.length > 0 ? dailyRevenue[dailyRevenue.length - 1].orders : 0;
  
  // 总用户数
  const totalUsers = userBehavior.totalUsers || 0;
  
  // 计算趋势数据（模拟）
  const revenueTrend = '+12.5%';
  const ordersTrend = '+8.3%';
  const devicesTrend = '+2.1%';
  const usersTrend = '+15.7%';
  
  // 获取当前时间
  const now = new Date();
  const timeString = now.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  });
  const dateString = now.toLocaleDateString('zh-CN', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  });
  
  return `
    <div class="dashboard-container">
      <!-- 欢迎横幅 -->
      <div class="welcome-banner">
        <h1>欢迎回来，管理员！</h1>
        <p>今天是 ${dateString}，当前时间 ${timeString}。让我们一起查看今天的业务数据。</p>
        <div class="welcome-stats">
          <div class="welcome-stat">
            <span class="welcome-stat-value">${totalDevices}</span>
            <span class="welcome-stat-label">总设备数</span>
          </div>
          <div class="welcome-stat">
            <span class="welcome-stat-value">${onlineDevices}</span>
            <span class="welcome-stat-label">在线设备</span>
          </div>
          <div class="welcome-stat">
            <span class="welcome-stat-value">${totalUsers.toLocaleString()}</span>
            <span class="welcome-stat-label">注册用户</span>
          </div>
          <div class="welcome-stat">
            <span class="welcome-stat-value">¥${todayRevenue.toLocaleString()}</span>
            <span class="welcome-stat-label">今日收入</span>
          </div>
        </div>
      </div>

      <!-- 统计卡片 -->
      <div class="stats-grid">
        <div class="stat-card revenue">
          <div class="stat-header">
            <div class="stat-icon revenue">
              <i class="ri-money-dollar-circle-line"></i>
            </div>
            <div class="stat-trend up">
              <i class="ri-arrow-up-line"></i>
              ${revenueTrend}
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">¥${todayRevenue.toLocaleString()}</div>
            <div class="stat-label">今日收入</div>
            <div class="stat-subtitle">较昨日增长 ${revenueTrend}</div>
          </div>
        </div>
        
        <div class="stat-card orders">
          <div class="stat-header">
            <div class="stat-icon orders">
              <i class="ri-shopping-cart-line"></i>
            </div>
            <div class="stat-trend up">
              <i class="ri-arrow-up-line"></i>
              ${ordersTrend}
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">${todayOrders.toLocaleString()}</div>
            <div class="stat-label">今日订单</div>
            <div class="stat-subtitle">较昨日增长 ${ordersTrend}</div>
          </div>
        </div>
        
        <div class="stat-card devices">
          <div class="stat-header">
            <div class="stat-icon devices">
              <i class="ri-device-line"></i>
            </div>
            <div class="stat-trend up">
              <i class="ri-arrow-up-line"></i>
              ${devicesTrend}
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">${onlineDevices}/${totalDevices}</div>
            <div class="stat-label">在线设备</div>
            <div class="stat-subtitle">在线率 ${Math.round((onlineDevices / totalDevices) * 100)}%</div>
          </div>
        </div>
        
        <div class="stat-card users">
          <div class="stat-header">
            <div class="stat-icon users">
              <i class="ri-user-line"></i>
            </div>
            <div class="stat-trend up">
              <i class="ri-arrow-up-line"></i>
              ${usersTrend}
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">${totalUsers.toLocaleString()}</div>
            <div class="stat-label">总用户数</div>
            <div class="stat-subtitle">较昨日增长 ${usersTrend}</div>
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="charts-grid">
        <div class="chart-card">
          <div class="chart-header">
            <h3 class="chart-title">收入趋势分析</h3>
            <div class="chart-actions">
              <button class="chart-action-btn" title="刷新数据" onclick="refreshRevenueChart()">
                <i class="ri-refresh-line"></i>
              </button>
              <button class="chart-action-btn" title="导出数据" onclick="exportRevenueData()">
                <i class="ri-download-line"></i>
              </button>
            </div>
          </div>
          <div class="chart-container">
            <canvas id="revenueChart" width="400" height="200"></canvas>
          </div>
        </div>
        
        <div class="chart-card">
          <div class="chart-header">
            <h3 class="chart-title">设备状态分布</h3>
            <div class="chart-actions">
              <button class="chart-action-btn" title="刷新数据" onclick="refreshDeviceStatusChart()">
                <i class="ri-refresh-line"></i>
              </button>
              <button class="chart-action-btn" title="导出数据" onclick="exportDeviceStatusData()">
                <i class="ri-download-line"></i>
              </button>
            </div>
          </div>
          <div class="chart-container">
            <canvas id="deviceStatusChart" width="400" height="200"></canvas>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="quick-actions">
        <h3>快速操作</h3>
        <div class="action-buttons">
          <button class="action-btn" onclick="loadPage('devices')">
            <span>📱</span>
            设备管理
          </button>
          <button class="action-btn" onclick="loadPage('locations')">
            <span>📍</span>
            点位管理
          </button>
          <button class="action-btn" onclick="loadPage('maintenance')">
            <span>🔧</span>
            维护工单
          </button>
          <button class="action-btn" onclick="loadPage('financial-analysis')">
            <span>📊</span>
            财务分析
          </button>
          <button class="action-btn" onclick="loadPage('market-analysis')">
            <span>🔥</span>
            市场热点
          </button>
          <button class="action-btn" onclick="loadPage('user-behavior')">
            <span>👥</span>
            用户分析
          </button>
        </div>
      </div>
    </div>
  `;
}

// 正确的设备管理页面渲染函数
function renderDevicesPage() {
  if (typeof mockData === 'undefined') {
    return '<div class="error-message">数据加载失败，请刷新页面重试</div>';
  }

  const devices = mockData.devices || [];
  
  return `
    <div class="devices-container">
      <!-- 筛选器 -->
      <div class="filter-section">
        <div class="filter-group">
          <label>状态筛选：</label>
          <select id="statusFilter" onchange="filterDevices()">
            <option value="">全部状态</option>
            <option value="在线">在线</option>
            <option value="离线">离线</option>
            <option value="故障">故障</option>
          </select>
        </div>
        <div class="filter-group">
          <label>分组筛选：</label>
          <select id="groupFilter" onchange="filterDevices()">
            <option value="">全部分组</option>
            <option value="A组">A组</option>
            <option value="B组">B组</option>
            <option value="C组">C组</option>
          </select>
        </div>
        <div class="filter-group">
          <label>关键词：</label>
          <input type="text" id="keywordFilter" placeholder="设备名称或位置" onkeyup="filterDevices()">
        </div>
        <button class="btn btn-primary" onclick="addDevice()">添加设备</button>
      </div>

      <!-- 设备列表 -->
      <div class="table-container">
        <table class="data-table">
          <thead>
            <tr>
              <th>设备ID</th>
              <th>设备名称</th>
              <th>类型</th>
              <th>状态</th>
              <th>位置</th>
              <th>分组</th>
              <th>电量</th>
              <th>使用次数</th>
              <th>最后更新</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody id="devicesTableBody">
            ${devices.map(device => `
              <tr>
                <td>${device.id}</td>
                <td>${device.name}</td>
                <td>${device.type}</td>
                <td><span class="status-badge status-${device.status === '在线' ? 'online' : device.status === '离线' ? 'offline' : 'error'}">${device.status}</span></td>
                <td>${device.location}</td>
                <td>${device.group}</td>
                <td>${device.battery}%</td>
                <td>${device.usageCount}</td>
                <td>${device.lastUpdate}</td>
                <td>
                  <button class="btn btn-small btn-secondary" onclick="editDevice(${device.id})">编辑</button>
                  <button class="btn btn-small btn-danger" onclick="deleteDevice(${device.id})">删除</button>
                </td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    </div>
  `;
}

// 正确的点位管理页面渲染函数
function renderLocationsPage() {
  if (typeof mockData === 'undefined') {
    return '<div class="error-message">数据加载失败，请刷新页面重试</div>';
  }

  const locations = mockData.locations || [];
  
  return `
    <div class="locations-container">
      <!-- 筛选器 -->
      <div class="filter-section">
        <div class="filter-group">
          <label>区域筛选：</label>
          <select id="areaFilter" onchange="filterLocations()">
            <option value="">全部区域</option>
            <option value="朝阳区">朝阳区</option>
            <option value="浦东区">浦东区</option>
            <option value="天河区">天河区</option>
            <option value="南山区">南山区</option>
            <option value="西湖区">西湖区</option>
          </select>
        </div>
        <div class="filter-group">
          <label>关键词：</label>
          <input type="text" id="locationKeywordFilter" placeholder="点位名称或地址" onkeyup="filterLocations()">
        </div>
        <button class="btn btn-primary" onclick="addLocation()">添加点位</button>
      </div>

      <!-- 点位列表 -->
      <div class="table-container">
        <table class="data-table">
          <thead>
            <tr>
              <th>点位ID</th>
              <th>点位名称</th>
              <th>地址</th>
              <th>区域</th>
              <th>设备数量</th>
              <th>使用率</th>
              <th>收入</th>
              <th>热度</th>
              <th>状态</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody id="locationsTableBody">
            ${locations.map(location => `
              <tr>
                <td>${location.id}</td>
                <td>${location.name}</td>
                <td>${location.address}</td>
                <td>${location.area}</td>
                <td>${location.deviceCount}</td>
                <td>${location.usageRate}%</td>
                <td>¥${location.revenue}</td>
                <td>${location.heat}</td>
                <td><span class="status-badge status-${location.status === '正常' ? 'normal' : 'disabled'}">${location.status}</span></td>
                <td>
                  <button class="btn btn-small btn-secondary" onclick="editLocation(${location.id})">编辑</button>
                  <button class="btn btn-small btn-danger" onclick="deleteLocation(${location.id})">删除</button>
                </td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    </div>
  `;
}

// 设备筛选函数
function filterDevices() {
  const statusFilter = document.getElementById('statusFilter')?.value || '';
  const groupFilter = document.getElementById('groupFilter')?.value || '';
  const keywordFilter = document.getElementById('deviceSearch')?.value || '';
  
  // 更新筛选缓存
  deviceFilter = { status: statusFilter, group: groupFilter, keyword: keywordFilter };
  
  // 获取所有设备数据
  let devices = DataManager.getData('devices');
  
  // 应用筛选
  if (statusFilter) {
    devices = devices.filter(device => device.status === statusFilter);
  }
  if (groupFilter) {
    devices = devices.filter(device => device.group === groupFilter);
  }
  if (keywordFilter) {
    devices = DataManager.searchItems('devices', keywordFilter, ['name', 'location', 'serialNumber']);
  }
  
  // 更新表格
  updateDevicesTable(devices);
  
  // 更新统计信息
  updateDeviceStats(devices);
}

// 点位筛选函数
function filterLocations() {
  const areaFilter = document.getElementById('areaFilter')?.value || '';
  const keywordFilter = document.getElementById('locationKeywordFilter')?.value || '';
  
  const locations = mockData.locations || [];
  const filteredLocations = locations.filter(location => {
    const areaMatch = !areaFilter || location.area === areaFilter;
    const keywordMatch = !keywordFilter || 
      location.name.toLowerCase().includes(keywordFilter.toLowerCase()) ||
      location.address.toLowerCase().includes(keywordFilter.toLowerCase());
    
    return areaMatch && keywordMatch;
  });
  
  updateLocationsTable(filteredLocations);
}

// 更新设备表格
function updateDevicesTable(devices) {
  const tbody = document.querySelector('#devicesTable tbody');
  if (!tbody) return;
  
  tbody.innerHTML = '';
  
  if (devices.length === 0) {
    tbody.innerHTML = '<tr><td colspan="12" class="text-center">暂无数据</td></tr>';
    return;
  }
  
  devices.forEach(device => {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td><input type="checkbox" value="${device.id}"></td>
      <td>${device.id}</td>
      <td>${device.name}</td>
      <td>${device.type}</td>
      <td><span class="status-badge status-${device.status}">${device.status}</span></td>
      <td>${device.location}</td>
      <td>${device.group}</td>
      <td>${device.battery}%</td>
      <td>${device.usageCount}</td>
      <td>${device.lastUpdate}</td>
      <td>
        <button class="btn btn-small btn-primary" onclick="editDevice(${device.id})">编辑</button>
        <button class="btn btn-small btn-danger" onclick="deleteDevice(${device.id})">删除</button>
      </td>
    `;
    tbody.appendChild(row);
  });
}

// 更新点位表格
function updateLocationsTable(locations) {
  const tbody = document.getElementById('locationsTableBody');
  if (!tbody) return;
  
  tbody.innerHTML = locations.map(location => `
    <tr>
      <td>${location.id}</td>
      <td>${location.name}</td>
      <td>${location.address}</td>
      <td>${location.area}</td>
      <td>${location.deviceCount}</td>
      <td>${location.usageRate}%</td>
      <td>¥${location.revenue}</td>
      <td>${location.heat}</td>
      <td><span class="status-badge status-${location.status === '正常' ? 'normal' : 'disabled'}">${location.status}</span></td>
      <td>
        <button class="btn btn-small btn-secondary" onclick="editLocation(${location.id})">编辑</button>
        <button class="btn btn-small btn-danger" onclick="deleteLocation(${location.id})">删除</button>
      </td>
    </tr>
  `).join('');
}

// 添加设备
function addDevice() {
  showModal('添加设备', `
    <form id="addDeviceForm">
      <div class="form-group">
        <label>设备名称：</label>
        <input type="text" id="deviceName" required>
      </div>
      <div class="form-group">
        <label>设备类型：</label>
        <select id="deviceType" required>
          <option value="">请选择</option>
          <option value="标准型">标准型</option>
          <option value="快充型">快充型</option>
          <option value="无线型">无线型</option>
        </select>
      </div>
      <div class="form-group">
        <label>设备状态：</label>
        <select id="deviceStatus" required>
          <option value="在线">在线</option>
          <option value="离线">离线</option>
          <option value="故障">故障</option>
        </select>
      </div>
      <div class="form-group">
        <label>安装位置：</label>
        <input type="text" id="deviceLocation" required>
      </div>
      <div class="form-group">
        <label>设备分组：</label>
        <select id="deviceGroup" required>
          <option value="">请选择</option>
          <option value="A组">A组</option>
          <option value="B组">B组</option>
          <option value="C组">C组</option>
        </select>
      </div>
      <div class="form-group">
        <label>序列号：</label>
        <input type="text" id="deviceSerial" required>
      </div>
      <div class="form-group">
        <label>MAC地址：</label>
        <input type="text" id="deviceMac" required>
      </div>
      <div class="form-group">
        <label>IP地址：</label>
        <input type="text" id="deviceIp" required>
      </div>
      <div class="form-group">
        <label>供应商：</label>
        <input type="text" id="deviceVendor" required>
      </div>
      <div class="form-group">
        <label>备注：</label>
        <textarea id="deviceRemark"></textarea>
      </div>
    </form>
  `, [
    { text: '取消', class: 'secondary', onclick: 'closeModal()' },
    { text: '确定', class: 'primary', onclick: 'submitAddDevice()' }
  ]);
}

// 提交添加设备
function submitAddDevice() {
  const formData = {
    name: document.getElementById('deviceName')?.value,
    type: document.getElementById('deviceType')?.value,
    status: document.getElementById('deviceStatus')?.value,
    location: document.getElementById('deviceLocation')?.value,
    group: document.getElementById('deviceGroup')?.value,
    serialNumber: document.getElementById('deviceSerial')?.value,
    mac: document.getElementById('deviceMac')?.value,
    ip: document.getElementById('deviceIp')?.value,
    vendor: document.getElementById('deviceVendor')?.value,
    remark: document.getElementById('deviceRemark')?.value,
    battery: 100,
    usageCount: 0,
    lastUpdate: new Date().toLocaleString(),
    lastMaintenance: new Date().toISOString().slice(0, 10),
    firmware: 'v2.1.0',
    installDate: new Date().toISOString().slice(0, 10),
    warranty: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10)
  };
  
  // 验证必填字段
  const requiredFields = ['name', 'type', 'status', 'location', 'group', 'serialNumber', 'mac', 'ip', 'vendor'];
  for (const field of requiredFields) {
    if (!formData[field]) {
      showToast(`请填写${field === 'name' ? '设备名称' : field === 'type' ? '设备类型' : field === 'status' ? '设备状态' : field === 'location' ? '安装位置' : field === 'group' ? '设备分组' : field === 'serialNumber' ? '序列号' : field === 'mac' ? 'MAC地址' : field === 'ip' ? 'IP地址' : '供应商'}`, 'error');
      return;
    }
  }
  
  // 添加设备
  const newDevice = DataManager.addItem('devices', formData);
  if (newDevice) {
    showToast('设备添加成功', 'success');
    closeModal();
    loadPage('devices'); // 重新加载页面
  } else {
    showToast('设备添加失败', 'error');
  }
}

// 编辑设备
function editDevice(deviceId) {
  const devices = DataManager.getData('devices');
  const device = devices.find(d => d.id == deviceId);
  
  if (!device) {
    showToast('设备不存在', 'error');
    return;
  }
  
  showModal(`编辑设备 - ${device.name}`, `
    <form id="editDeviceForm">
      <div class="form-group">
        <label>设备名称：</label>
        <input type="text" id="editDeviceName" value="${device.name}" required>
      </div>
      <div class="form-group">
        <label>设备类型：</label>
        <select id="editDeviceType" required>
          <option value="标准型" ${device.type === '标准型' ? 'selected' : ''}>标准型</option>
          <option value="快充型" ${device.type === '快充型' ? 'selected' : ''}>快充型</option>
          <option value="无线型" ${device.type === '无线型' ? 'selected' : ''}>无线型</option>
        </select>
      </div>
      <div class="form-group">
        <label>设备状态：</label>
        <select id="editDeviceStatus" required>
          <option value="在线" ${device.status === '在线' ? 'selected' : ''}>在线</option>
          <option value="离线" ${device.status === '离线' ? 'selected' : ''}>离线</option>
          <option value="故障" ${device.status === '故障' ? 'selected' : ''}>故障</option>
        </select>
      </div>
      <div class="form-group">
        <label>安装位置：</label>
        <input type="text" id="editDeviceLocation" value="${device.location}" required>
      </div>
      <div class="form-group">
        <label>设备分组：</label>
        <select id="editDeviceGroup" required>
          <option value="A组" ${device.group === 'A组' ? 'selected' : ''}>A组</option>
          <option value="B组" ${device.group === 'B组' ? 'selected' : ''}>B组</option>
          <option value="C组" ${device.group === 'C组' ? 'selected' : ''}>C组</option>
        </select>
      </div>
      <div class="form-group">
        <label>电池电量：</label>
        <input type="number" id="editDeviceBattery" value="${device.battery}" min="0" max="100" required>
      </div>
      <div class="form-group">
        <label>使用次数：</label>
        <input type="number" id="editDeviceUsageCount" value="${device.usageCount}" min="0" required>
      </div>
      <div class="form-group">
        <label>序列号：</label>
        <input type="text" id="editDeviceSerial" value="${device.serialNumber}" required>
      </div>
      <div class="form-group">
        <label>MAC地址：</label>
        <input type="text" id="editDeviceMac" value="${device.mac}" required>
      </div>
      <div class="form-group">
        <label>IP地址：</label>
        <input type="text" id="editDeviceIp" value="${device.ip}" required>
      </div>
      <div class="form-group">
        <label>供应商：</label>
        <input type="text" id="editDeviceVendor" value="${device.vendor}" required>
      </div>
      <div class="form-group">
        <label>备注：</label>
        <textarea id="editDeviceRemark">${device.remark || ''}</textarea>
      </div>
    </form>
  `, [
    { text: '取消', class: 'secondary', onclick: 'closeModal()' },
    { text: '保存', class: 'primary', onclick: `submitEditDevice(${deviceId})` }
  ]);
}

// 提交编辑设备
function submitEditDevice(deviceId) {
  const formData = {
    name: document.getElementById('editDeviceName')?.value,
    type: document.getElementById('editDeviceType')?.value,
    status: document.getElementById('editDeviceStatus')?.value,
    location: document.getElementById('editDeviceLocation')?.value,
    group: document.getElementById('editDeviceGroup')?.value,
    battery: parseInt(document.getElementById('editDeviceBattery')?.value) || 0,
    usageCount: parseInt(document.getElementById('editDeviceUsageCount')?.value) || 0,
    serialNumber: document.getElementById('editDeviceSerial')?.value,
    mac: document.getElementById('editDeviceMac')?.value,
    ip: document.getElementById('editDeviceIp')?.value,
    vendor: document.getElementById('editDeviceVendor')?.value,
    remark: document.getElementById('editDeviceRemark')?.value,
    lastUpdate: new Date().toLocaleString()
  };
  
  // 验证必填字段
  const requiredFields = ['name', 'type', 'status', 'location', 'group', 'serialNumber', 'mac', 'ip', 'vendor'];
  for (const field of requiredFields) {
    if (!formData[field]) {
      showToast(`请填写${field === 'name' ? '设备名称' : field === 'type' ? '设备类型' : field === 'status' ? '设备状态' : field === 'location' ? '安装位置' : field === 'group' ? '设备分组' : field === 'serialNumber' ? '序列号' : field === 'mac' ? 'MAC地址' : field === 'ip' ? 'IP地址' : '供应商'}`, 'error');
      return;
    }
  }
  
  // 更新设备
  const updatedDevice = DataManager.updateItem('devices', deviceId, formData);
  if (updatedDevice) {
    showToast('设备更新成功', 'success');
    closeModal();
    loadPage('devices'); // 重新加载页面
  } else {
    showToast('设备更新失败', 'error');
  }
}

// 删除设备
function deleteDevice(deviceId) {
  if (confirm('确定要删除这个设备吗？此操作不可恢复。')) {
    const deletedDevice = DataManager.deleteItem('devices', deviceId);
    if (deletedDevice) {
      showToast('设备删除成功', 'success');
      loadPage('devices'); // 重新加载页面
    } else {
      showToast('设备删除失败', 'error');
    }
  }
}

// 搜索设备
function searchDevices() {
  const searchTerm = document.getElementById('deviceSearch')?.value || '';
  if (!searchTerm) {
    showToast('请输入搜索关键词', 'warning');
    return;
  }
  
  const devices = DataManager.searchItems('devices', searchTerm, ['name', 'location', 'serialNumber']);
  updateDevicesTable(devices);
  showToast(`找到 ${devices.length} 个设备`, 'success');
}

// 清除设备搜索
function clearDeviceSearch() {
  const searchInput = document.getElementById('deviceSearch');
  if (searchInput) {
    searchInput.value = '';
  }
  filterDevices();
  showToast('搜索条件已清除', 'info');
}

// 添加点位
function addLocation() {
  showModal('添加点位', `
    <form id="addLocationForm">
      <div class="form-group">
        <label>点位名称：</label>
        <input type="text" id="locationName" required>
      </div>
      <div class="form-group">
        <label>地址：</label>
        <input type="text" id="locationAddress" required>
      </div>
      <div class="form-group">
        <label>区域：</label>
        <select id="locationArea" required>
          <option value="">请选择区域</option>
          <option value="朝阳区">朝阳区</option>
          <option value="浦东区">浦东区</option>
          <option value="天河区">天河区</option>
          <option value="南山区">南山区</option>
          <option value="西湖区">西湖区</option>
        </select>
      </div>
    </form>
  `, [
    { text: '取消', class: 'secondary', onclick: 'closeModal()' },
    { text: '确定', class: 'primary', onclick: 'submitAddLocation()' }
  ]);
}

// 提交添加点位
function submitAddLocation() {
  const name = document.getElementById('locationName')?.value;
  const address = document.getElementById('locationAddress')?.value;
  const area = document.getElementById('locationArea')?.value;
  
  if (!name || !address || !area) {
    showToast('请填写完整信息', 'error');
    return;
  }
  
  // 模拟添加点位
  const newLocation = {
    id: Date.now(),
    name,
    address,
    area,
    deviceCount: 0,
    usageRate: 0,
    revenue: 0,
    heat: '低',
    status: '正常'
  };
  
  if (!mockData.locations) mockData.locations = [];
  mockData.locations.push(newLocation);
  
  showToast('点位添加成功', 'success');
  closeModal();
  
  // 刷新点位列表
  if (currentPage === 'locations') {
    const pageContent = document.getElementById('pageContent');
    if (pageContent) {
      pageContent.innerHTML = renderLocationsPage();
    }
  }
}

// 编辑点位
function editLocation(locationId) {
  const location = mockData.locations?.find(l => l.id === locationId);
  if (!location) {
    showToast('点位不存在', 'error');
    return;
  }
  
  showModal('编辑点位', `
    <form id="editLocationForm">
      <div class="form-group">
        <label>点位名称：</label>
        <input type="text" id="editLocationName" value="${location.name}" required>
      </div>
      <div class="form-group">
        <label>地址：</label>
        <input type="text" id="editLocationAddress" value="${location.address}" required>
      </div>
      <div class="form-group">
        <label>区域：</label>
        <select id="editLocationArea" required>
          <option value="朝阳区" ${location.area === '朝阳区' ? 'selected' : ''}>朝阳区</option>
          <option value="浦东区" ${location.area === '浦东区' ? 'selected' : ''}>浦东区</option>
          <option value="天河区" ${location.area === '天河区' ? 'selected' : ''}>天河区</option>
          <option value="南山区" ${location.area === '南山区' ? 'selected' : ''}>南山区</option>
          <option value="西湖区" ${location.area === '西湖区' ? 'selected' : ''}>西湖区</option>
        </select>
      </div>
    </form>
  `, [
    { text: '取消', class: 'secondary', onclick: 'closeModal()' },
    { text: '确定', class: 'primary', onclick: `submitEditLocation(${locationId})` }
  ]);
}

// 提交编辑点位
function submitEditLocation(locationId) {
  const name = document.getElementById('editLocationName')?.value;
  const address = document.getElementById('editLocationAddress')?.value;
  const area = document.getElementById('editLocationArea')?.value;
  
  if (!name || !address || !area) {
    showToast('请填写完整信息', 'error');
    return;
  }
  
  const locationIndex = mockData.locations?.findIndex(l => l.id === locationId);
  if (locationIndex === -1) {
    showToast('点位不存在', 'error');
    return;
  }
  
  mockData.locations[locationIndex] = {
    ...mockData.locations[locationIndex],
    name,
    address,
    area
  };
  
  showToast('点位更新成功', 'success');
  closeModal();
  
  // 刷新点位列表
  if (currentPage === 'locations') {
    const pageContent = document.getElementById('pageContent');
    if (pageContent) {
      pageContent.innerHTML = renderLocationsPage();
    }
  }
}

// 删除点位
function deleteLocation(locationId) {
  showModal('确认删除', `
    <p>确定要删除点位 ${locationId} 吗？此操作不可撤销。</p>
  `, [
    { text: '取消', class: 'secondary', onclick: 'closeModal()' },
    { text: '确定', class: 'btn-danger', onclick: `confirmDeleteLocation(${locationId})` }
  ]);
}

// 确认删除点位
function confirmDeleteLocation(locationId) {
  const locationIndex = mockData.locations?.findIndex(l => l.id === locationId);
  if (locationIndex === -1) {
    showToast('点位不存在', 'error');
    return;
  }
  
  mockData.locations.splice(locationIndex, 1);
  showToast('点位删除成功', 'success');
  closeModal();
  
  // 刷新点位列表
  if (currentPage === 'locations') {
    const pageContent = document.getElementById('pageContent');
    if (pageContent) {
      pageContent.innerHTML = renderLocationsPage();
    }
  }
}

// ========================================
// 用户行为分析功能
// ========================================

// 搜索用户
function searchUsers() {
  const searchTerm = document.getElementById('userSearch')?.value || '';
  if (!searchTerm) {
    showToast('请输入搜索关键词', 'warning');
    return;
  }
  
  showToast(`搜索"${searchTerm}"的结果已更新！`, 'success');
}

// 刷新用户行为数据
function refreshUserBehaviorData() {
  showToast('用户行为数据已刷新！', 'success');
  loadPage('user-behavior');
}

// 导出用户行为数据
function exportUserBehaviorData() {
  const userBehavior = mockData.userBehavior || {};
  const users = userBehavior.users || [];
  
  const exportData = users.map(user => ({
    '用户ID': user.id,
    '用户类型': user.type,
    '活跃度': user.activity,
    '使用次数': user.usageCount,
    '平均时长': `${user.avgTime}h`,
    '最后使用': user.lastUsage,
    '偏好时间': user.preferredTime
  }));
  
  downloadCSVFile(exportData, `用户行为数据_${new Date().toISOString().slice(0, 10)}`);
}

// 查看用户详情
function viewUserDetail(userId) {
  showToast(`查看用户详情: ${userId}`, 'info');
}

// 导出用户详情
function exportUserDetail(userId) {
  showToast(`用户详情导出成功: ${userId}`, 'success');
}

// ========================================
// 市场分析功能
// ========================================

// 刷新市场热点
function refreshMarketHotspots() {
  showToast('热门点位图表已更新！', 'success');
}

// 刷新高峰时段
function refreshPeakHours() {
  showToast('高峰时段图表已更新！', 'success');
}

// 刷新市场趋势
function refreshMarketTrend() {
  showToast('市场趋势图表已更新！', 'success');
}

// 查看热点详情
function viewHotspotDetail(hotspotId) {
  showToast(`查看热点详情: ${hotspotId}`, 'info');
}

// 导出热点详情
function exportHotspotDetail(hotspotId) {
  showToast(`热点详情导出成功: ${hotspotId}`, 'success');
}

// ========================================
// 竞争对手分析功能
// ========================================

// 添加竞争对手
function showAddCompetitorModal() {
  showModal('添加竞争对手', `
    <form id="addCompetitorForm">
      <div class="form-group">
        <label>竞争对手名称：</label>
        <input type="text" id="competitorName" required>
      </div>
      <div class="form-group">
        <label>市场份额：</label>
        <input type="number" id="competitorMarketShare" step="0.1" required>
      </div>
      <div class="form-group">
        <label>威胁等级：</label>
        <select id="competitorThreatLevel" required>
          <option value="高">高</option>
          <option value="中">中</option>
          <option value="低">低</option>
        </select>
      </div>
      <div class="form-group">
        <label>市场定位：</label>
        <input type="text" id="competitorPosition" required>
      </div>
      <div class="form-group">
        <label>设备数量：</label>
        <input type="number" id="competitorDeviceCount" required>
      </div>
      <div class="form-group">
        <label>价格策略：</label>
        <input type="text" id="competitorPriceStrategy" required>
      </div>
    </form>
  `, [
    { text: '取消', class: 'secondary', onclick: 'closeModal()' },
    { text: '确定', class: 'primary', onclick: 'submitAddCompetitor()' }
  ]);
}

// 提交添加竞争对手
function submitAddCompetitor() {
  const name = document.getElementById('competitorName')?.value;
  const marketShare = document.getElementById('competitorMarketShare')?.value;
  const threatLevel = document.getElementById('competitorThreatLevel')?.value;
  const position = document.getElementById('competitorPosition')?.value;
  const deviceCount = document.getElementById('competitorDeviceCount')?.value;
  const priceStrategy = document.getElementById('competitorPriceStrategy')?.value;
  
  if (!name || !marketShare || !threatLevel || !position || !deviceCount || !priceStrategy) {
    showToast('请填写所有必填字段', 'error');
    return;
  }
  
  showToast('竞争对手添加成功', 'success');
  closeModal();
  loadPage('competitor-analysis');
}

// 编辑竞争对手
function editCompetitor(name) {
  showModal('编辑竞争对手', `
    <form id="editCompetitorForm">
      <div class="form-group">
        <label>竞争对手名称：</label>
        <input type="text" id="editCompetitorName" value="${name}" required>
      </div>
      <div class="form-group">
        <label>市场份额：</label>
        <input type="number" id="editCompetitorMarketShare" step="0.1" value="25.0" required>
      </div>
      <div class="form-group">
        <label>威胁等级：</label>
        <select id="editCompetitorThreatLevel" required>
          <option value="高">高</option>
          <option value="中" selected>中</option>
          <option value="低">低</option>
        </select>
      </div>
      <div class="form-group">
        <label>市场定位：</label>
        <input type="text" id="editCompetitorPosition" value="挑战者" required>
      </div>
      <div class="form-group">
        <label>设备数量：</label>
        <input type="number" id="editCompetitorDeviceCount" value="8000" required>
      </div>
      <div class="form-group">
        <label>价格策略：</label>
        <input type="text" id="editCompetitorPriceStrategy" value="中端定位" required>
      </div>
    </form>
  `, [
    { text: '取消', class: 'secondary', onclick: 'closeModal()' },
    { text: '确定', class: 'primary', onclick: 'submitEditCompetitor()' }
  ]);
}

// 提交编辑竞争对手
function submitEditCompetitor() {
  const name = document.getElementById('editCompetitorName')?.value;
  const marketShare = document.getElementById('editCompetitorMarketShare')?.value;
  const threatLevel = document.getElementById('editCompetitorThreatLevel')?.value;
  const position = document.getElementById('editCompetitorPosition')?.value;
  const deviceCount = document.getElementById('editCompetitorDeviceCount')?.value;
  const priceStrategy = document.getElementById('editCompetitorPriceStrategy')?.value;
  
  if (!name || !marketShare || !threatLevel || !position || !deviceCount || !priceStrategy) {
    showToast('请填写所有必填字段', 'warning');
    return;
  }
  
  showToast('竞争对手信息更新成功', 'success');
  closeModal();
  loadPage('competitor-analysis');
}

// ========================================
// 财务分析功能
// ========================================

// 查看财务详情
function viewFinancialDetail(month) {
  showToast(`查看财务详情: ${month}`, 'info');
}

// 导出财务分段报告
function exportFinancialSegments() {
  showToast('财务分段报告导出成功！', 'success');
}

// 生成财务洞察
function generateFinancialInsights() {
  showToast('生成财务洞察...', 'info');
  setTimeout(() => {
    showToast('财务洞察生成完成', 'success');
  }, 2000);
}

// 刷新财务数据
function refreshFinancialData() {
  showToast('财务数据已刷新！', 'success');
  loadPage('financial-analysis');
}

// ========================================
// 维护工单功能
// ========================================

// 查看维护详情
function viewMaintenanceDetail(id) {
  showToast(`查看维护工单详情: ${id}`, 'info');
}

// 编辑维护工单
function editMaintenanceOrder(id) {
  showToast(`编辑维护工单: ${id}`, 'info');
}

// ========================================
// 兼容性函数
// ========================================

// 兼容旧的showMessage函数
function showMessage(message, type = 'info') {
  showToast(message, type);
}

// 兼容旧的图表初始化函数
function initRevenueChart() {
  initDashboardCharts();
}

function initDeviceStatusChart() {
  initDashboardCharts();
}

// ========================================
// 用户相关功能
// ========================================


// 显示用户资料
function showProfile() {
  const userInfo = mockData.userInfo || {};
  showModal('个人资料', `
    <div class="profile-info">
      <div class="profile-avatar">
        <div class="avatar-circle">${userInfo.name ? userInfo.name.charAt(0) : 'A'}</div>
      </div>
      <div class="profile-details">
        <div class="profile-item">
          <label>用户名：</label>
          <span>${userInfo.username || 'admin'}</span>
        </div>
        <div class="profile-item">
          <label>姓名：</label>
          <span>${userInfo.name || '管理员'}</span>
        </div>
        <div class="profile-item">
          <label>角色：</label>
          <span>系统管理员</span>
        </div>
        <div class="profile-item">
          <label>登录时间：</label>
          <span>${new Date().toLocaleString()}</span>
        </div>
      </div>
    </div>
  `, [
    { text: '取消', class: 'secondary', onclick: 'closeModal()' },
    { text: '保存', class: 'primary', onclick: 'saveProfile()' }
  ]);
}

// 编辑用户资料
function editProfile() {
  const userInfo = mockData.userInfo || {};
  showModal('编辑个人资料', `
    <form id="editProfileForm">
      <div class="form-group">
        <label>用户名：</label>
        <input type="text" id="editUsername" value="${userInfo.username || 'admin'}" readonly>
      </div>
      <div class="form-group">
        <label>姓名：</label>
        <input type="text" id="editName" value="${userInfo.name || '管理员'}" required>
      </div>
      <div class="form-group">
        <label>新密码：</label>
        <input type="password" id="newPassword" placeholder="留空则不修改">
      </div>
      <div class="form-group">
        <label>确认密码：</label>
        <input type="password" id="confirmPassword" placeholder="留空则不修改">
      </div>
    </form>
  `, [
    { text: '取消', class: 'secondary', onclick: 'closeModal()' },
    { text: '保存', class: 'primary', onclick: 'saveProfile()' }
  ]);
}

// 保存用户资料
function saveProfile() {
  const name = document.getElementById('editName')?.value;
  const newPassword = document.getElementById('newPassword')?.value;
  const confirmPassword = document.getElementById('confirmPassword')?.value;
  
  if (!name) {
    showToast('请输入姓名', 'error');
    return;
  }
  
  if (newPassword && newPassword !== confirmPassword) {
    showToast('两次输入的密码不一致', 'error');
    return;
  }
  
  // 更新用户信息
  if (mockData.userInfo) {
    mockData.userInfo.name = name;
    if (newPassword) {
      mockData.userInfo.password = newPassword;
    }
  }
  
  showToast('个人资料更新成功', 'success');
  closeModal();
  
  // 更新页面显示
  const userNameElement = document.querySelector('.user-name');
  if (userNameElement) {
    userNameElement.textContent = name;
  }
  
  const userAvatarElement = document.getElementById('userAvatar');
  if (userAvatarElement) {
    userAvatarElement.textContent = name.charAt(0);
  }
}

// 显示系统设置
function showSettings() {
  showModal('系统设置', `
    <div class="settings-container">
      <div class="settings-group">
        <h4>基本设置</h4>
        <div class="setting-item">
          <label>系统名称：</label>
          <input type="text" id="systemName" value="共享充电宝管理系统" class="setting-input">
        </div>
        <div class="setting-item">
          <label>数据刷新间隔：</label>
          <select class="setting-select">
            <option value="30">30秒</option>
            <option value="60" selected>1分钟</option>
            <option value="300">5分钟</option>
          </select>
        </div>
      </div>
      <div class="settings-group">
        <h4>通知设置</h4>
        <div class="setting-item">
          <label>
            <input type="checkbox" checked>
            启用邮件通知
          </label>
        </div>
        <div class="setting-item">
          <label>
            <input type="checkbox" checked>
            启用短信通知
          </label>
        </div>
      </div>
    </div>
  `, [
    { text: '取消', class: 'secondary', onclick: 'closeModal()' },
    { text: '保存设置', class: 'primary', onclick: 'saveSystemSettings()' }
  ]);
}

// 保存系统设置
function saveSystemSettings() {
  showToast('系统设置已保存', 'success');
  closeModal();
}

// 显示数据备份
function showDataBackup() {
  showModal('数据备份', `
    <div class="backup-container">
      <div class="backup-actions">
        <button class="btn btn-primary" onclick="createBackup()">
          <span>💾</span>
          创建备份
        </button>
        <button class="btn btn-secondary" onclick="restoreBackup()">
          <span>🔄</span>
          恢复备份
        </button>
      </div>
      <div class="backup-list">
        <h4>备份历史</h4>
        <div class="backup-item">
          <div class="backup-info">
            <div class="backup-name">backup_20240115_1430.zip</div>
            <div class="backup-date">2024-01-15 14:30</div>
            <div class="backup-size">2.5 MB</div>
          </div>
          <div class="backup-actions">
            <button class="btn btn-small btn-primary" onclick="downloadBackup('backup_20240115_1430.zip')">下载</button>
            <button class="btn btn-small btn-danger" onclick="deleteBackup('backup_20240115_1430.zip')">删除</button>
          </div>
        </div>
        <div class="backup-item">
          <div class="backup-info">
            <div class="backup-name">backup_20240114_0930.zip</div>
            <div class="backup-date">2024-01-14 09:30</div>
            <div class="backup-size">2.3 MB</div>
          </div>
          <div class="backup-actions">
            <button class="btn btn-small btn-primary" onclick="downloadBackup('backup_20240114_0930.zip')">下载</button>
            <button class="btn btn-small btn-danger" onclick="deleteBackup('backup_20240114_0930.zip')">删除</button>
          </div>
        </div>
      </div>
    </div>
  `, [
    { text: '关闭', class: 'secondary', onclick: 'closeModal()' }
  ]);
}

// 创建备份
function createBackup() {
  showToast('正在创建备份...', 'info');
  setTimeout(() => {
    showToast('备份创建成功', 'success');
  }, 2000);
}

// 下载备份
function downloadBackup(backupName) {
  showToast(`正在下载备份: ${backupName}`, 'info');
  // 模拟下载
  setTimeout(() => {
    showToast('备份下载完成', 'success');
  }, 1500);
}

// 恢复备份
function restoreBackup() {
  if (confirm('确定要恢复备份吗？这将覆盖当前数据。')) {
    showToast('正在恢复备份...', 'info');
    setTimeout(() => {
      showToast('备份恢复成功', 'success');
    }, 3000);
  }
}

// 删除备份
function deleteBackup(backupName) {
  if (confirm(`确定要删除备份 ${backupName} 吗？`)) {
    showToast('备份删除成功', 'success');
  }
}

// ========================================
// 图表更新功能
// ========================================

// 更新收入趋势图表
function updateRevenueChart() {
  if (typeof Chart === 'undefined' || typeof mockData === 'undefined') {
    return;
  }
  
  try {
    const revenueChart = window.chartInstances['revenueChart'];
    if (revenueChart) {
      const financial = mockData.financial || {};
      const revenueData = financial.revenue || [];
      
      revenueChart.data.labels = revenueData.map(item => item.month);
      revenueChart.data.datasets[0].data = revenueData.map(item => item.amount);
      revenueChart.update();
      
      showToast('收入趋势图表已更新！', 'success');
    }
  } catch (error) {
    console.error('更新收入趋势图表失败:', error);
  }
}

// 更新设备状态图表
function updateDeviceStatusChart() {
  if (typeof Chart === 'undefined' || typeof mockData === 'undefined') {
    return;
  }
  
  try {
    const deviceStatusChart = window.chartInstances['deviceStatusChart'];
    if (deviceStatusChart) {
      const devices = mockData.devices || [];
      
      const statusCount = {
        '在线': devices.filter(d => d.status === '在线').length,
        '离线': devices.filter(d => d.status === '离线').length,
        '故障': devices.filter(d => d.status === '故障').length
      };
      
      deviceStatusChart.data.labels = Object.keys(statusCount);
      deviceStatusChart.data.datasets[0].data = Object.values(statusCount);
      deviceStatusChart.update();
      
      showToast('设备状态图表已更新！', 'success');
    }
  } catch (error) {
    console.error('更新设备状态图表失败:', error);
  }
}

// 刷新设备数据
function refreshDevices() {
  showToast('设备列表已刷新！', 'info');
  loadPage('devices');
}

// 刷新点位数据
function refreshLocations() {
  showToast('点位列表已刷新！', 'info');
  loadPage('locations');
}

// 刷新维护数据
function refreshMaintenanceData() {
  showToast('维护数据已刷新！', 'info');
  loadPage('maintenance');
}

// 搜索设备
function searchDevices() {
  const searchTerm = document.getElementById('deviceSearch')?.value || '';
  if (!searchTerm) {
    showToast('请输入搜索关键词', 'warning');
    return;
  }
  
  showToast(`搜索"${searchTerm}"的结果已更新！`, 'success');
  filterDevices();
}

// 搜索点位
function searchLocations() {
  const searchTerm = document.getElementById('locationSearch')?.value || '';
  if (!searchTerm) {
    showToast('请输入搜索关键词', 'warning');
    return;
  }
  
  showToast(`搜索"${searchTerm}"的结果已更新！`, 'success');
  filterLocations();
}

// 清除设备搜索
function clearDeviceSearch() {
  const searchInput = document.getElementById('deviceSearch');
  if (searchInput) {
    searchInput.value = '';
  }
  filterDevices();
  showToast('搜索条件已清除', 'info');
}

// 清除点位搜索
function clearLocationSearch() {
  const searchInput = document.getElementById('locationSearch');
  if (searchInput) {
    searchInput.value = '';
  }
  filterLocations();
  showToast('搜索条件已清除', 'info');
}

// ========================================
// 用户行为分析功能
// ========================================

// 搜索用户
function searchUsers() {
  const searchTerm = document.getElementById('userSearch')?.value || '';
  if (!searchTerm) {
    showToast('请输入搜索关键词', 'warning');
    return;
  }
  
  showToast(`搜索"${searchTerm}"的结果已更新！`, 'success');
}

// 刷新用户行为数据
function refreshUserBehaviorData() {
  showToast('用户行为数据已刷新！', 'success');
  loadPage('user-behavior');
}

// 导出用户行为数据
function exportUserBehaviorData() {
  const userBehavior = mockData.userBehavior || {};
  const users = userBehavior.users || [];
  
  const exportData = users.map(user => ({
    '用户ID': user.id,
    '用户类型': user.type,
    '活跃度': user.activity,
    '使用次数': user.usageCount,
    '平均时长': `${user.avgTime}h`,
    '最后使用': user.lastUsage,
    '偏好时间': user.preferredTime
  }));
  
  downloadCSVFile(exportData, `用户行为数据_${new Date().toISOString().slice(0, 10)}`);
}

// 查看用户详情
function viewUserDetail(userId) {
  showToast(`查看用户详情: ${userId}`, 'info');
}

// 导出用户详情
function exportUserDetail(userId) {
  showToast(`用户详情导出成功: ${userId}`, 'success');
}

// ========================================
// 市场分析功能
// ========================================

// 刷新市场热点
function refreshMarketHotspots() {
  showToast('热门点位图表已更新！', 'success');
}

// 刷新高峰时段
function refreshPeakHours() {
  showToast('高峰时段图表已更新！', 'success');
}

// 刷新市场趋势
function refreshMarketTrend() {
  showToast('市场趋势图表已更新！', 'success');
}

// 查看热点详情
function viewHotspotDetail(hotspotId) {
  showToast(`查看热点详情: ${hotspotId}`, 'info');
}

// 导出热点详情
function exportHotspotDetail(hotspotId) {
  showToast(`热点详情导出成功: ${hotspotId}`, 'success');
}

// ========================================
// 竞争对手分析功能
// ========================================

// 添加竞争对手
function showAddCompetitorModal() {
  showModal('添加竞争对手', `
    <form id="addCompetitorForm">
      <div class="form-group">
        <label>竞争对手名称：</label>
        <input type="text" id="competitorName" required>
      </div>
      <div class="form-group">
        <label>市场份额：</label>
        <input type="number" id="competitorMarketShare" step="0.1" required>
      </div>
      <div class="form-group">
        <label>威胁等级：</label>
        <select id="competitorThreatLevel" required>
          <option value="高">高</option>
          <option value="中">中</option>
          <option value="低">低</option>
        </select>
      </div>
      <div class="form-group">
        <label>市场定位：</label>
        <input type="text" id="competitorPosition" required>
      </div>
      <div class="form-group">
        <label>设备数量：</label>
        <input type="number" id="competitorDeviceCount" required>
      </div>
      <div class="form-group">
        <label>价格策略：</label>
        <input type="text" id="competitorPriceStrategy" required>
      </div>
    </form>
  `, [
    { text: '取消', class: 'secondary', onclick: 'closeModal()' },
    { text: '确定', class: 'primary', onclick: 'submitAddCompetitor()' }
  ]);
}

// 提交添加竞争对手
function submitAddCompetitor() {
  const name = document.getElementById('competitorName')?.value;
  const marketShare = document.getElementById('competitorMarketShare')?.value;
  const threatLevel = document.getElementById('competitorThreatLevel')?.value;
  const position = document.getElementById('competitorPosition')?.value;
  const deviceCount = document.getElementById('competitorDeviceCount')?.value;
  const priceStrategy = document.getElementById('competitorPriceStrategy')?.value;
  
  if (!name || !marketShare || !threatLevel || !position || !deviceCount || !priceStrategy) {
    showToast('请填写所有必填字段', 'error');
    return;
  }
  
  showToast('竞争对手添加成功', 'success');
  closeModal();
  loadPage('competitor-analysis');
}

// 编辑竞争对手
function editCompetitor(name) {
  showModal('编辑竞争对手', `
    <form id="editCompetitorForm">
      <div class="form-group">
        <label>竞争对手名称：</label>
        <input type="text" id="editCompetitorName" value="${name}" required>
      </div>
      <div class="form-group">
        <label>市场份额：</label>
        <input type="number" id="editCompetitorMarketShare" step="0.1" value="25.0" required>
      </div>
      <div class="form-group">
        <label>威胁等级：</label>
        <select id="editCompetitorThreatLevel" required>
          <option value="高">高</option>
          <option value="中" selected>中</option>
          <option value="低">低</option>
        </select>
      </div>
      <div class="form-group">
        <label>市场定位：</label>
        <input type="text" id="editCompetitorPosition" value="挑战者" required>
      </div>
      <div class="form-group">
        <label>设备数量：</label>
        <input type="number" id="editCompetitorDeviceCount" value="8000" required>
      </div>
      <div class="form-group">
        <label>价格策略：</label>
        <input type="text" id="editCompetitorPriceStrategy" value="中端定位" required>
      </div>
    </form>
  `, [
    { text: '取消', class: 'secondary', onclick: 'closeModal()' },
    { text: '确定', class: 'primary', onclick: 'submitEditCompetitor()' }
  ]);
}

// 提交编辑竞争对手
function submitEditCompetitor() {
  const name = document.getElementById('editCompetitorName')?.value;
  const marketShare = document.getElementById('editCompetitorMarketShare')?.value;
  const threatLevel = document.getElementById('editCompetitorThreatLevel')?.value;
  const position = document.getElementById('editCompetitorPosition')?.value;
  const deviceCount = document.getElementById('editCompetitorDeviceCount')?.value;
  const priceStrategy = document.getElementById('editCompetitorPriceStrategy')?.value;
  
  if (!name || !marketShare || !threatLevel || !position || !deviceCount || !priceStrategy) {
    showToast('请填写所有必填字段', 'warning');
    return;
  }
  
  showToast('竞争对手信息更新成功', 'success');
  closeModal();
  loadPage('competitor-analysis');
}

// ========================================
// 财务分析功能
// ========================================

// 查看财务详情
function viewFinancialDetail(month) {
  showToast(`查看财务详情: ${month}`, 'info');
}

// 导出财务分段报告
function exportFinancialSegments() {
  showToast('财务分段报告导出成功！', 'success');
}

// 生成财务洞察
function generateFinancialInsights() {
  showToast('生成财务洞察...', 'info');
  setTimeout(() => {
    showToast('财务洞察生成完成', 'success');
  }, 2000);
}

// 刷新财务数据
function refreshFinancialData() {
  showToast('财务数据已刷新！', 'success');
  loadPage('financial-analysis');
}

// ========================================
// 维护工单功能
// ========================================

// 查看维护详情
function viewMaintenanceDetail(id) {
  showToast(`查看维护工单详情: ${id}`, 'info');
}

// 编辑维护工单
function editMaintenanceOrder(id) {
  showToast(`编辑维护工单: ${id}`, 'info');
}

// ========================================
// 兼容性函数
// ========================================

// 兼容旧的showMessage函数
function showMessage(message, type = 'info') {
  showToast(message, type);
}

// 兼容旧的图表初始化函数
function initRevenueChart() {
  initDashboardCharts();
}

function initDeviceStatusChart() {
  initDashboardCharts();
}

// ========================================
// 用户相关功能
// ========================================

// 显示用户资料
function showProfile() {
  const userInfo = mockData.userInfo || {};
  showModal('个人资料', `
    <div class="profile-info">
      <div class="profile-avatar">
        <div class="avatar-circle">${userInfo.name ? userInfo.name.charAt(0) : 'A'}</div>
      </div>
      <div class="profile-details">
        <div class="profile-item">
          <label>用户名：</label>
          <span>${userInfo.username || 'admin'}</span>
        </div>
        <div class="profile-item">
          <label>姓名：</label>
          <span>${userInfo.name || '管理员'}</span>
        </div>
        <div class="profile-item">
          <label>角色：</label>
          <span>系统管理员</span>
        </div>
        <div class="profile-item">
          <label>登录时间：</label>
          <span>${new Date().toLocaleString()}</span>
        </div>
      </div>
    </div>
  `, [
    { text: '取消', class: 'secondary', onclick: 'closeModal()' },
    { text: '保存', class: 'primary', onclick: 'saveProfile()' }
  ]);
}

// 编辑用户资料
function editProfile() {
  const userInfo = mockData.userInfo || {};
  showModal('编辑个人资料', `
    <form id="editProfileForm">
      <div class="form-group">
        <label>用户名：</label>
        <input type="text" id="editUsername" value="${userInfo.username || 'admin'}" readonly>
      </div>
      <div class="form-group">
        <label>姓名：</label>
        <input type="text" id="editName" value="${userInfo.name || '管理员'}" required>
      </div>
      <div class="form-group">
        <label>新密码：</label>
        <input type="password" id="newPassword" placeholder="留空则不修改">
      </div>
      <div class="form-group">
        <label>确认密码：</label>
        <input type="password" id="confirmPassword" placeholder="留空则不修改">
      </div>
    </form>
  `, [
    { text: '取消', class: 'secondary', onclick: 'closeModal()' },
    { text: '保存', class: 'primary', onclick: 'saveProfile()' }
  ]);
}

// 保存用户资料
function saveProfile() {
  const name = document.getElementById('editName')?.value;
  const newPassword = document.getElementById('newPassword')?.value;
  const confirmPassword = document.getElementById('confirmPassword')?.value;
  
  if (!name) {
    showToast('请输入姓名', 'error');
    return;
  }
  
  if (newPassword && newPassword !== confirmPassword) {
    showToast('两次输入的密码不一致', 'error');
    return;
  }
  
  // 更新用户信息
  if (mockData.userInfo) {
    mockData.userInfo.name = name;
    if (newPassword) {
      mockData.userInfo.password = newPassword;
    }
  }
  
  showToast('个人资料更新成功', 'success');
  closeModal();
  
  // 更新页面显示
  const userNameElement = document.querySelector('.user-name');
  if (userNameElement) {
    userNameElement.textContent = name;
  }
  
  const userAvatarElement = document.getElementById('userAvatar');
  if (userAvatarElement) {
    userAvatarElement.textContent = name.charAt(0);
  }
}

// 显示系统设置
function showSettings() {
  showModal('系统设置', `
    <div class="settings-container">
      <div class="settings-group">
        <h4>基本设置</h4>
        <div class="setting-item">
          <label>系统名称：</label>
          <input type="text" id="systemName" value="共享充电宝市场热点分析系统">
        </div>
        <div class="setting-item">
          <label>数据刷新间隔（秒）：</label>
          <input type="number" id="refreshInterval" value="30" min="10" max="300">
        </div>
      </div>
      
      <div class="settings-section">
        <h4>通知设置</h4>
        <div class="form-group">
          <label>
            <input type="checkbox" id="emailNotification" checked>
            启用邮件通知
          </label>
        </div>
        <div class="form-group">
          <label>
            <input type="checkbox" id="smsNotification">
            启用短信通知
          </label>
        </div>
      </div>
      
      <div class="settings-section">
        <h4>安全设置</h4>
        <div class="form-group">
          <label>会话超时时间（分钟）：</label>
          <input type="number" id="sessionTimeout" value="30" min="5" max="480">
        </div>
        <div class="form-group">
          <label>
            <input type="checkbox" id="twoFactorAuth">
            启用双因素认证
          </label>
        </div>
      </div>
    </div>
  `, [
    { text: '取消', class: 'secondary', onclick: 'closeModal()' },
    { text: '保存设置', class: 'primary', onclick: 'saveSettings()' },
    { text: '恢复默认', class: 'warning', onclick: 'resetSettings()' }
  ]);
}

// 保存系统设置
function saveSettings() {
  const systemName = document.getElementById('systemName')?.value;
  const refreshInterval = document.getElementById('refreshInterval')?.value;
  const chartTheme = document.getElementById('chartTheme')?.value;
  const emailNotification = document.getElementById('emailNotification')?.checked;
  const smsNotification = document.getElementById('smsNotification')?.checked;
  const browserNotification = document.getElementById('browserNotification')?.checked;
  const sessionTimeout = document.getElementById('sessionTimeout')?.value;
  const twoFactorAuth = document.getElementById('twoFactorAuth')?.checked;
  
  // 保存设置到localStorage
  const settings = {
    systemName,
    refreshInterval: parseInt(refreshInterval),
    chartTheme,
    emailNotification,
    smsNotification,
    browserNotification,
    sessionTimeout: parseInt(sessionTimeout),
    twoFactorAuth
  };
  
  localStorage.setItem('systemSettings', JSON.stringify(settings));
  
  showToast('系统设置保存成功', 'success');
  closeModal();
  
  // 应用设置
  applySettings(settings);
}

// 重置系统设置
function resetSettings() {
  const defaultSettings = {
    systemName: '共享充电宝市场热点分析系统',
    refreshInterval: 30,
    chartTheme: 'light',
    emailNotification: true,
    smsNotification: false,
    browserNotification: true,
    sessionTimeout: 30,
    twoFactorAuth: false
  };
  
  localStorage.setItem('systemSettings', JSON.stringify(defaultSettings));
  
  showToast('系统设置已恢复默认', 'success');
  closeModal();
  
  // 重新加载设置页面
  showSettings();
}

// 应用系统设置
function applySettings(settings) {
  // 更新页面标题
  if (settings.systemName) {
    document.title = settings.systemName;
  }
  
  // 应用图表主题
  if (settings.chartTheme === 'dark') {
    document.body.classList.add('dark-theme');
  } else {
    document.body.classList.remove('dark-theme');
  }
  
  // 设置数据刷新间隔
  if (window.dataRefreshInterval) {
    clearInterval(window.dataRefreshInterval);
  }
  
  if (settings.refreshInterval > 0) {
    window.dataRefreshInterval = setInterval(() => {
      refreshCurrentPageData();
    }, settings.refreshInterval * 1000);
  }
}

// 刷新当前页面数据
function refreshCurrentPageData() {
  const currentPage = window.currentPage || 'dashboard';
  
  switch (currentPage) {
    case 'dashboard':
      refreshDevices();
      break;
    case 'devices':
      refreshDevices();
      break;
    case 'locations':
      refreshLocations();
      break;
    case 'user-behavior':
      refreshUserBehaviorData();
      break;
    case 'market-analysis':
      refreshMarketHotspots();
      break;
    case 'competitor-analysis':
      // 竞争对手数据刷新
      showToast('竞争对手数据已刷新', 'success');
      break;
    case 'financial-analysis':
      refreshFinancialData();
      break;
    case 'maintenance':
      refreshMaintenanceData();
      break;
    case 'system':
      // 系统数据刷新
      showToast('系统数据已刷新', 'success');
      break;
  }
}

// ========================================
// 增强的搜索和筛选功能
// ========================================

// 高级搜索设备
function advancedSearchDevices() {
  showModal('高级搜索设备', `
    <form id="advancedDeviceSearchForm">
      <div class="form-group">
        <label>设备名称：</label>
        <input type="text" id="advDeviceName" placeholder="输入设备名称关键词">
      </div>
      <div class="form-group">
        <label>设备类型：</label>
        <select id="advDeviceType">
          <option value="">全部类型</option>
          <option value="标准型">标准型</option>
          <option value="快充型">快充型</option>
          <option value="无线型">无线型</option>
        </select>
      </div>
      <div class="form-group">
        <label>设备状态：</label>
        <select id="advDeviceStatus">
          <option value="">全部状态</option>
          <option value="在线">在线</option>
          <option value="离线">离线</option>
          <option value="故障">故障</option>
        </select>
      </div>
      <div class="form-group">
        <label>位置：</label>
        <input type="text" id="advDeviceLocation" placeholder="输入位置关键词">
      </div>
      <div class="form-group">
        <label>电量范围：</label>
        <div style="display: flex; gap: 8px; align-items: center;">
          <input type="number" id="advBatteryMin" placeholder="最小值" min="0" max="100" style="width: 80px;">
          <span>-</span>
          <input type="number" id="advBatteryMax" placeholder="最大值" min="0" max="100" style="width: 80px;">
        </div>
      </div>
      <div class="form-group">
        <label>使用次数范围：</label>
        <div style="display: flex; gap: 8px; align-items: center;">
          <input type="number" id="advUsageMin" placeholder="最小值" min="0" style="width: 80px;">
          <span>-</span>
          <input type="number" id="advUsageMax" placeholder="最大值" min="0" style="width: 80px;">
        </div>
      </div>
    </form>
  `, [
    { text: '取消', class: 'secondary', onclick: 'closeModal()' },
    { text: '搜索', class: 'primary', onclick: 'executeAdvancedDeviceSearch()' },
    { text: '重置', class: 'warning', onclick: 'resetAdvancedDeviceSearch()' }
  ]);
}

// 执行高级设备搜索
function executeAdvancedDeviceSearch() {
  const name = document.getElementById('advDeviceName')?.value.toLowerCase();
  const type = document.getElementById('advDeviceType')?.value;
  const status = document.getElementById('advDeviceStatus')?.value;
  const location = document.getElementById('advDeviceLocation')?.value.toLowerCase();
  const batteryMin = parseInt(document.getElementById('advBatteryMin')?.value) || 0;
  const batteryMax = parseInt(document.getElementById('advBatteryMax')?.value) || 100;
  const usageMin = parseInt(document.getElementById('advUsageMin')?.value) || 0;
  const usageMax = parseInt(document.getElementById('advUsageMax')?.value) || 999999;
  
  const devices = mockData.devices || [];
  const filteredDevices = devices.filter(device => {
    const nameMatch = !name || device.name.toLowerCase().includes(name);
    const typeMatch = !type || device.type === type;
    const statusMatch = !status || device.status === status;
    const locationMatch = !location || device.location.toLowerCase().includes(location);
    const batteryMatch = device.battery >= batteryMin && device.battery <= batteryMax;
    const usageMatch = device.usageCount >= usageMin && device.usageCount <= usageMax;
    
    return nameMatch && typeMatch && statusMatch && locationMatch && batteryMatch && usageMatch;
  });
  
  // 更新设备表格
  updateDevicesTable(filteredDevices);
  
  showToast(`找到 ${filteredDevices.length} 台设备`, 'success');
  closeModal();
}

// 重置高级设备搜索
function resetAdvancedDeviceSearch() {
  const form = document.getElementById('advancedDeviceSearchForm');
  if (form) {
    form.reset();
  }
}

// 更新设备表格
function updateDevicesTable(devices) {
  const tbody = document.getElementById('devicesTableBody');
  if (!tbody) return;
  
  tbody.innerHTML = devices.map(device => `
    <tr>
      <td>${device.id}</td>
      <td>${device.name}</td>
      <td>${device.type}</td>
      <td><span class="status-badge status-${device.status === '在线' ? 'online' : device.status === '离线' ? 'offline' : 'error'}">${device.status}</span></td>
      <td>${device.location}</td>
      <td>${device.group}</td>
      <td>${device.battery}%</td>
      <td>${device.usageCount}</td>
      <td>${device.lastUpdate}</td>
      <td>
        <button class="btn btn-small btn-secondary" onclick="editDevice(${device.id})">编辑</button>
        <button class="btn btn-small btn-danger" onclick="deleteDevice(${device.id})">删除</button>
      </td>
    </tr>
  `).join('');
}

// ========================================
// 数据导出功能增强
// ========================================

// 导出所有数据
function exportAllData() {
  const allData = {
    '设备数据': mockData.devices || [],
    '点位数据': mockData.locations || [],
    '用户行为数据': mockData.userBehavior?.users || [],
    '财务数据': mockData.financial?.monthlyData || [],
    '维护数据': mockData.maintenance?.orders || []
  };
  
  // 导出为JSON格式
  const jsonData = JSON.stringify(allData, null, 2);
  const blob = new Blob([jsonData], { type: 'application/json;charset=utf-8;' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.setAttribute('href', url);
  link.setAttribute('download', `全部数据_${new Date().toISOString().slice(0, 10)}.json`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
  
  showToast('全部数据导出成功', 'success');
}

// 下载JSON文件
function downloadJSON(data, filename) {
  const jsonString = JSON.stringify(data, null, 2);
  const blob = new Blob([jsonString], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `${filename}.json`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}

// 下载CSV文件
function downloadCSV(data, filename) {
  let csvContent = '';
  
  // 设备数据
  if (data.devices && data.devices.length > 0) {
    csvContent += '设备数据\n';
    csvContent += 'ID,名称,类型,状态,位置,分组,电量,使用次数,最后更新\n';
    data.devices.forEach(device => {
      csvContent += `${device.id},${device.name},${device.type},${device.status},${device.location},${device.group},${device.battery}%,${device.usageCount},${device.lastUpdate}\n`;
    });
    csvContent += '\n';
  }
  
  // 点位数据
  if (data.locations && data.locations.length > 0) {
    csvContent += '点位数据\n';
    csvContent += 'ID,名称,地址,区域,设备数量,使用率,收入,热度,状态\n';
    data.locations.forEach(location => {
      csvContent += `${location.id},${location.name},${location.address},${location.area},${location.deviceCount},${location.usageRate}%,${location.revenue},${location.heat},${location.status}\n`;
    });
    csvContent += '\n';
  }
  
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `${filename}.csv`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}

// 下载Excel文件（模拟）
function downloadExcel(data, filename) {
  // 由于浏览器限制，这里模拟Excel导出
  // 实际项目中可以使用SheetJS等库
  showToast('Excel导出功能需要额外的库支持，已生成CSV格式', 'info');
  downloadCSV(data, filename);
}

// ========================================
// 系统监控功能
// ========================================

// 显示系统监控
function showSystemMonitor() {
  const systemInfo = {
    cpu: Math.floor(Math.random() * 30) + 20,
    memory: Math.floor(Math.random() * 40) + 30,
    disk: Math.floor(Math.random() * 20) + 60,
    network: Math.floor(Math.random() * 50) + 20,
    onlineUsers: Math.floor(Math.random() * 20) + 5,
    activeConnections: Math.floor(Math.random() * 100) + 50
  };
  
  showModal('系统监控', `
    <div class="system-monitor">
      <div class="monitor-grid">
        <div class="monitor-item">
          <div class="monitor-label">CPU使用率</div>
          <div class="monitor-value">${systemInfo.cpu}%</div>
          <div class="monitor-bar">
            <div class="monitor-bar-fill" style="width: ${systemInfo.cpu}%"></div>
          </div>
        </div>
        <div class="monitor-item">
          <div class="monitor-label">内存使用率</div>
          <div class="monitor-value">${systemInfo.memory}%</div>
          <div class="monitor-bar">
            <div class="monitor-bar-fill" style="width: ${systemInfo.memory}%"></div>
          </div>
        </div>
        <div class="monitor-item">
          <div class="monitor-label">磁盘使用率</div>
          <div class="monitor-value">${systemInfo.disk}%</div>
          <div class="monitor-bar">
            <div class="monitor-bar-fill" style="width: ${systemInfo.disk}%"></div>
          </div>
        </div>
        <div class="monitor-item">
          <div class="monitor-label">网络使用率</div>
          <div class="monitor-value">${systemInfo.network}%</div>
          <div class="monitor-bar">
            <div class="monitor-bar-fill" style="width: ${systemInfo.network}%"></div>
          </div>
        </div>
      </div>
      
      <div class="monitor-stats">
        <div class="stat-item">
          <span class="stat-label">在线用户：</span>
          <span class="stat-value">${systemInfo.onlineUsers}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">活跃连接：</span>
          <span class="stat-value">${systemInfo.activeConnections}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">系统运行时间：</span>
          <span class="stat-value">${Math.floor(Math.random() * 24) + 1}天 ${Math.floor(Math.random() * 12) + 1}小时</span>
        </div>
      </div>
    </div>
  `, [
    { text: '关闭', class: 'secondary', onclick: 'closeModal()' },
    { text: '刷新', class: 'primary', onclick: 'showSystemMonitor()' },
    { text: '生成报告', class: 'info', onclick: 'generateSystemReport()' }
  ]);
}

// 生成系统报告
function generateSystemReport() {
  showToast('系统报告生成中...', 'info');
  
  setTimeout(() => {
    const reportData = {
      timestamp: new Date().toISOString(),
      systemStatus: '正常',
      performance: {
        cpu: '25%',
        memory: '45%',
        disk: '65%',
        network: '30%'
      },
      recommendations: [
        '建议定期清理临时文件',
        '监控内存使用情况',
        '检查磁盘空间',
        '优化数据库查询'
      ]
    };
    
    downloadJSON(reportData, `系统报告_${new Date().toISOString().slice(0, 10)}`);
    showToast('系统报告已生成并下载', 'success');
  }, 2000);
}

// ========================================
// 初始化系统设置
// ========================================

// 页面加载时初始化系统设置
document.addEventListener('DOMContentLoaded', function() {
  // 加载保存的系统设置
  const savedSettings = localStorage.getItem('systemSettings');
  if (savedSettings) {
    try {
      const settings = JSON.parse(savedSettings);
      applySettings(settings);
    } catch (error) {
      console.warn('加载系统设置失败:', error);
    }
  }
  
  // 初始化应用
  initApp();
});

// ========================================
// 缺失的功能函数补充
// ========================================

// 刷新财务数据
function refreshFinancialData() {
  // 模拟数据刷新
  const financial = mockData.financial || {};
  
  // 更新收入数据
  if (financial.dailyRevenue) {
    financial.dailyRevenue = financial.dailyRevenue.map(item => ({
      ...item,
      revenue: Math.floor(Math.random() * 1000) + 500
    }));
  }
  
  // 更新设备状态数据
  if (financial.deviceStatus) {
    financial.deviceStatus = {
      online: Math.floor(Math.random() * 50) + 30,
      offline: Math.floor(Math.random() * 20) + 10,
      maintenance: Math.floor(Math.random() * 10) + 5
    };
  }
  
  showToast('财务数据已刷新', 'success');
  
  // 重新渲染财务分析页面
  if (window.currentPage === 'financial-analysis') {
    renderFinancialAnalysis();
  }
}

// 刷新竞争对手数据
function refreshCompetitorData() {
  // 模拟竞争对手数据更新
  const competitors = mockData.competitors || [];
  
  competitors.forEach(competitor => {
    competitor.marketShare = (Math.random() * 20 + 10).toFixed(1);
    competitor.deviceCount = Math.floor(Math.random() * 1000) + 500;
    competitor.avgRating = (Math.random() * 2 + 3).toFixed(1);
  });
  
  showToast('竞争对手数据已刷新', 'success');
  
  // 重新渲染竞争对手分析页面
  if (window.currentPage === 'competitor-analysis') {
    renderCompetitorAnalysis();
  }
}

// 刷新系统数据
function refreshSystemData() {
  // 模拟系统数据更新
  const systemLogs = mockData.systemLogs || [];
  
  // 添加新的系统日志
  const newLog = {
    id: Date.now(),
    timestamp: new Date().toISOString(),
    level: ['INFO', 'WARNING', 'ERROR'][Math.floor(Math.random() * 3)],
    message: '系统数据自动刷新完成',
    module: 'SystemMonitor'
  };
  
  systemLogs.unshift(newLog);
  
  // 保持日志数量在合理范围内
  if (systemLogs.length > 100) {
    systemLogs.splice(100);
  }
  
  showToast('系统数据已刷新', 'success');
  
  // 重新渲染系统管理页面
  if (window.currentPage === 'system') {
    renderSystem();
  }
}

// 批量操作设备
function batchOperationDevices(operation) {
  const checkboxes = document.querySelectorAll('#devicesTable input[type="checkbox"]:checked');
  const deviceIds = Array.from(checkboxes).map(cb => cb.value).filter(id => id !== 'all');
  
  if (deviceIds.length === 0) {
    showToast('请选择要操作的设备', 'error');
    return;
  }
  
  const operationText = {
    'enable': '启用',
    'disable': '禁用',
    'delete': '删除',
    'maintenance': '维护'
  }[operation] || '操作';
  
  showModal('确认操作', `
    <p>确定要${operationText}选中的 ${deviceIds.length} 台设备吗？</p>
    <p class="text-warning">此操作不可撤销！</p>
  `, [
    { text: '取消', class: 'secondary', onclick: 'closeModal()' },
    { text: '确认', class: 'danger', onclick: `executeBatchOperation('${operation}', [${deviceIds.join(',')}])` }
  ]);
}

// 执行批量操作
function executeBatchOperation(operation, deviceIds) {
  const devices = mockData.devices || [];
  
  deviceIds.forEach(id => {
    const device = devices.find(d => d.id === parseInt(id));
    if (device) {
      switch (operation) {
        case 'enable':
          device.status = '在线';
          break;
        case 'disable':
          device.status = '离线';
          break;
        case 'delete':
          const index = devices.indexOf(device);
          if (index > -1) {
            devices.splice(index, 1);
          }
          break;
        case 'maintenance':
          device.status = '维护中';
          break;
      }
    }
  });
  
  const operationText = {
    'enable': '启用',
    'disable': '禁用',
    'delete': '删除',
    'maintenance': '维护'
  }[operation] || '操作';
  
  showToast(`已${operationText} ${deviceIds.length} 台设备`, 'success');
  closeModal();
  
  // 重新渲染设备页面
  if (window.currentPage === 'devices') {
    renderDevices();
  }
}

// 设备详情查看
function viewDeviceDetail(deviceId) {
  const device = mockData.devices?.find(d => d.id === deviceId);
  if (!device) {
    showToast('设备不存在', 'error');
    return;
  }
  
  showModal('设备详情', `
    <div class="device-detail">
      <div class="detail-section">
        <h4>基本信息</h4>
        <div class="detail-grid">
          <div class="detail-item">
            <label>设备ID：</label>
            <span>${device.id}</span>
          </div>
          <div class="detail-item">
            <label>设备名称：</label>
            <span>${device.name}</span>
          </div>
          <div class="detail-item">
            <label>设备类型：</label>
            <span>${device.type}</span>
          </div>
          <div class="detail-item">
            <label>设备状态：</label>
            <span class="status-badge status-${device.status === '在线' ? 'online' : device.status === '离线' ? 'offline' : 'error'}">${device.status}</span>
          </div>
          <div class="detail-item">
            <label>当前位置：</label>
            <span>${device.location}</span>
          </div>
          <div class="detail-item">
            <label>设备分组：</label>
            <span>${device.group}</span>
          </div>
        </div>
      </div>
      
      <div class="detail-section">
        <h4>运行状态</h4>
        <div class="detail-grid">
          <div class="detail-item">
            <label>当前电量：</label>
            <span>${device.battery}%</span>
          </div>
          <div class="detail-item">
            <label>使用次数：</label>
            <span>${device.usageCount}</span>
          </div>
          <div class="detail-item">
            <label>最后更新：</label>
            <span>${device.lastUpdate}</span>
          </div>
          <div class="detail-item">
            <label>运行时长：</label>
            <span>${Math.floor(Math.random() * 1000) + 100}小时</span>
          </div>
        </div>
      </div>
      
      <div class="detail-section">
        <h4>维护记录</h4>
        <div class="maintenance-history">
          <div class="history-item">
            <span class="history-date">2024-01-15</span>
            <span class="history-action">定期维护</span>
            <span class="history-status">已完成</span>
          </div>
          <div class="history-item">
            <span class="history-date">2024-01-01</span>
            <span class="history-action">电池更换</span>
            <span class="history-status">已完成</span>
          </div>
        </div>
      </div>
    </div>
  `, [
    { text: '关闭', class: 'secondary', onclick: 'closeModal()' },
    { text: '编辑设备', class: 'primary', onclick: `editDevice(${deviceId}); closeModal();` },
    { text: '查看历史', class: 'info', onclick: 'viewDeviceHistory()' }
  ]);
}

// 查看设备历史
function viewDeviceHistory() {
  showModal('设备使用历史', `
    <div class="history-chart">
      <canvas id="deviceHistoryChart" width="400" height="200"></canvas>
    </div>
    <div class="history-stats">
      <div class="stat-item">
        <span class="stat-label">总使用次数：</span>
        <span class="stat-value">1,234</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">平均使用时长：</span>
        <span class="stat-value">2.5小时</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">故障次数：</span>
        <span class="stat-value">3次</span>
      </div>
    </div>
  `, [
    { text: '关闭', class: 'secondary', onclick: 'closeModal()' },
    { text: '导出历史', class: 'primary', onclick: 'exportDeviceHistory()' }
  ]);
  
  // 初始化历史图表
  setTimeout(() => {
    const ctx = document.getElementById('deviceHistoryChart');
    if (ctx) {
      new Chart(ctx, {
        type: 'line',
        data: {
          labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
          datasets: [{
            label: '使用次数',
            data: [120, 150, 180, 200, 220, 250],
            borderColor: 'rgb(75, 192, 192)',
            tension: 0.1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false
        }
      });
    }
  }, 100);
}

// 导出设备历史
function exportDeviceHistory() {
  const historyData = {
    deviceId: 'DEV001',
    exportTime: new Date().toISOString(),
    history: [
      { date: '2024-01-15', action: '定期维护', status: '已完成' },
      { date: '2024-01-01', action: '电池更换', status: '已完成' }
    ]
  };
  
  downloadJSON(historyData, `设备历史_DEV001_${new Date().toISOString().slice(0, 10)}`);
  showToast('设备历史已导出', 'success');
}

// 系统健康检查
function systemHealthCheck() {
  showToast('系统健康检查中...', 'info');
  
  setTimeout(() => {
    const healthStatus = {
      database: Math.random() > 0.1 ? '正常' : '异常',
      network: Math.random() > 0.05 ? '正常' : '异常',
      storage: Math.random() > 0.15 ? '正常' : '异常',
      security: Math.random() > 0.02 ? '正常' : '异常'
    };
    
    const allHealthy = Object.values(healthStatus).every(status => status === '正常');
    
    showModal('系统健康检查', `
      <div class="health-check">
        <div class="health-status ${allHealthy ? 'healthy' : 'warning'}">
          <i class="ri-${allHealthy ? 'check-line' : 'alert-line'}"></i>
          <span>系统状态：${allHealthy ? '健康' : '需要关注'}</span>
        </div>
        
        <div class="health-grid">
          <div class="health-item ${healthStatus.database === '正常' ? 'healthy' : 'error'}">
            <div class="health-label">数据库</div>
            <div class="health-value">${healthStatus.database}</div>
          </div>
          <div class="health-item ${healthStatus.network === '正常' ? 'healthy' : 'error'}">
            <div class="health-label">网络连接</div>
            <div class="health-value">${healthStatus.network}</div>
          </div>
          <div class="health-item ${healthStatus.storage === '正常' ? 'healthy' : 'error'}">
            <div class="health-label">存储空间</div>
            <div class="health-value">${healthStatus.storage}</div>
          </div>
          <div class="health-item ${healthStatus.security === '正常' ? 'healthy' : 'error'}">
            <div class="health-label">安全状态</div>
            <div class="health-value">${healthStatus.security}</div>
          </div>
        </div>
        
        <div class="health-recommendations">
          <h4>建议操作：</h4>
          <ul>
            <li>定期备份重要数据</li>
            <li>监控系统资源使用情况</li>
            <li>更新安全补丁</li>
            <li>检查网络连接稳定性</li>
          </ul>
        </div>
      </div>
    `, [
      { text: '关闭', class: 'secondary', onclick: 'closeModal()' },
      { text: '生成报告', class: 'primary', onclick: 'generateHealthReport()' },
      { text: '自动修复', class: 'warning', onclick: 'autoFixIssues()' }
    ]);
  }, 2000);
}

// 生成健康报告
function generateHealthReport() {
  const report = {
    timestamp: new Date().toISOString(),
    systemHealth: '良好',
    recommendations: [
      '建议定期备份重要数据',
      '监控系统资源使用情况',
      '更新安全补丁',
      '检查网络连接稳定性'
    ]
  };
  
  downloadJSON(report, `系统健康报告_${new Date().toISOString().slice(0, 10)}`);
  showToast('健康报告已生成', 'success');
}

// 自动修复问题
function autoFixIssues() {
  showToast('正在自动修复系统问题...', 'info');
  
  setTimeout(() => {
    showToast('系统问题修复完成', 'success');
    closeModal();
  }, 3000);
}

// 数据备份管理
function manageDataBackup() {
  const backups = [
    { name: 'backup_2024_01_15.zip', size: '256MB', date: '2024-01-15 10:30:00', status: '完成' },
    { name: 'backup_2024_01_14.zip', size: '255MB', date: '2024-01-14 10:30:00', status: '完成' },
    { name: 'backup_2024_01_13.zip', size: '254MB', date: '2024-01-13 10:30:00', status: '完成' }
  ];
  
  showModal('数据备份管理', `
    <div class="backup-management">
      <div class="backup-actions">
        <button class="btn btn-primary" onclick="createBackup()">创建备份</button>
        <button class="btn btn-info" onclick="scheduleBackup()">设置定时备份</button>
      </div>
      
      <div class="backup-list">
        <h4>备份历史</h4>
        <table class="table">
          <thead>
            <tr>
              <th>备份名称</th>
              <th>大小</th>
              <th>创建时间</th>
              <th>状态</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            ${backups.map(backup => `
              <tr>
                <td>${backup.name}</td>
                <td>${backup.size}</td>
                <td>${backup.date}</td>
                <td><span class="status-badge status-online">${backup.status}</span></td>
                <td>
                  <button class="btn btn-small btn-secondary" onclick="downloadBackup('${backup.name}')">下载</button>
                  <button class="btn btn-small btn-warning" onclick="restoreBackup('${backup.name}')">恢复</button>
                  <button class="btn btn-small btn-danger" onclick="deleteBackup('${backup.name}')">删除</button>
                </td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    </div>
  `, [
    { text: '关闭', class: 'secondary', onclick: 'closeModal()' }
  ]);
}

// 设置定时备份
function scheduleBackup() {
  showModal('设置定时备份', `
    <form id="backupScheduleForm">
      <div class="form-group">
        <label>备份频率：</label>
        <select id="backupFrequency">
          <option value="daily">每日</option>
          <option value="weekly">每周</option>
          <option value="monthly">每月</option>
        </select>
      </div>
      <div class="form-group">
        <label>备份时间：</label>
        <input type="time" id="backupTime" value="02:00">
      </div>
      <div class="form-group">
        <label>保留备份数量：</label>
        <input type="number" id="backupRetention" value="7" min="1" max="30">
      </div>
      <div class="form-group">
        <label>
          <input type="checkbox" id="backupEnabled" checked>
          启用定时备份
        </label>
      </div>
    </form>
  `, [
    { text: '取消', class: 'secondary', onclick: 'closeModal()' },
    { text: '保存设置', class: 'primary', onclick: 'saveBackupSchedule()' }
  ]);
}

// 保存备份计划
function saveBackupSchedule() {
  const frequency = document.getElementById('backupFrequency')?.value;
  const time = document.getElementById('backupTime')?.value;
  const retention = document.getElementById('backupRetention')?.value;
  const enabled = document.getElementById('backupEnabled')?.checked;
  
  const schedule = {
    frequency,
    time,
    retention: parseInt(retention),
    enabled
  };
  
  localStorage.setItem('backupSchedule', JSON.stringify(schedule));
  
  showToast('定时备份设置已保存', 'success');
  closeModal();
}

// ========================================
// 用户管理功能
// ========================================

// 显示添加用户模态框
function showAddUserModal() {
  showModal('添加用户', `
    <form id="addUserForm">
      <div class="form-group">
        <label>用户名：</label>
        <input type="text" id="newUsername" required>
      </div>
      <div class="form-group">
        <label>密码：</label>
        <input type="password" id="newPassword" required>
      </div>
      <div class="form-group">
        <label>确认密码：</label>
        <input type="password" id="confirmNewPassword" required>
      </div>
      <div class="form-group">
        <label>角色：</label>
        <select id="newUserRole" required>
          <option value="">请选择角色</option>
          <option value="admin">管理员</option>
          <option value="operator">操作员</option>
          <option value="viewer">查看者</option>
        </select>
      </div>
      <div class="form-group">
        <label>邮箱：</label>
        <input type="email" id="newUserEmail">
      </div>
      <div class="form-group">
        <label>手机号：</label>
        <input type="tel" id="newUserPhone">
      </div>
    </form>
  `, [
    { text: '取消', class: 'secondary', onclick: 'closeModal()' },
    { text: '添加', class: 'primary', onclick: 'submitAddUser()' }
  ]);
}

// 提交添加用户
function submitAddUser() {
  const username = document.getElementById('newUsername')?.value;
  const password = document.getElementById('newPassword')?.value;
  const confirmPassword = document.getElementById('confirmNewPassword')?.value;
  const role = document.getElementById('newUserRole')?.value;
  const email = document.getElementById('newUserEmail')?.value;
  const phone = document.getElementById('newUserPhone')?.value;
  
  if (!username || !password || !role) {
    showToast('请填写必填字段', 'error');
    return;
  }
  
  if (password !== confirmPassword) {
    showToast('两次输入的密码不一致', 'error');
    return;
  }
  
  // 添加用户到系统数据
  const system = mockData.system || {};
  const users = system.users || [];
  
  const newUser = {
    id: `USER${Date.now()}`,
    username,
    role,
    status: 'active',
    lastLogin: '从未登录',
    email,
    phone,
    createTime: new Date().toISOString()
  };
  
  users.push(newUser);
  system.users = users;
  
  showToast('用户添加成功', 'success');
  closeModal();
  
  // 重新渲染系统页面
  if (window.currentPage === 'system') {
    renderSystem();
  }
}

// 刷新用户数据
function refreshUserData() {
  // 模拟刷新用户数据
  const system = mockData.system || {};
  const users = system.users || [];
  
  users.forEach(user => {
    if (Math.random() > 0.8) {
      user.lastLogin = new Date().toLocaleString();
    }
  });
  
  showToast('用户数据已刷新', 'success');
  
  // 重新渲染系统页面
  if (window.currentPage === 'system') {
    renderSystem();
  }
}

// 切换全选用户
function toggleSelectAllUsers() {
  const selectAll = document.getElementById('selectAllUsers');
  const checkboxes = document.querySelectorAll('.user-checkbox');
  
  checkboxes.forEach(checkbox => {
    checkbox.checked = selectAll.checked;
  });
}

// 批量操作用户
function batchOperationUsers(operation) {
  const checkboxes = document.querySelectorAll('.user-checkbox:checked');
  const userIds = Array.from(checkboxes).map(cb => cb.value);
  
  if (userIds.length === 0) {
    showToast('请选择要操作的用户', 'error');
    return;
  }
  
  const operationText = {
    'enable': '启用',
    'disable': '禁用',
    'delete': '删除'
  }[operation] || '操作';
  
  showModal('确认操作', `
    <p>确定要${operationText}选中的 ${userIds.length} 个用户吗？</p>
    <p class="text-warning">此操作不可撤销！</p>
  `, [
    { text: '取消', class: 'secondary', onclick: 'closeModal()' },
    { text: '确认', class: 'danger', onclick: `executeBatchUserOperation('${operation}', [${userIds.map(id => `'${id}'`).join(',')}])` }
  ]);
}

// 执行批量用户操作
function executeBatchUserOperation(operation, userIds) {
  const system = mockData.system || {};
  const users = system.users || [];
  
  userIds.forEach(userId => {
    const user = users.find(u => u.id === userId);
    if (user) {
      switch (operation) {
        case 'enable':
          user.status = 'active';
          break;
        case 'disable':
          user.status = 'inactive';
          break;
        case 'delete':
          const index = users.indexOf(user);
          if (index > -1) {
            users.splice(index, 1);
          }
          break;
      }
    }
  });
  
  const operationText = {
    'enable': '启用',
    'disable': '禁用',
    'delete': '删除'
  }[operation] || '操作';
  
  showToast(`已${operationText} ${userIds.length} 个用户`, 'success');
  closeModal();
  
  // 重新渲染系统页面
  if (window.currentPage === 'system') {
    renderSystem();
  }
}

// ========================================
// 系统日志管理功能
// ========================================

// 筛选系统日志
function filterSystemLogs() {
  const levelFilter = document.getElementById('logLevelFilter')?.value;
  const systemLogs = mockData.systemLogs || [];
  
  const filteredLogs = levelFilter ? 
    systemLogs.filter(log => log.level === levelFilter) : 
    systemLogs;
  
  // 更新日志表格显示
  const tbody = document.querySelector('.system-container .data-table tbody');
  if (tbody) {
    tbody.innerHTML = filteredLogs.slice(0, 50).map(log => `
      <tr class="log-level-${log.level.toLowerCase()}">
        <td>${new Date(log.timestamp).toLocaleString()}</td>
        <td><span class="status-badge status-${log.level === 'ERROR' ? 'error' : log.level === 'WARNING' ? 'warning' : 'info'}">${log.level}</span></td>
        <td>${log.module}</td>
        <td>${log.message}</td>
        <td>
          <button class="btn btn-small btn-info" onclick="viewLogDetail('${log.id}')" title="查看详情">
            <i class="ri-eye-line"></i>
          </button>
        </td>
      </tr>
    `).join('');
  }
  
  showToast(`显示 ${filteredLogs.length} 条日志`, 'info');
}

// 查看日志详情
function viewLogDetail(logId) {
  const systemLogs = mockData.systemLogs || [];
  const log = systemLogs.find(l => l.id === logId);
  
  if (!log) {
    showToast('日志不存在', 'error');
    return;
  }
  
  showModal('日志详情', `
    <div class="log-detail">
      <div class="detail-item">
        <label>日志ID：</label>
        <span>${log.id}</span>
      </div>
      <div class="detail-item">
        <label>时间：</label>
        <span>${new Date(log.timestamp).toLocaleString()}</span>
      </div>
      <div class="detail-item">
        <label>级别：</label>
        <span class="status-badge status-${log.level === 'ERROR' ? 'error' : log.level === 'WARNING' ? 'warning' : 'info'}">${log.level}</span>
      </div>
      <div class="detail-item">
        <label>模块：</label>
        <span>${log.module}</span>
      </div>
      <div class="detail-item">
        <label>消息：</label>
        <span>${log.message}</span>
      </div>
    </div>
  `, [
    { text: '关闭', class: 'secondary', onclick: 'closeModal()' },
    { text: '导出此日志', class: 'primary', onclick: `exportLogDetail('${logId}')` }
  ]);
}

// 导出日志详情
function exportLogDetail(logId) {
  const systemLogs = mockData.systemLogs || [];
  const log = systemLogs.find(l => l.id === logId);
  
  if (log) {
    downloadJSON(log, `日志详情_${logId}_${new Date().toISOString().slice(0, 10)}`);
    showToast('日志详情已导出', 'success');
  }
}

// 清空系统日志
function clearSystemLogs() {
  showModal('确认清空', `
    <p>确定要清空所有系统日志吗？</p>
    <p class="text-warning">此操作不可撤销！</p>
  `, [
    { text: '取消', class: 'secondary', onclick: 'closeModal()' },
    { text: '确认清空', class: 'danger', onclick: 'executeClearSystemLogs()' }
  ]);
}

// 执行清空系统日志
function executeClearSystemLogs() {
  mockData.systemLogs = [];
  
  showToast('系统日志已清空', 'success');
  closeModal();
  
  // 重新渲染系统页面
  if (window.currentPage === 'system') {
    renderSystem();
  }
}

// ========================================
// 数据概览页面专用函数
// ========================================

// 刷新收入图表
function refreshRevenueChart() {
  showToast('正在刷新收入数据...', 'info');
  setTimeout(() => {
    if (typeof initDashboardCharts === 'function') {
      initDashboardCharts();
      showToast('收入数据已刷新', 'success');
    }
  }, 1000);
}

// 导出收入数据
function exportRevenueData() {
  const financial = mockData.financial || {};
  const monthlyData = financial.monthlyData || [];
  
  const exportData = monthlyData.map(month => ({
    '月份': month.month,
    '收入': month.revenue,
    '成本': month.cost,
    '利润': month.profit,
    '利润率': `${month.margin}%`,
    '订单数': month.orders
  }));
  
  downloadCSVFile(exportData, `收入数据_${new Date().toISOString().slice(0, 10)}`);
}

// 刷新设备状态图表
function refreshDeviceStatusChart() {
  showToast('正在刷新设备状态数据...', 'info');
  setTimeout(() => {
    if (typeof initDashboardCharts === 'function') {
      initDashboardCharts();
      showToast('设备状态数据已刷新', 'success');
    }
  }, 1000);
}

// 导出设备状态数据
function exportDeviceStatusData() {
  const devices = mockData.devices || [];
  
  const exportData = devices.map(device => ({
    '设备ID': device.id,
    '设备名称': device.name,
    '状态': device.status,
    '位置': device.location,
    '电量': `${device.battery}%`,
    '最后更新': device.lastUpdate
  }));
  
  downloadCSVFile(exportData, `设备状态数据_${new Date().toISOString().slice(0, 10)}`);
}

// 显示统计详情
function showStatDetail(statType) {
  try {
    // 将dailyRevenue变量移到函数开头，避免作用域问题
    const dailyRevenue = mockData.financial?.dailyRevenue || [];
    
    let title = '';
    let content = '';
    
    switch (statType) {
      case 'revenue':
        title = '收入详情';
        // 计算本月累计收入 - 使用dailyRevenue数组
        const monthlyRevenue = dailyRevenue.reduce((sum, item) => sum + (item.revenue || 0), 0);
        
        content = `
          <div class="stat-detail-content">
            <div class="detail-item">
              <span class="detail-label">今日收入：</span>
              <span class="detail-value">¥${(dailyRevenue[dailyRevenue.length - 1]?.revenue || 0).toLocaleString()}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">昨日收入：</span>
              <span class="detail-value">¥${(dailyRevenue[dailyRevenue.length - 2]?.revenue || 0).toLocaleString()}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">本月累计：</span>
              <span class="detail-value">¥${monthlyRevenue.toLocaleString()}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">同比增长：</span>
              <span class="detail-value success">+12.5%</span>
            </div>
          </div>
        `;
        break;
      case 'orders':
        title = '订单详情';
        // 计算本月累计订单 - 使用dailyRevenue数组
        const monthlyOrders = dailyRevenue.reduce((sum, item) => sum + (item.orders || 0), 0);
        
        content = `
          <div class="stat-detail-content">
            <div class="detail-item">
              <span class="detail-label">今日订单：</span>
              <span class="detail-value">${(dailyRevenue[dailyRevenue.length - 1]?.orders || 0).toLocaleString()}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">昨日订单：</span>
              <span class="detail-value">${(dailyRevenue[dailyRevenue.length - 2]?.orders || 0).toLocaleString()}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">本月累计：</span>
              <span class="detail-value">${monthlyOrders.toLocaleString()}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">平均客单价：</span>
              <span class="detail-value">¥${(mockData.financial?.averageOrderValue || 10).toLocaleString()}</span>
            </div>
          </div>
        `;
        break;
      case 'devices':
        title = '设备详情';
        content = `
          <div class="stat-detail-content">
            <div class="detail-item">
              <span class="detail-label">总设备数：</span>
              <span class="detail-value">${(mockData.devices || []).length}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">在线设备：</span>
              <span class="detail-value success">${(mockData.devices || []).filter(d => d.status === '在线').length}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">离线设备：</span>
              <span class="detail-value warning">${(mockData.devices || []).filter(d => d.status === '离线').length}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">故障设备：</span>
              <span class="detail-value error">${(mockData.devices || []).filter(d => d.status === '故障').length}</span>
            </div>
          </div>
        `;
        break;
      case 'users':
        title = '用户详情';
        content = `
          <div class="stat-detail-content">
            <div class="detail-item">
              <span class="detail-label">总用户数：</span>
              <span class="detail-value">${(mockData.userBehavior?.totalUsers || 0).toLocaleString()}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">活跃用户：</span>
              <span class="detail-value">${(mockData.userBehavior?.activeUsers || 0).toLocaleString()}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">新增用户：</span>
              <span class="detail-value success">+${(mockData.userBehavior?.newUsers || 0).toLocaleString()}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">用户留存率：</span>
              <span class="detail-value">${(mockData.userBehavior?.retentionRate || 0)}%</span>
            </div>
          </div>
        `;
        break;
      default:
        console.error('未知的统计类型:', statType);
        showToast('未知的统计类型', 'error');
        return;
    }
    
    showModal(title, content, [
      {
        text: '关闭',
        class: 'secondary',
        onclick: 'closeModal()'
      },
      {
        text: '导出数据',
        class: 'primary',
        onclick: () => {
          exportStatData(statType);
          closeModal();
        }
      }
    ]);
    
  } catch (error) {
    console.error('showStatDetail函数出错:', error);
    showToast('显示统计详情时出错: ' + error.message, 'error');
  }
}

// 导出统计数据
function exportStatData(statType) {
  let data = [];
  let filename = '';
  
  switch (statType) {
    case 'revenue':
      data = mockData.financial?.revenue || [];
      filename = '收入数据';
      break;
    case 'orders':
      data = mockData.financial?.dailyRevenue || [];
      filename = '订单数据';
      break;
    case 'devices':
      data = mockData.devices || [];
      filename = '设备数据';
      break;
    case 'users':
      data = mockData.userBehavior?.dailyStats || [];
      filename = '用户数据';
      break;
  }
  
  downloadJSON(data, filename);
  showToast(`${filename}已导出`, 'success');
}

// 绑定统计卡片事件
function bindStatCardEvents() {
  // 先移除现有的事件监听器
  const statCards = document.querySelectorAll('.stat-card');
  statCards.forEach(card => {
    const newCard = card.cloneNode(true);
    card.parentNode.replaceChild(newCard, card);
  });
  
  // 重新获取元素并绑定事件
  const newStatCards = document.querySelectorAll('.stat-card');
  
  newStatCards.forEach((card, index) => {
    // 确定统计类型
    const statType = card.classList.contains('revenue') ? 'revenue' :
                    card.classList.contains('orders') ? 'orders' :
                    card.classList.contains('devices') ? 'devices' :
                    card.classList.contains('users') ? 'users' : '';
    
    card.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      
      if (statType) {
        showStatDetail(statType);
      } else {
        console.warn('无法确定统计类型');
      }
    });
    
    // 添加鼠标悬停效果
    card.style.cursor = 'pointer';
  });
}

// 更新初始化函数
function initDashboardCharts() {
  if (typeof Chart === 'undefined') {
    console.error('Chart.js 未加载');
    return;
  }
  
  if (typeof mockData === 'undefined') {
    console.error('模拟数据未加载');
    return;
  }
  
  try {
    // 收入趋势图表
    const revenueCtx = document.getElementById('revenueChart');
    
    if (revenueCtx) {
      destroyChart('revenueChart');
      const financial = mockData.financial || {};
      const revenueData = financial.revenue || [];
      
      if (revenueData.length === 0) {
        console.warn('收入数据为空，显示空状态');
        const chartContainer = revenueCtx.closest('.chart-container');
        if (chartContainer) {
          chartContainer.classList.add('empty');
          chartContainer.innerHTML = '<div>暂无收入数据</div>';
        }
      } else {
        // 移除空状态类
        const chartContainer = revenueCtx.closest('.chart-container');
        if (chartContainer) {
          chartContainer.classList.remove('empty');
        }
        
        window.chartInstances['revenueChart'] = new Chart(revenueCtx, {
          type: 'line',
          data: {
            labels: revenueData.map(item => item.month),
            datasets: [{
              label: '收入',
              data: revenueData.map(item => item.amount),
              borderColor: '#0052d9',
              backgroundColor: 'rgba(0, 82, 217, 0.1)',
              tension: 0.4,
              borderWidth: 3,
              pointBackgroundColor: '#0052d9',
              pointBorderColor: '#fff',
              pointBorderWidth: 2,
              pointRadius: 6,
              pointHoverRadius: 8,
              fill: true
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
              intersect: false,
              mode: 'index'
            },
            plugins: {
              legend: {
                display: false
              },
              tooltip: {
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                titleColor: '#000000',
                bodyColor: '#666666',
                borderColor: '#e7e7e7',
                borderWidth: 1,
                cornerRadius: 8,
                displayColors: false,
                padding: 12,
                titleFont: {
                  size: 14,
                  weight: '600'
                },
                bodyFont: {
                  size: 13
                },
                callbacks: {
                  label: function(context) {
                    return '收入: ¥' + context.parsed.y.toLocaleString();
                  }
                }
              }
            },
            scales: {
              y: {
                beginAtZero: true,
                grid: {
                  color: 'rgba(0, 0, 0, 0.05)',
                  drawBorder: false
                },
                ticks: {
                  color: '#666666',
                  font: {
                    size: 12
                  },
                  callback: function(value) {
                    return '¥' + value.toLocaleString();
                  }
                }
              },
              x: {
                grid: {
                  color: 'rgba(0, 0, 0, 0.05)',
                  drawBorder: false
                },
                ticks: {
                  color: '#666666',
                  font: {
                    size: 12
                  }
                }
              }
            },
            animation: {
              duration: 1000,
              easing: 'easeInOutQuart',
              onComplete: function() {
                const chartContainer = revenueCtx.closest('.chart-container');
                if (chartContainer) {
                  chartContainer.classList.add('loaded');
                }
              }
            }
          }
        });
      }
    }
    
    // 设备状态分布图表
    const deviceStatusCtx = document.getElementById('deviceStatusChart');
    
    if (deviceStatusCtx) {
      destroyChart('deviceStatusChart');
      const devices = mockData.devices || [];
      
      if (devices.length === 0) {
        console.warn('设备数据为空，显示空状态');
        const chartContainer = deviceStatusCtx.closest('.chart-container');
        if (chartContainer) {
          chartContainer.classList.add('empty');
          chartContainer.innerHTML = '<div>暂无设备数据</div>';
        }
      } else {
        // 移除空状态类
        const chartContainer = deviceStatusCtx.closest('.chart-container');
        if (chartContainer) {
          chartContainer.classList.remove('empty');
        }
        
        const statusCount = {
          '在线': devices.filter(d => d.status === '在线').length,
          '离线': devices.filter(d => d.status === '离线').length,
          '故障': devices.filter(d => d.status === '故障').length
        };
        
        window.chartInstances['deviceStatusChart'] = new Chart(deviceStatusCtx, {
          type: 'doughnut',
          data: {
            labels: Object.keys(statusCount),
            datasets: [{
              data: Object.values(statusCount),
              backgroundColor: [
                '#00a870',
                '#ed7b2f',
                '#d54941'
              ],
              borderWidth: 0,
              hoverOffset: 8,
              borderColor: '#ffffff',
              cutout: '60%'
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            layout: {
              padding: 20
            },
            plugins: {
              legend: {
                position: 'bottom',
                labels: {
                  color: '#666666',
                  padding: 20,
                  usePointStyle: true,
                  pointStyle: 'circle',
                  font: {
                    size: 13,
                    weight: '500'
                  },
                  generateLabels: function(chart) {
                    const data = chart.data;
                    if (data.labels.length && data.datasets.length) {
                      return data.labels.map((label, i) => {
                        const dataset = data.datasets[0];
                        const value = dataset.data[i];
                        const total = dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = ((value / total) * 100).toFixed(1);
                        
                        return {
                          text: `${label}: ${value}台 (${percentage}%)`,
                          fillStyle: dataset.backgroundColor[i],
                          strokeStyle: dataset.backgroundColor[i],
                          lineWidth: 0,
                          pointStyle: 'circle',
                          hidden: false,
                          index: i
                        };
                      });
                    }
                    return [];
                  }
                }
              },
              tooltip: {
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                titleColor: '#000000',
                bodyColor: '#666666',
                borderColor: '#e7e7e7',
                borderWidth: 1,
                cornerRadius: 8,
                padding: 12,
                titleFont: {
                  size: 14,
                  weight: '600'
                },
                bodyFont: {
                  size: 13
                },
                callbacks: {
                  label: function(context) {
                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                    const percentage = ((context.parsed / total) * 100).toFixed(1);
                    return `${context.label}: ${context.parsed}台 (${percentage}%)`;
                  }
                }
              }
            },
            animation: {
              duration: 1000,
              easing: 'easeInOutQuart',
              onComplete: function() {
                console.log('设备状态图表加载完成');
                const chartContainer = deviceStatusCtx.closest('.chart-container');
                if (chartContainer) {
                  chartContainer.classList.add('loaded');
                }
              }
            }
          }
        });
      }
    }
    
    // 绑定统计卡片事件
    setTimeout(() => {
      bindStatCardEvents();
    }, 100);
    
    // 优化图表响应式处理
    optimizeChartResponsiveness();
    
  } catch (error) {
    console.error('初始化数据概览图表失败:', error);
  }
}

// ========================================
// 图表工具函数
// ========================================

// 防抖函数
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 优化图表响应式处理
function optimizeChartResponsiveness() {
  const handleResize = debounce(() => {
    Object.values(window.chartInstances).forEach(chart => {
      if (chart && typeof chart.resize === 'function') {
        chart.resize();
      }
    });
  }, 250); // 250ms 防抖延迟
  
  // 移除之前的监听器（如果存在）
  window.removeEventListener('resize', handleResize);
  // 添加新的监听器
  window.addEventListener('resize', handleResize);
  
  return handleResize;
}

// ========================================
// 全局变量初始化
// ========================================

function handleModalButton(buttonText, buttonType) {
  if (window.modalButtons) {
    const button = window.modalButtons.find(btn => btn.text === buttonText);
    if (button && button.onclick) {
      // 如果是字符串，直接执行
      if (typeof button.onclick === 'string') {
        eval(button.onclick);
      } else if (typeof button.onclick === 'function') {
        button.onclick();
      }
    }
  }
}

// 测试统计卡片功能
function testStatCards() {
  const statCards = document.querySelectorAll('.stat-card');
  console.log('找到统计卡片数量:', statCards.length);
  
  statCards.forEach((card, index) => {
    console.log(`统计卡片 ${index + 1}:`, card.className);
  });
}

// 渲染用户行为分析页面
function renderUserBehavior() {
  if (typeof mockData === 'undefined') {
    return '<div class="error-message">数据加载失败，请刷新页面重试</div>';
  }

  const userBehavior = mockData.userBehavior || {};
  
  return `
    <div class="user-behavior-container">
      <!-- 页面标题和工具栏 -->
      <div class="page-header">
        <div class="page-title">
          <h2>用户行为分析</h2>
          <p>深入分析用户使用习惯和行为模式，优化用户体验</p>
        </div>
        <div class="page-actions">
          <button class="btn btn-secondary" onclick="refreshUserBehaviorData()">
            <i class="ri-refresh-line"></i>刷新数据
          </button>
          <button class="btn btn-primary" onclick="exportUserBehaviorData()">
            <i class="ri-download-line"></i>导出报告
          </button>
        </div>
      </div>

      <!-- 统计概览 -->
      <div class="stats-grid">
        <div class="stat-card" onclick="showUserDetail('total')">
          <div class="stat-header">
            <div class="stat-icon users">
              <i class="ri-user-line"></i>
            </div>
            <div class="stat-trend up">
              <i class="ri-arrow-up-line"></i>
              +15.2%
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">${(userBehavior.totalUsers || 0).toLocaleString()}</div>
            <div class="stat-label">总用户数</div>
            <div class="stat-subtitle">较上月增长 15.2%</div>
          </div>
        </div>
        <div class="stat-card" onclick="showUserDetail('active')">
          <div class="stat-header">
            <div class="stat-icon active">
              <i class="ri-fire-line"></i>
            </div>
            <div class="stat-trend up">
              <i class="ri-arrow-up-line"></i>
              +8.7%
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">${(userBehavior.activeUsers || 0).toLocaleString()}</div>
            <div class="stat-label">活跃用户</div>
            <div class="stat-subtitle">日活跃用户数</div>
          </div>
        </div>
        <div class="stat-card" onclick="showUserDetail('time')">
          <div class="stat-header">
            <div class="stat-icon time">
              <i class="ri-time-line"></i>
            </div>
            <div class="stat-trend up">
              <i class="ri-arrow-up-line"></i>
              +12.3%
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">${userBehavior.avgUsageTime || 0}h</div>
            <div class="stat-label">平均使用时长</div>
            <div class="stat-subtitle">单次使用平均时长</div>
          </div>
        </div>
        <div class="stat-card" onclick="showUserDetail('satisfaction')">
          <div class="stat-header">
            <div class="stat-icon satisfaction">
              <i class="ri-heart-line"></i>
            </div>
            <div class="stat-trend up">
              <i class="ri-arrow-up-line"></i>
              +5.8%
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">${userBehavior.satisfaction || 0}%</div>
            <div class="stat-label">用户满意度</div>
            <div class="stat-subtitle">基于用户评价</div>
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="charts-grid">
        <div class="chart-card">
          <div class="chart-header">
            <h3>用户活跃度趋势</h3>
            <div class="chart-actions">
              <button class="btn btn-small btn-secondary" onclick="refreshUserActivityChart()">
                <i class="ri-refresh-line"></i>
              </button>
              <button class="btn btn-small btn-secondary" onclick="exportUserActivityData()">
                <i class="ri-download-line"></i>
              </button>
            </div>
          </div>
          <canvas id="userActivityChart" width="400" height="200"></canvas>
        </div>
        <div class="chart-card">
          <div class="chart-header">
            <h3>用户画像分布</h3>
            <div class="chart-actions">
              <button class="btn btn-small btn-secondary" onclick="refreshUserProfileChart()">
                <i class="ri-refresh-line"></i>
              </button>
              <button class="btn btn-small btn-secondary" onclick="exportUserProfileData()">
                <i class="ri-download-line"></i>
              </button>
            </div>
          </div>
          <canvas id="userProfileChart" width="400" height="200"></canvas>
        </div>
        <div class="chart-card">
          <div class="chart-header">
            <h3>使用时段分布</h3>
            <div class="chart-actions">
              <button class="btn btn-small btn-secondary" onclick="refreshUsageTimeChart()">
                <i class="ri-refresh-line"></i>
              </button>
              <button class="btn btn-small btn-secondary" onclick="exportUsageTimeData()">
                <i class="ri-download-line"></i>
              </button>
            </div>
          </div>
          <canvas id="usageTimeChart" width="400" height="200"></canvas>
        </div>
        <div class="chart-card">
          <div class="chart-header">
            <h3>用户留存率</h3>
            <div class="chart-actions">
              <button class="btn btn-small btn-secondary" onclick="refreshRetentionChart()">
                <i class="ri-refresh-line"></i>
              </button>
              <button class="btn btn-small btn-secondary" onclick="exportRetentionData()">
                <i class="ri-download-line"></i>
              </button>
            </div>
          </div>
          <canvas id="retentionChart" width="400" height="200"></canvas>
        </div>
      </div>

      <!-- 用户列表工具栏 -->
      <div class="table-toolbar">
        <div class="toolbar-left">
          <div class="search-box">
            <i class="ri-search-line"></i>
            <input type="text" placeholder="搜索用户ID、类型..." onkeyup="searchUsers(this.value)">
          </div>
          <div class="filter-group">
            <select onchange="filterUsersByType(this.value)">
              <option value="">所有用户类型</option>
              <option value="个人用户">个人用户</option>
              <option value="企业用户">企业用户</option>
              <option value="学生用户">学生用户</option>
            </select>
            <select onchange="filterUsersByActivity(this.value)">
              <option value="">所有活跃度</option>
              <option value="高">高活跃度</option>
              <option value="中">中活跃度</option>
              <option value="低">低活跃度</option>
            </select>
          </div>
        </div>
        <div class="toolbar-right">
          <button class="btn btn-secondary" onclick="batchExportUsers()">
            <i class="ri-download-line"></i>批量导出
          </button>
          <button class="btn btn-primary" onclick="showAddUserModal()">
            <i class="ri-add-line"></i>添加用户
          </button>
        </div>
      </div>

      <!-- 用户列表 -->
      <div class="table-container">
        <table class="data-table">
          <thead>
            <tr>
              <th>
                <input type="checkbox" onchange="toggleSelectAllUsers(this.checked)">
              </th>
              <th>用户ID</th>
              <th>用户类型</th>
              <th>活跃度</th>
              <th>使用次数</th>
              <th>平均时长</th>
              <th>最后使用</th>
              <th>偏好时间</th>
              <th>满意度</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            ${(userBehavior.users || []).map(user => `
              <tr>
                <td>
                  <input type="checkbox" value="${user.id}" onchange="toggleUserSelection(this)">
                </td>
                <td>${user.id}</td>
                <td>
                  <span class="user-type-badge user-type-${user.type === '个人用户' ? 'personal' : user.type === '企业用户' ? 'business' : 'student'}">
                    ${user.type}
                  </span>
                </td>
                <td>
                  <span class="activity-badge activity-${user.activity === '高' ? 'high' : user.activity === '中' ? 'medium' : 'low'}">
                    ${user.activity}
                  </span>
                </td>
                <td>${user.usageCount}</td>
                <td>${user.avgTime}h</td>
                <td>${user.lastUsage}</td>
                <td>${user.preferredTime}</td>
                <td>
                  <div class="satisfaction-rating">
                    <span class="rating-stars">
                      ${'★'.repeat(Math.floor(user.satisfaction / 20))}${'☆'.repeat(5 - Math.floor(user.satisfaction / 20))}
                    </span>
                    <span class="rating-text">${user.satisfaction}%</span>
                  </div>
                </td>
                <td>
                  <div class="table-actions">
                    <button class="btn btn-small btn-secondary" onclick="viewUserDetail('${user.id}')" title="查看详情">
                      <i class="ri-eye-line"></i>
                    </button>
                    <button class="btn btn-small btn-secondary" onclick="editUser('${user.id}')" title="编辑">
                      <i class="ri-edit-line"></i>
                    </button>
                    <button class="btn btn-small btn-danger" onclick="deleteUser('${user.id}')" title="删除">
                      <i class="ri-delete-bin-line"></i>
                    </button>
                  </div>
                </td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>

      <!-- 分页控件 -->
      <div class="pagination">
        <div class="pagination-info">
          显示 1-10 条，共 ${(userBehavior.users || []).length} 条记录
        </div>
        <div class="pagination-controls">
          <button class="btn btn-small btn-secondary" disabled>上一页</button>
          <button class="btn btn-small btn-primary">1</button>
          <button class="btn btn-small btn-secondary">2</button>
          <button class="btn btn-small btn-secondary">3</button>
          <button class="btn btn-small btn-secondary">下一页</button>
        </div>
      </div>
    </div>
  `;
}

// 渲染市场热点分析页面
function renderMarketAnalysis() {
  if (typeof mockData === 'undefined') {
    return '<div class="error-message">数据加载失败，请刷新页面重试</div>';
  }

  const marketAnalysis = mockData.marketAnalysis || {};
  
  // 处理高峰时段数据 - 显示使用量最高的前3个时段
  const peakHours = marketAnalysis.peakHours || [];
  const topPeakHours = peakHours
    .sort((a, b) => b.usage - a.usage)
    .slice(0, 3)
    .map(item => item.hour);
  const peakHoursText = topPeakHours.length > 0 ? 
    topPeakHours.join(', ') : 
    '暂无数据';
  
  return `
    <div class="market-analysis-container">
      <!-- 页面标题和工具栏 -->
      <div class="page-header">
        <div class="page-title">
          <h2>市场热点分析</h2>
          <p>分析市场热点区域和使用趋势，优化设备布局策略</p>
        </div>
        <div class="page-actions">
          <button class="btn btn-secondary" onclick="refreshMarketData()">
            <i class="ri-refresh-line"></i>刷新数据
          </button>
          <button class="btn btn-primary" onclick="exportMarketReport()">
            <i class="ri-download-line"></i>导出报告
          </button>
        </div>
      </div>

      <!-- 统计概览 -->
      <div class="stats-grid">
        <div class="stat-card" onclick="showHotspotDetail('overview')">
          <div class="stat-header">
            <div class="stat-icon hotspots">
              <i class="ri-fire-line"></i>
            </div>
            <div class="stat-trend up">
              <i class="ri-arrow-up-line"></i>
              +23.5%
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">${marketAnalysis.hotSpots || 0}</div>
            <div class="stat-label">热点区域</div>
            <div class="stat-subtitle">较上月增长 23.5%</div>
          </div>
        </div>
        <div class="stat-card" onclick="showPeakHoursDetail()">
          <div class="stat-header">
            <div class="stat-icon peak">
              <i class="ri-time-line"></i>
            </div>
            <div class="stat-trend up">
              <i class="ri-arrow-up-line"></i>
              +18.2%
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">${peakHoursText}</div>
            <div class="stat-label">高峰时段</div>
            <div class="stat-subtitle">使用量最高的时段</div>
          </div>
        </div>
        <div class="stat-card" onclick="showGrowthDetail()">
          <div class="stat-header">
            <div class="stat-icon growth">
              <i class="ri-trending-up-line"></i>
            </div>
            <div class="stat-trend up">
              <i class="ri-arrow-up-line"></i>
              +${marketAnalysis.growthRate || 0}%
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">${marketAnalysis.growthRate || 0}%</div>
            <div class="stat-label">市场增长率</div>
            <div class="stat-subtitle">较去年同期</div>
          </div>
        </div>
        <div class="stat-card" onclick="showCompetitionDetail()">
          <div class="stat-header">
            <div class="stat-icon competition">
              <i class="ri-sword-line"></i>
            </div>
            <div class="stat-trend down">
              <i class="ri-arrow-down-line"></i>
              -5.3%
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">${marketAnalysis.competitionIndex || 0}</div>
            <div class="stat-label">竞争指数</div>
            <div class="stat-subtitle">较上月下降 5.3%</div>
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="charts-grid">
        <div class="chart-card">
          <div class="chart-header">
            <h3>高峰时段分析</h3>
            <div class="chart-actions">
              <button class="btn btn-small btn-secondary" onclick="refreshPeakHoursChart()">
                <i class="ri-refresh-line"></i>
              </button>
              <button class="btn btn-small btn-secondary" onclick="exportPeakHoursData()">
                <i class="ri-download-line"></i>
              </button>
            </div>
          </div>
          <canvas id="peakHoursChart" width="400" height="200"></canvas>
        </div>
        <div class="chart-card">
          <div class="chart-header">
            <h3>热点设备排行</h3>
            <div class="chart-actions">
              <button class="btn btn-small btn-secondary" onclick="refreshHotDevicesChart()">
                <i class="ri-refresh-line"></i>
              </button>
              <button class="btn btn-small btn-secondary" onclick="exportHotDevicesData()">
                <i class="ri-download-line"></i>
              </button>
            </div>
          </div>
          <canvas id="hotDevicesChart" width="400" height="200"></canvas>
        </div>
        <div class="chart-card">
          <div class="chart-header">
            <h3>区域热度地图</h3>
            <div class="chart-actions">
              <button class="btn btn-small btn-secondary" onclick="refreshHeatMap()">
                <i class="ri-refresh-line"></i>
              </button>
              <button class="btn btn-small btn-secondary" onclick="exportHeatMapData()">
                <i class="ri-download-line"></i>
              </button>
            </div>
          </div>
          <canvas id="heatMapChart" width="400" height="200"></canvas>
        </div>
        <div class="chart-card">
          <div class="chart-header">
            <h3>市场趋势预测</h3>
            <div class="chart-actions">
              <button class="btn btn-small btn-secondary" onclick="refreshTrendPrediction()">
                <i class="ri-refresh-line"></i>
              </button>
              <button class="btn btn-small btn-secondary" onclick="exportTrendData()">
                <i class="ri-download-line"></i>
              </button>
            </div>
          </div>
          <canvas id="trendPredictionChart" width="400" height="200"></canvas>
        </div>
      </div>

      <!-- 热点区域列表工具栏 -->
      <div class="table-toolbar">
        <div class="toolbar-left">
          <div class="search-box">
            <i class="ri-search-line"></i>
            <input type="text" placeholder="搜索区域名称..." onkeyup="searchHotspots(this.value)">
          </div>
          <div class="filter-group">
            <select onchange="filterHotspotsByHeat(this.value)">
              <option value="">所有热度等级</option>
              <option value="高">高热度</option>
              <option value="中">中热度</option>
              <option value="低">低热度</option>
            </select>
            <select onchange="filterHotspotsByRevenue(this.value)">
              <option value="">所有收入范围</option>
              <option value="high">高收入 (>¥10000)</option>
              <option value="medium">中等收入 (¥5000-10000)</option>
              <option value="low">低收入 (<¥5000)</option>
            </select>
          </div>
        </div>
        <div class="toolbar-right">
          <button class="btn btn-secondary" onclick="batchExportHotspots()">
            <i class="ri-download-line"></i>批量导出
          </button>
          <button class="btn btn-primary" onclick="showAddHotspotModal()">
            <i class="ri-add-line"></i>添加热点
          </button>
        </div>
      </div>

      <!-- 热点区域列表 -->
      <div class="table-container">
        <table class="data-table">
          <thead>
            <tr>
              <th>
                <input type="checkbox" onchange="toggleSelectAllHotspots(this.checked)">
              </th>
              <th>排名</th>
              <th>区域名称</th>
              <th>设备数量</th>
              <th>热度指数</th>
              <th>收入</th>
              <th>使用率</th>
              <th>增长率</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            ${(marketAnalysis.hotLocations || []).map((location, index) => `
              <tr>
                <td>
                  <input type="checkbox" value="${location.name}" onchange="toggleHotspotSelection(this)">
                </td>
                <td>
                  <div class="ranking-badge ranking-${index < 3 ? 'top' : 'normal'}">
                    ${index + 1}
                  </div>
                </td>
                <td>
                  <div class="location-info">
                    <div class="location-name">${location.name}</div>
                    <div class="location-address">${location.address || '地址信息'}</div>
                  </div>
                </td>
                <td>${location.devices}</td>
                <td>
                  <div class="heat-indicator">
                    <div class="heat-bar">
                      <div class="heat-fill" style="width: ${location.heat}%"></div>
                    </div>
                    <span class="heat-value">${location.heat}</span>
                  </div>
                </td>
                <td>
                  <div class="revenue-info">
                    <div class="revenue-amount">¥${location.revenue.toLocaleString()}</div>
                    <div class="revenue-trend up">+${Math.floor(Math.random() * 20 + 5)}%</div>
                  </div>
                </td>
                <td>
                  <div class="usage-rate">
                    <div class="usage-bar">
                      <div class="usage-fill" style="width: ${Math.floor(Math.random() * 40 + 60)}%"></div>
                    </div>
                    <span class="usage-value">${Math.floor(Math.random() * 40 + 60)}%</span>
                  </div>
                </td>
                <td>
                  <span class="growth-badge growth-positive">+${Math.floor(Math.random() * 30 + 10)}%</span>
                </td>
                <td>
                  <div class="table-actions">
                    <button class="btn btn-small btn-secondary" onclick="viewHotspotDetail('${location.name}')" title="查看详情">
                      <i class="ri-eye-line"></i>
                    </button>
                    <button class="btn btn-small btn-secondary" onclick="editHotspot('${location.name}')" title="编辑">
                      <i class="ri-edit-line"></i>
                    </button>
                    <button class="btn btn-small btn-secondary" onclick="analyzeHotspot('${location.name}')" title="分析">
                      <i class="ri-bar-chart-line"></i>
                    </button>
                    <button class="btn btn-small btn-danger" onclick="deleteHotspot('${location.name}')" title="删除">
                      <i class="ri-delete-bin-line"></i>
                    </button>
                  </div>
                </td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>

      <!-- 分页控件 -->
      <div class="pagination">
        <div class="pagination-info">
          显示 1-10 条，共 ${(marketAnalysis.hotLocations || []).length} 条记录
        </div>
        <div class="pagination-controls">
          <button class="btn btn-small btn-secondary" disabled>上一页</button>
          <button class="btn btn-small btn-primary">1</button>
          <button class="btn btn-small btn-secondary">2</button>
          <button class="btn btn-small btn-secondary">3</button>
          <button class="btn btn-small btn-secondary">下一页</button>
        </div>
      </div>
    </div>
  `;
}

// 渲染竞争对手分析页面
function renderCompetitorAnalysis() {
  if (typeof mockData === 'undefined') {
    return '<div class="error-message">数据加载失败，请刷新页面重试</div>';
  }

  const competitorAnalysis = mockData.competitorAnalysis || {};
  
  return `
    <div class="competitor-analysis-container">
      <!-- 页面标题和工具栏 -->
      <div class="page-header">
        <div class="page-title">
          <h2>竞争对手分析</h2>
          <p>深入分析竞争对手策略，制定差异化竞争方案</p>
        </div>
        <div class="page-actions">
          <button class="btn btn-secondary" onclick="refreshCompetitorData()">
            <i class="ri-refresh-line"></i>刷新数据
          </button>
          <button class="btn btn-primary" onclick="exportCompetitorReport()">
            <i class="ri-download-line"></i>导出报告
          </button>
        </div>
      </div>

      <!-- 统计概览 -->
      <div class="stats-grid">
        <div class="stat-card" onclick="showMarketShareDetail()">
          <div class="stat-header">
            <div class="stat-icon market-share">
              <i class="ri-pie-chart-line"></i>
            </div>
            <div class="stat-trend up">
              <i class="ri-arrow-up-line"></i>
              +8.5%
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">${competitorAnalysis.marketShare || 0}%</div>
            <div class="stat-label">市场份额</div>
            <div class="stat-subtitle">较上月增长 8.5%</div>
          </div>
        </div>
        <div class="stat-card" onclick="showCompetitorCountDetail()">
          <div class="stat-header">
            <div class="stat-icon competitors">
              <i class="ri-building-line"></i>
            </div>
            <div class="stat-trend up">
              <i class="ri-arrow-up-line"></i>
              +2
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">${competitorAnalysis.competitorCount || 0}</div>
            <div class="stat-label">竞争对手</div>
            <div class="stat-subtitle">新增 2 个竞争对手</div>
          </div>
        </div>
        <div class="stat-card" onclick="showAdvantageDetail()">
          <div class="stat-header">
            <div class="stat-icon advantage">
              <i class="ri-star-line"></i>
            </div>
            <div class="stat-trend up">
              <i class="ri-arrow-up-line"></i>
              +12.3%
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">${competitorAnalysis.advantage || 0}</div>
            <div class="stat-label">竞争优势</div>
            <div class="stat-subtitle">较上月提升 12.3%</div>
          </div>
        </div>
        <div class="stat-card" onclick="showThreatDetail()">
          <div class="stat-header">
            <div class="stat-icon threat">
              <i class="ri-error-warning-line"></i>
            </div>
            <div class="stat-trend down">
              <i class="ri-arrow-down-line"></i>
              -15.7%
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">${competitorAnalysis.threatLevel || 0}</div>
            <div class="stat-label">威胁等级</div>
            <div class="stat-subtitle">较上月下降 15.7%</div>
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="charts-grid">
        <div class="chart-card">
          <div class="chart-header">
            <h3>市场份额分布</h3>
            <div class="chart-actions">
              <button class="btn btn-small btn-secondary" onclick="refreshMarketShareChart()">
                <i class="ri-refresh-line"></i>
              </button>
              <button class="btn btn-small btn-secondary" onclick="exportMarketShareData()">
                <i class="ri-download-line"></i>
              </button>
            </div>
          </div>
          <canvas id="marketShareChart" width="400" height="200"></canvas>
        </div>
        <div class="chart-card">
          <div class="chart-header">
            <h3>竞争态势分析</h3>
            <div class="chart-actions">
              <button class="btn btn-small btn-secondary" onclick="refreshCompetitiveAnalysis()">
                <i class="ri-refresh-line"></i>
              </button>
              <button class="btn btn-small btn-secondary" onclick="exportCompetitiveData()">
                <i class="ri-download-line"></i>
              </button>
            </div>
          </div>
          <canvas id="competitiveAnalysisChart" width="400" height="200"></canvas>
        </div>
        <div class="chart-card">
          <div class="chart-header">
            <h3>价格策略对比</h3>
            <div class="chart-actions">
              <button class="btn btn-small btn-secondary" onclick="refreshPriceComparison()">
                <i class="ri-refresh-line"></i>
              </button>
              <button class="btn btn-small btn-secondary" onclick="exportPriceData()">
                <i class="ri-download-line"></i>
              </button>
            </div>
          </div>
          <canvas id="priceComparisonChart" width="400" height="200"></canvas>
        </div>
        <div class="chart-card">
          <div class="chart-header">
            <h3>威胁趋势预测</h3>
            <div class="chart-actions">
              <button class="btn btn-small btn-secondary" onclick="refreshThreatPrediction()">
                <i class="ri-refresh-line"></i>
              </button>
              <button class="btn btn-small btn-secondary" onclick="exportThreatData()">
                <i class="ri-download-line"></i>
              </button>
            </div>
          </div>
          <canvas id="threatPredictionChart" width="400" height="200"></canvas>
        </div>
      </div>

      <!-- 竞争对手列表工具栏 -->
      <div class="table-toolbar">
        <div class="toolbar-left">
          <div class="search-box">
            <i class="ri-search-line"></i>
            <input type="text" placeholder="搜索竞争对手名称..." onkeyup="searchCompetitors(this.value)">
          </div>
          <div class="filter-group">
            <select onchange="filterCompetitorsByThreat(this.value)">
              <option value="">所有威胁等级</option>
              <option value="高">高威胁</option>
              <option value="中">中威胁</option>
              <option value="低">低威胁</option>
            </select>
            <select onchange="filterCompetitorsByMarketShare(this.value)">
              <option value="">所有市场份额</option>
              <option value="high">高份额 (>20%)</option>
              <option value="medium">中等份额 (10-20%)</option>
              <option value="low">低份额 (<10%)</option>
            </select>
          </div>
        </div>
        <div class="toolbar-right">
          <button class="btn btn-secondary" onclick="batchExportCompetitors()">
            <i class="ri-download-line"></i>批量导出
          </button>
          <button class="btn btn-primary" onclick="showAddCompetitorModal()">
            <i class="ri-add-line"></i>添加竞争对手
          </button>
        </div>
      </div>

      <!-- 竞争对手列表 -->
      <div class="table-container">
        <table class="data-table">
          <thead>
            <tr>
              <th>
                <input type="checkbox" onchange="toggleSelectAllCompetitors(this.checked)">
              </th>
              <th>竞争对手</th>
              <th>市场份额</th>
              <th>设备数量</th>
              <th>价格策略</th>
              <th>威胁等级</th>
              <th>市场定位</th>
              <th>优势分析</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            ${(competitorAnalysis.competitors || []).map(competitor => `
              <tr>
                <td>
                  <input type="checkbox" value="${competitor.name}" onchange="toggleCompetitorSelection(this)">
                </td>
                <td>
                  <div class="competitor-info">
                    <div class="competitor-name">${competitor.name}</div>
                    <div class="competitor-desc">${competitor.description || '暂无描述'}</div>
                  </div>
                </td>
                <td>
                  <div class="market-share-info">
                    <div class="share-bar">
                      <div class="share-fill" style="width: ${competitor.marketShare}%"></div>
                    </div>
                    <span class="share-value">${competitor.marketShare}%</span>
                  </div>
                </td>
                <td>
                  <div class="device-count">
                    <span class="count-number">${competitor.deviceCount.toLocaleString()}</span>
                    <span class="count-trend up">+${Math.floor(Math.random() * 10 + 2)}%</span>
                  </div>
                </td>
                <td>
                  <span class="strategy-badge strategy-${competitor.priceStrategy === '低价策略' ? 'low' : competitor.priceStrategy === '高价策略' ? 'high' : 'medium'}">
                    ${competitor.priceStrategy}
                  </span>
                </td>
                <td>
                  <span class="threat-badge threat-${competitor.threatLevel === '高' ? 'high' : competitor.threatLevel === '中' ? 'medium' : 'low'}">
                    ${competitor.threatLevel}
                  </span>
                </td>
                <td>
                  <span class="position-badge position-${competitor.position === '领导者' ? 'leader' : competitor.position === '挑战者' ? 'challenger' : 'follower'}">
                    ${competitor.position}
                  </span>
                </td>
                <td>
                  <div class="advantage-analysis">
                    <div class="advantage-item">
                      <span class="advantage-label">技术优势:</span>
                      <span class="advantage-value">${Math.floor(Math.random() * 40 + 60)}%</span>
                    </div>
                    <div class="advantage-item">
                      <span class="advantage-label">成本优势:</span>
                      <span class="advantage-value">${Math.floor(Math.random() * 40 + 60)}%</span>
                    </div>
                  </div>
                </td>
                <td>
                  <div class="table-actions">
                    <button class="btn btn-small btn-secondary" onclick="viewCompetitorDetail('${competitor.name}')" title="查看详情">
                      <i class="ri-eye-line"></i>
                    </button>
                    <button class="btn btn-small btn-secondary" onclick="editCompetitor('${competitor.name}')" title="编辑">
                      <i class="ri-edit-line"></i>
                    </button>
                    <button class="btn btn-small btn-secondary" onclick="analyzeCompetitor('${competitor.name}')" title="分析">
                      <i class="ri-bar-chart-line"></i>
                    </button>
                    <button class="btn btn-small btn-secondary" onclick="compareCompetitor('${competitor.name}')" title="对比">
                      <i class="ri-exchange-line"></i>
                    </button>
                    <button class="btn btn-small btn-danger" onclick="deleteCompetitor('${competitor.name}')" title="删除">
                      <i class="ri-delete-bin-line"></i>
                    </button>
                  </div>
                </td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>

      <!-- 分页控件 -->
      <div class="pagination">
        <div class="pagination-info">
          显示 1-10 条，共 ${(competitorAnalysis.competitors || []).length} 条记录
        </div>
        <div class="pagination-controls">
          <button class="btn btn-small btn-secondary" disabled>上一页</button>
          <button class="btn btn-small btn-primary">1</button>
          <button class="btn btn-small btn-secondary">2</button>
          <button class="btn btn-small btn-secondary">3</button>
          <button class="btn btn-small btn-secondary">下一页</button>
        </div>
      </div>
    </div>
  `;
}

// 渲染财务分析页面
function renderFinancialAnalysis() {
  if (typeof mockData === 'undefined') {
    return '<div class="error-message">数据加载失败，请刷新页面重试</div>';
  }

  const financial = mockData.financial || {};
  
  return `
    <div class="financial-analysis-container">
      <!-- 页面标题和工具栏 -->
      <div class="page-header">
        <div class="page-title">
          <h2>收入与财务分析</h2>
          <p>全面分析收入结构、成本控制和盈利能力，制定财务策略</p>
        </div>
        <div class="page-actions">
          <button class="btn btn-secondary" onclick="refreshFinancialData()">
            <i class="ri-refresh-line"></i>刷新数据
          </button>
          <button class="btn btn-primary" onclick="exportFinancialReport()">
            <i class="ri-download-line"></i>导出报告
          </button>
        </div>
      </div>

      <!-- 统计概览 -->
      <div class="stats-grid">
        <div class="stat-card" onclick="showRevenueDetail()">
          <div class="stat-header">
            <div class="stat-icon revenue">
              <i class="ri-money-dollar-circle-line"></i>
            </div>
            <div class="stat-trend up">
              <i class="ri-arrow-up-line"></i>
              +18.5%
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">¥${(financial.totalRevenue || 0).toLocaleString()}</div>
            <div class="stat-label">总收入</div>
            <div class="stat-subtitle">较上月增长 18.5%</div>
          </div>
        </div>
        <div class="stat-card" onclick="showProfitDetail()">
          <div class="stat-header">
            <div class="stat-icon profit">
              <i class="ri-trending-up-line"></i>
            </div>
            <div class="stat-trend up">
              <i class="ri-arrow-up-line"></i>
              +22.3%
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">¥${(financial.totalProfit || 0).toLocaleString()}</div>
            <div class="stat-label">总利润</div>
            <div class="stat-subtitle">较上月增长 22.3%</div>
          </div>
        </div>
        <div class="stat-card" onclick="showMarginDetail()">
          <div class="stat-header">
            <div class="stat-icon margin">
              <i class="ri-pie-chart-line"></i>
            </div>
            <div class="stat-trend up">
              <i class="ri-arrow-up-line"></i>
              +3.2%
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">${financial.profitMargin || 0}%</div>
            <div class="stat-label">利润率</div>
            <div class="stat-subtitle">较上月提升 3.2%</div>
          </div>
        </div>
        <div class="stat-card" onclick="showGrowthDetail()">
          <div class="stat-header">
            <div class="stat-icon growth">
              <i class="ri-rocket-line"></i>
            </div>
            <div class="stat-trend up">
              <i class="ri-arrow-up-line"></i>
              +${financial.growthRate || 0}%
            </div>
          </div>
          <div class="stat-content">
            <div class="stat-value">${financial.growthRate || 0}%</div>
            <div class="stat-label">增长率</div>
            <div class="stat-subtitle">较去年同期</div>
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="charts-grid">
        <div class="chart-card">
          <div class="chart-header">
            <h3>收入趋势</h3>
            <div class="chart-actions">
              <button class="btn btn-small btn-secondary" onclick="refreshRevenueChart()">
                <i class="ri-refresh-line"></i>
              </button>
              <button class="btn btn-small btn-secondary" onclick="exportRevenueData()">
                <i class="ri-download-line"></i>
              </button>
            </div>
          </div>
          <canvas id="revenueChart" width="400" height="200"></canvas>
        </div>
        <div class="chart-card">
          <div class="chart-header">
            <h3>收入构成</h3>
            <div class="chart-actions">
              <button class="btn btn-small btn-secondary" onclick="refreshRevenueCompositionChart()">
                <i class="ri-refresh-line"></i>
              </button>
              <button class="btn btn-small btn-secondary" onclick="exportRevenueCompositionData()">
                <i class="ri-download-line"></i>
              </button>
            </div>
          </div>
          <canvas id="revenueCompositionChart" width="400" height="200"></canvas>
        </div>
        <div class="chart-card">
          <div class="chart-header">
            <h3>成本分析</h3>
            <div class="chart-actions">
              <button class="btn btn-small btn-secondary" onclick="refreshCostAnalysisChart()">
                <i class="ri-refresh-line"></i>
              </button>
              <button class="btn btn-small btn-secondary" onclick="exportCostAnalysisData()">
                <i class="ri-download-line"></i>
              </button>
            </div>
          </div>
          <canvas id="costAnalysisChart" width="400" height="200"></canvas>
        </div>
        <div class="chart-card">
          <div class="chart-header">
            <h3>利润预测</h3>
            <div class="chart-actions">
              <button class="btn btn-small btn-secondary" onclick="refreshProfitForecastChart()">
                <i class="ri-refresh-line"></i>
              </button>
              <button class="btn btn-small btn-secondary" onclick="exportProfitForecastData()">
                <i class="ri-download-line"></i>
              </button>
            </div>
          </div>
          <canvas id="profitForecastChart" width="400" height="200"></canvas>
        </div>
      </div>

      <!-- 财务详情工具栏 -->
      <div class="table-toolbar">
        <div class="toolbar-left">
          <div class="search-box">
            <i class="ri-search-line"></i>
            <input type="text" placeholder="搜索月份..." onkeyup="searchFinancialData(this.value)">
          </div>
          <div class="filter-group">
            <select onchange="filterFinancialByRevenue(this.value)">
              <option value="">所有收入范围</option>
              <option value="high">高收入 (>¥100000)</option>
              <option value="medium">中等收入 (¥50000-100000)</option>
              <option value="low">低收入 (<¥50000)</option>
            </select>
            <select onchange="filterFinancialByMargin(this.value)">
              <option value="">所有利润率</option>
              <option value="high">高利润率 (>30%)</option>
              <option value="medium">中等利润率 (15-30%)</option>
              <option value="low">低利润率 (<15%)</option>
            </select>
          </div>
        </div>
        <div class="toolbar-right">
          <button class="btn btn-secondary" onclick="batchExportFinancialData()">
            <i class="ri-download-line"></i>批量导出
          </button>
          <button class="btn btn-primary" onclick="showAddFinancialRecordModal()">
            <i class="ri-add-line"></i>添加记录
          </button>
        </div>
      </div>

      <!-- 财务详情 -->
      <div class="table-container">
        <table class="data-table">
          <thead>
            <tr>
              <th>
                <input type="checkbox" onchange="toggleSelectAllFinancial(this.checked)">
              </th>
              <th>月份</th>
              <th>收入</th>
              <th>成本</th>
              <th>利润</th>
              <th>利润率</th>
              <th>订单数</th>
              <th>平均客单价</th>
              <th>趋势</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            ${(financial.monthlyData || []).map(month => `
              <tr>
                <td>
                  <input type="checkbox" value="${month.month}" onchange="toggleFinancialSelection(this)">
                </td>
                <td>
                  <div class="month-info">
                    <div class="month-name">${month.month}</div>
                    <div class="month-period">${month.period || '全月'}</div>
                  </div>
                </td>
                <td>
                  <div class="revenue-info">
                    <div class="revenue-amount">¥${month.revenue.toLocaleString()}</div>
                    <div class="revenue-trend up">+${Math.floor(Math.random() * 20 + 5)}%</div>
                  </div>
                </td>
                <td>
                  <div class="cost-info">
                    <div class="cost-amount">¥${month.cost.toLocaleString()}</div>
                    <div class="cost-trend down">-${Math.floor(Math.random() * 10 + 2)}%</div>
                  </div>
                </td>
                <td>
                  <div class="profit-info">
                    <div class="profit-amount">¥${month.profit.toLocaleString()}</div>
                    <div class="profit-trend up">+${Math.floor(Math.random() * 25 + 8)}%</div>
                  </div>
                </td>
                <td>
                  <div class="margin-indicator">
                    <div class="margin-bar">
                      <div class="margin-fill" style="width: ${month.margin}%"></div>
                    </div>
                    <span class="margin-value">${month.margin}%</span>
                  </div>
                </td>
                <td>
                  <div class="orders-info">
                    <span class="orders-count">${month.orders.toLocaleString()}</span>
                    <span class="orders-trend up">+${Math.floor(Math.random() * 15 + 3)}%</span>
                  </div>
                </td>
                <td>
                  <div class="avg-price">
                    <span class="price-amount">¥${(month.revenue / month.orders).toFixed(2)}</span>
                    <span class="price-trend up">+${Math.floor(Math.random() * 8 + 1)}%</span>
                  </div>
                </td>
                <td>
                  <span class="trend-badge trend-positive">📈 上升</span>
                </td>
                <td>
                  <div class="table-actions">
                    <button class="btn btn-small btn-secondary" onclick="viewFinancialDetail('${month.month}')" title="查看详情">
                      <i class="ri-eye-line"></i>
                    </button>
                    <button class="btn btn-small btn-secondary" onclick="editFinancialRecord('${month.month}')" title="编辑">
                      <i class="ri-edit-line"></i>
                    </button>
                    <button class="btn btn-small btn-secondary" onclick="analyzeFinancialRecord('${month.month}')" title="分析">
                      <i class="ri-bar-chart-line"></i>
                    </button>
                    <button class="btn btn-small btn-danger" onclick="deleteFinancialRecord('${month.month}')" title="删除">
                      <i class="ri-delete-bin-line"></i>
                    </button>
                  </div>
                </td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>

      <!-- 分页控件 -->
      <div class="pagination">
        <div class="pagination-info">
          显示 1-10 条，共 ${(financial.monthlyData || []).length} 条记录
        </div>
        <div class="pagination-controls">
          <button class="btn btn-small btn-secondary" disabled>上一页</button>
          <button class="btn btn-small btn-primary">1</button>
          <button class="btn btn-small btn-secondary">2</button>
          <button class="btn btn-small btn-secondary">3</button>
          <button class="btn btn-small btn-secondary">下一页</button>
        </div>
      </div>
    </div>
  `;
}

// 渲染维护管理页面
function renderMaintenance() {
  if (typeof mockData === 'undefined') {
    return '<div class="error-message">数据加载失败，请刷新页面重试</div>';
  }

  const maintenance = mockData.maintenance || {};
  
  return `
    <div class="maintenance-container">
      <!-- 统计概览 -->
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon pending">⏳</div>
          <div class="stat-content">
            <div class="stat-value">${maintenance.pendingOrders || 0}</div>
            <div class="stat-label">待处理工单</div>
        </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon completed">✅</div>
          <div class="stat-content">
            <div class="stat-value">${maintenance.completedOrders || 0}</div>
            <div class="stat-label">已完成工单</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon response">⚡</div>
          <div class="stat-content">
            <div class="stat-value">${maintenance.avgResponseTime || 0}h</div>
            <div class="stat-label">平均响应时间</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon satisfaction">😊</div>
          <div class="stat-content">
            <div class="stat-value">${maintenance.satisfaction || 0}%</div>
            <div class="stat-label">满意度</div>
          </div>
        </div>
      </div>

      <!-- 工单列表 -->
      <div class="table-container">
        <div class="table-header">
          <h3>维护工单</h3>
          <button class="btn btn-primary" onclick="createMaintenanceOrder()">创建工单</button>
        </div>
        <table class="data-table">
          <thead>
            <tr>
              <th>工单ID</th>
              <th>设备ID</th>
              <th>问题类型</th>
              <th>优先级</th>
              <th>状态</th>
              <th>创建时间</th>
              <th>响应时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            ${(maintenance.orders || []).map(order => `
              <tr>
                <td>${order.id}</td>
                <td>${order.deviceId}</td>
                <td>${order.issueType}</td>
                <td><span class="priority-badge priority-${order.priority === '高' ? 'high' : order.priority === '中' ? 'medium' : 'low'}">${order.priority}</span></td>
                <td><span class="status-badge status-${order.status === '待处理' ? 'pending' : order.status === '处理中' ? 'processing' : 'completed'}">${order.status}</span></td>
                <td>${order.createTime}</td>
                <td>${order.responseTime || '-'}</td>
                <td>
                  <button class="btn btn-small btn-secondary" onclick="editMaintenanceOrder(${order.id})">编辑</button>
                  <button class="btn btn-small btn-danger" onclick="deleteMaintenanceOrder(${order.id})">删除</button>
                </td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    </div>
  `;
}

// 渲染系统管理页面
function renderSystem() {
  if (typeof mockData === 'undefined') {
    return '<div class="error-message">数据加载失败，请刷新页面重试</div>';
  }

  const system = mockData.system || {};
  
  return `
    <div class="system-container">
      <!-- 统计概览 -->
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon uptime">⏰</div>
          <div class="stat-content">
            <div class="stat-value">${system.uptime || 0}%</div>
            <div class="stat-label">系统可用性</div>
            </div>
            </div>
        <div class="stat-card">
          <div class="stat-icon performance">⚡</div>
          <div class="stat-content">
            <div class="stat-value">${system.performance || 0}%</div>
            <div class="stat-label">系统性能</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon security">🔒</div>
          <div class="stat-content">
            <div class="stat-value">${system.securityScore || 0}</div>
            <div class="stat-label">安全评分</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon alerts">🚨</div>
          <div class="stat-content">
            <div class="stat-value">${system.activeAlerts || 0}</div>
            <div class="stat-label">活跃告警</div>
            </div>
            </div>
          </div>

      <!-- 系统功能 -->
      <div class="function-grid">
        <div class="function-card" onclick="showSystemLogs()">
          <div class="function-icon">📋</div>
          <div class="function-content">
            <h4>系统日志</h4>
            <p>查看系统运行日志和错误信息</p>
          </div>
        </div>
        <div class="function-card" onclick="showAlarmManagement()">
          <div class="function-icon">🚨</div>
          <div class="function-content">
            <h4>告警管理</h4>
            <p>管理系统告警和通知设置</p>
            </div>
            </div>
        <div class="function-card" onclick="showPermissionManagement()">
          <div class="function-icon">👥</div>
          <div class="function-content">
            <h4>权限管理</h4>
            <p>管理用户权限和角色设置</p>
          </div>
          </div>
        <div class="function-card" onclick="showFirmwareManagement()">
          <div class="function-icon">🔧</div>
          <div class="function-content">
            <h4>固件管理</h4>
            <p>设备固件升级和维护</p>
        </div>
            </div>
        <div class="function-card" onclick="showSystemSettings()">
          <div class="function-icon">⚙️</div>
          <div class="function-content">
            <h4>系统设置</h4>
            <p>系统配置和参数设置</p>
            </div>
          </div>
        <div class="function-card" onclick="showDataBackup()">
          <div class="function-icon">💾</div>
          <div class="function-content">
            <h4>数据备份</h4>
            <p>系统数据备份和恢复</p>
          </div>
        </div>
      </div>
    </div>
  `;
}

// ========================================
// 用户行为分析页面功能函数
// ========================================

// 显示用户详情
function showUserDetail(type) {
  const titles = {
    'total': '总用户数详情',
    'active': '活跃用户详情',
    'time': '使用时长详情',
    'satisfaction': '满意度详情'
  };
  
  showModal(titles[type] || '用户详情', `
    <div class="detail-content">
      <div class="detail-item">
        <label>统计时间：</label>
        <span>${new Date().toLocaleDateString()}</span>
            </div>
      <div class="detail-item">
        <label>数据来源：</label>
        <span>用户行为分析系统</span>
          </div>
      <div class="detail-item">
        <label>更新时间：</label>
        <span>${new Date().toLocaleString()}</span>
        </div>
            </div>
  `, [
    { text: '关闭', class: 'secondary', onclick: 'closeModal()' },
    { text: '导出数据', class: 'primary', onclick: 'exportUserDetailData()' }
  ]);
}

// 搜索用户
function searchUsers(value) {
  if (!value) {
    // 显示所有用户
    return;
  }
  
  showToast(`搜索用户: ${value}`, 'info');
  // 这里可以添加实际的搜索逻辑
}

// 按用户类型筛选
function filterUsersByType(type) {
  if (!type) {
    // 显示所有类型
    return;
  }
  
  showToast(`筛选用户类型: ${type}`, 'info');
  // 这里可以添加实际的筛选逻辑
}

// 按活跃度筛选
function filterUsersByActivity(activity) {
  if (!activity) {
    // 显示所有活跃度
    return;
  }
  
  showToast(`筛选活跃度: ${activity}`, 'info');
  // 这里可以添加实际的筛选逻辑
}

// 批量导出用户
function batchExportUsers() {
  showToast('正在准备批量导出用户数据...', 'info');
  setTimeout(() => {
    showToast('用户数据批量导出成功', 'success');
  }, 2000);
}

// 显示添加用户模态框
function showAddUserModal() {
  showModal('添加用户', `
    <form id="addUserForm">
      <div class="form-group">
        <label>用户ID：</label>
        <input type="text" id="newUserId" required>
          </div>
      <div class="form-group">
        <label>用户类型：</label>
        <select id="newUserType" required>
          <option value="个人用户">个人用户</option>
          <option value="企业用户">企业用户</option>
          <option value="学生用户">学生用户</option>
        </select>
        </div>
      <div class="form-group">
        <label>活跃度：</label>
        <select id="newUserActivity" required>
          <option value="高">高</option>
          <option value="中">中</option>
          <option value="低">低</option>
        </select>
            </div>
    </form>
  `, [
    { text: '取消', class: 'secondary', onclick: 'closeModal()' },
    { text: '确定', class: 'primary', onclick: 'submitAddUser()' }
  ]);
}

// 提交添加用户
function submitAddUser() {
  const userId = document.getElementById('newUserId')?.value;
  const userType = document.getElementById('newUserType')?.value;
  const userActivity = document.getElementById('newUserActivity')?.value;
  
  if (!userId || !userType || !userActivity) {
    showToast('请填写所有必填字段', 'error');
    return;
  }
  
  showToast('用户添加成功', 'success');
  closeModal();
  loadPage('user-behavior');
}

// 查看用户详情
function viewUserDetail(userId) {
  showModal(`用户详情 - ${userId}`, `
    <div class="detail-content">
      <div class="detail-item">
        <label>用户ID：</label>
        <span>${userId}</span>
          </div>
      <div class="detail-item">
        <label>用户类型：</label>
        <span>个人用户</span>
        </div>
      <div class="detail-item">
        <label>活跃度：</label>
        <span>高</span>
            </div>
      <div class="detail-item">
        <label>使用次数：</label>
        <span>156</span>
          </div>
      <div class="detail-item">
        <label>平均时长：</label>
        <span>2.5h</span>
        </div>
      <div class="detail-item">
        <label>最后使用：</label>
        <span>2024-01-15 14:30</span>
      </div>
    </div>
  `, [
    { text: '关闭', class: 'secondary', onclick: 'closeModal()' },
    { text: '导出详情', class: 'primary', onclick: 'exportUserDetailData()' }
  ]);
}

// 编辑用户
function editUser(userId) {
  showModal(`编辑用户 - ${userId}`, `
    <form id="editUserForm">
      <div class="form-group">
        <label>用户ID：</label>
        <input type="text" value="${userId}" readonly>
          </div>
      <div class="form-group">
        <label>用户类型：</label>
        <select>
          <option value="个人用户" selected>个人用户</option>
          <option value="企业用户">企业用户</option>
          <option value="学生用户">学生用户</option>
            </select>
      </div>
      <div class="form-group">
        <label>活跃度：</label>
        <select>
          <option value="高" selected>高</option>
          <option value="中">中</option>
          <option value="低">低</option>
            </select>
      </div>
    </form>
  `, [
    { text: '取消', class: 'secondary', onclick: 'closeModal()' },
    { text: '保存', class: 'primary', onclick: 'saveUserEdit()' }
  ]);
}

// 保存用户编辑
function saveUserEdit() {
  showToast('用户信息更新成功', 'success');
  closeModal();
}

// 删除用户
function deleteUser(userId) {
  if (confirm(`确定要删除用户 ${userId} 吗？`)) {
    showToast('用户删除成功', 'success');
  }
}

// 切换全选用户
function toggleSelectAllUsers(checked) {
  const checkboxes = document.querySelectorAll('input[type="checkbox"][value]');
  checkboxes.forEach(checkbox => {
    checkbox.checked = checked;
  });
}

// 切换用户选择
function toggleUserSelection(checkbox) {
  // 可以在这里添加选择逻辑
}

// 刷新用户活跃度图表
function refreshUserActivityChart() {
  showToast('用户活跃度图表已刷新', 'success');
}

// 导出用户活跃度数据
function exportUserActivityData() {
  const activityData = [
    { '时间段': '00:00-06:00', '活跃用户': 150, '占比': '15%' },
    { '时间段': '06:00-12:00', '活跃用户': 450, '占比': '45%' },
    { '时间段': '12:00-18:00', '活跃用户': 300, '占比': '30%' },
    { '时间段': '18:00-24:00', '活跃用户': 100, '占比': '10%' }
  ];
  
  downloadCSVFile(activityData, `用户活动数据_${new Date().toISOString().slice(0, 10)}`);
}

// 刷新用户画像图表
function refreshUserProfileChart() {
  showToast('用户画像图表已刷新', 'success');
}

// 导出用户画像数据
function exportUserProfileData() {
  const profileData = [
    { '用户类型': '学生', '数量': 400, '占比': '40%' },
    { '用户类型': '上班族', '数量': 350, '占比': '35%' },
    { '用户类型': '其他', '数量': 250, '占比': '25%' }
  ];
  
  downloadCSVFile(profileData, `用户画像数据_${new Date().toISOString().slice(0, 10)}`);
}

// 刷新使用时段图表
function refreshUsageTimeChart() {
  showToast('使用时段图表已刷新', 'success');
}

// 导出使用时段数据
function exportUsageTimeData() {
  const timeData = [
    { '时长段': '0-1小时', '用户数': 300, '占比': '30%' },
    { '时长段': '1-2小时', '用户数': 400, '占比': '40%' },
    { '时长段': '2-4小时', '用户数': 200, '占比': '20%' },
    { '时长段': '4小时以上', '用户数': 100, '占比': '10%' }
  ];
  
  downloadCSVFile(timeData, `使用时长数据_${new Date().toISOString().slice(0, 10)}`);
}

// 刷新留存率图表
function refreshRetentionChart() {
  showToast('留存率图表已刷新', 'success');
}

// 导出留存率数据
function exportRetentionData() {
  const retentionData = [
    { '天数': '1天', '留存率': '85%', '用户数': 850 },
    { '天数': '7天', '留存率': '65%', '用户数': 650 },
    { '天数': '30天', '留存率': '45%', '用户数': 450 },
    { '天数': '90天', '留存率': '25%', '用户数': 250 }
  ];
  
  downloadCSVFile(retentionData, `留存率数据_${new Date().toISOString().slice(0, 10)}`);
}

// ========================================
// 市场热点分析页面功能函数
// ========================================

// 刷新市场数据
function refreshMarketData() {
  showToast('市场数据已刷新', 'success');
  loadPage('market-analysis');
}

// 导出市场报告
function exportMarketReport() {
  showToast('市场报告导出成功', 'success');
}

// 显示热点详情
function showHotspotDetail(type) {
  showModal('热点区域详情', `
    <div class="detail-content">
      <div class="detail-item">
        <label>热点类型：</label>
        <span>${type}</span>
      </div>
      <div class="detail-item">
        <label>统计时间：</label>
        <span>${new Date().toLocaleDateString()}</span>
      </div>
      <div class="detail-item">
        <label>数据来源：</label>
        <span>市场分析系统</span>
      </div>
    </div>
  `, [
    { text: '关闭', class: 'secondary', onclick: 'closeModal()' },
    { text: '导出数据', class: 'primary', onclick: 'exportHotspotDetailData()' }
  ]);
}

// 显示高峰时段详情
function showPeakHoursDetail() {
  showModal('高峰时段详情', `
    <div class="detail-content">
      <div class="detail-item">
        <label>高峰时段：</label>
        <span>18:00-20:00, 12:00-14:00, 08:00-10:00</span>
      </div>
      <div class="detail-item">
        <label>使用量峰值：</label>
        <span>85%</span>
      </div>
      <div class="detail-item">
        <label>平均等待时间：</label>
        <span>3.2分钟</span>
      </div>
    </div>
  `, [
    { text: '关闭', class: 'secondary', onclick: 'closeModal()' },
    { text: '导出数据', class: 'primary', onclick: 'exportPeakHoursData()' }
  ]);
}

// 显示增长详情
function showGrowthDetail() {
  showModal('市场增长详情', `
    <div class="detail-content">
      <div class="detail-item">
        <label>增长率：</label>
        <span>+25.3%</span>
      </div>
      <div class="detail-item">
        <label>增长驱动因素：</label>
        <span>新设备投放、用户习惯培养、营销活动</span>
      </div>
      <div class="detail-item">
        <label>预期趋势：</label>
        <span>持续增长</span>
      </div>
    </div>
  `, [
    { text: '关闭', class: 'secondary', onclick: 'closeModal()' },
    { text: '导出数据', class: 'primary', onclick: 'exportGrowthData()' }
  ]);
}

// 显示竞争详情
function showCompetitionDetail() {
  showModal('竞争指数详情', `
    <div class="detail-content">
      <div class="detail-item">
        <label>竞争指数：</label>
        <span>7.2/10</span>
      </div>
      <div class="detail-item">
        <label>主要竞争对手：</label>
        <span>街电、小电、怪兽充电</span>
      </div>
      <div class="detail-item">
        <label>竞争优势：</label>
        <span>设备密度高、用户体验好</span>
      </div>
    </div>
  `, [
    { text: '关闭', class: 'secondary', onclick: 'closeModal()' },
    { text: '导出数据', class: 'primary', onclick: 'exportCompetitionData()' }
  ]);
}

// 搜索热点
function searchHotspots(value) {
  if (!value) {
    return;
  }
  
  showToast(`搜索热点: ${value}`, 'info');
}

// 按热度筛选
function filterHotspotsByHeat(heat) {
  if (!heat) {
    return;
  }
  
  showToast(`筛选热度: ${heat}`, 'info');
}

// 按收入筛选
function filterHotspotsByRevenue(revenue) {
  if (!revenue) {
    return;
  }
  
  showToast(`筛选收入: ${revenue}`, 'info');
}

// 批量导出热点
function batchExportHotspots() {
  showToast('正在准备批量导出热点数据...', 'info');
  setTimeout(() => {
    showToast('热点数据批量导出成功', 'success');
  }, 2000);
}

// 显示添加热点模态框
function showAddHotspotModal() {
  showModal('添加热点区域', `
    <form id="addHotspotForm">
      <div class="form-group">
        <label>区域名称：</label>
        <input type="text" id="newHotspotName" required>
      </div>
      <div class="form-group">
        <label>设备数量：</label>
        <input type="number" id="newHotspotDevices" required>
      </div>
      <div class="form-group">
        <label>热度指数：</label>
        <input type="number" id="newHotspotHeat" min="0" max="100" required>
      </div>
    </form>
  `, [
    { text: '取消', class: 'secondary', onclick: 'closeModal()' },
    { text: '确定', class: 'primary', onclick: 'submitAddHotspot()' }
  ]);
}

// 提交添加热点
function submitAddHotspot() {
  const name = document.getElementById('newHotspotName')?.value;
  const devices = document.getElementById('newHotspotDevices')?.value;
  const heat = document.getElementById('newHotspotHeat')?.value;
  
  if (!name || !devices || !heat) {
    showToast('请填写所有必填字段', 'error');
    return;
  }
  
  showToast('热点区域添加成功', 'success');
  closeModal();
  loadPage('market-analysis');
}

// 查看热点详情
function viewHotspotDetail(hotspotName) {
  showModal(`热点详情 - ${hotspotName}`, `
    <div class="detail-content">
      <div class="detail-item">
        <label>区域名称：</label>
        <span>${hotspotName}</span>
      </div>
      <div class="detail-item">
        <label>设备数量：</label>
        <span>150</span>
      </div>
      <div class="detail-item">
        <label>热度指数：</label>
        <span>85</span>
      </div>
      <div class="detail-item">
        <label>月收入：</label>
        <span>¥12,500</span>
      </div>
    </div>
  `, [
    { text: '关闭', class: 'secondary', onclick: 'closeModal()' },
    { text: '导出详情', class: 'primary', onclick: 'exportHotspotDetailData()' }
  ]);
}

// 编辑热点
function editHotspot(hotspotName) {
  showModal(`编辑热点 - ${hotspotName}`, `
    <form id="editHotspotForm">
      <div class="form-group">
        <label>区域名称：</label>
        <input type="text" value="${hotspotName}" readonly>
      </div>
      <div class="form-group">
        <label>设备数量：</label>
        <input type="number" value="150">
      </div>
      <div class="form-group">
        <label>热度指数：</label>
        <input type="number" value="85" min="0" max="100">
      </div>
    </form>
  `, [
    { text: '取消', class: 'secondary', onclick: 'closeModal()' },
    { text: '保存', class: 'primary', onclick: 'saveHotspotEdit()' }
  ]);
}

// 保存热点编辑
function saveHotspotEdit() {
  showToast('热点信息更新成功', 'success');
  closeModal();
}

// 分析热点
function analyzeHotspot(hotspotName) {
  showModal(`热点分析 - ${hotspotName}`, `
    <div class="detail-content">
      <div class="detail-item">
        <label>使用率：</label>
        <span>78%</span>
      </div>
      <div class="detail-item">
        <label>平均使用时长：</label>
        <span>2.3小时</span>
      </div>
      <div class="detail-item">
        <label>高峰时段：</label>
        <span>18:00-20:00</span>
      </div>
      <div class="detail-item">
        <label>用户满意度：</label>
        <span>4.2/5.0</span>
      </div>
    </div>
  `, [
    { text: '关闭', class: 'secondary', onclick: 'closeModal()' },
    { text: '生成报告', class: 'primary', onclick: 'generateHotspotReport()' }
  ]);
}

// 删除热点
function deleteHotspot(hotspotName) {
  if (confirm(`确定要删除热点区域 ${hotspotName} 吗？`)) {
    showToast('热点区域删除成功', 'success');
  }
}

// 切换全选热点
function toggleSelectAllHotspots(checked) {
  const checkboxes = document.querySelectorAll('input[type="checkbox"][value]');
  checkboxes.forEach(checkbox => {
    checkbox.checked = checked;
  });
}

// 切换热点选择
function toggleHotspotSelection(checkbox) {
  // 可以在这里添加选择逻辑
}

// 刷新高峰时段图表
function refreshPeakHoursChart() {
  showToast('高峰时段图表已刷新', 'success');
}

// 导出高峰时段数据
function exportPeakHoursData() {
  const peakData = [
    { '时段': '08:00-10:00', '使用量': 150, '占比': '25%' },
    { '时段': '12:00-14:00', '使用量': 200, '占比': '33%' },
    { '时段': '18:00-20:00', '使用量': 180, '占比': '30%' },
    { '时段': '其他时段', '使用量': 70, '占比': '12%' }
  ];
  
  downloadCSVFile(peakData, `高峰时段数据_${new Date().toISOString().slice(0, 10)}`);
}

// 导出热门设备数据
function exportHotDevicesData() {
  const hotDevices = [
    { '设备ID': 'DEV-001', '使用次数': 150, '收入': 1200, '热度': '高' },
    { '设备ID': 'DEV-002', '使用次数': 120, '收入': 960, '热度': '高' },
    { '设备ID': 'DEV-003', '使用次数': 100, '收入': 800, '热度': '中' },
    { '设备ID': 'DEV-004', '使用次数': 80, '收入': 640, '热度': '中' }
  ];
  
  downloadCSVFile(hotDevices, `热门设备数据_${new Date().toISOString().slice(0, 10)}`);
}

// 导出热力图数据
function exportHeatMapData() {
  const heatMapData = [
    { '区域': '商业区', '热度': '高', '设备数': 50, '使用率': '85%' },
    { '区域': '住宅区', '热度': '中', '设备数': 30, '使用率': '65%' },
    { '区域': '学校区', '热度': '高', '设备数': 40, '使用率': '90%' },
    { '区域': '办公区', '热度': '中', '设备数': 35, '使用率': '70%' }
  ];
  
  downloadCSVFile(heatMapData, `热力图数据_${new Date().toISOString().slice(0, 10)}`);
}

// 导出趋势数据
function exportTrendData() {
  const trendData = [
    { '月份': '2024-01', '使用量': 1200, '收入': 9600, '增长率': '+15%' },
    { '月份': '2024-02', '使用量': 1380, '收入': 11040, '增长率': '+15%' },
    { '月份': '2024-03', '使用量': 1587, '收入': 12696, '增长率': '+15%' },
    { '月份': '2024-04', '使用量': 1825, '收入': 14600, '增长率': '+15%' }
  ];
  
  downloadCSVFile(trendData, `趋势数据_${new Date().toISOString().slice(0, 10)}`);
}

// ========================================
// 竞争对手分析页面功能函数
// ========================================

// 刷新竞争对手数据
function refreshCompetitorData() {
  showToast('竞争对手数据已刷新', 'success');
  loadPage('competitor-analysis');
}

// 导出竞争对手报告
function exportCompetitorReport() {
  showToast('竞争对手报告导出成功', 'success');
}

// 显示市场份额详情
function showMarketShareDetail() {
  showModal('市场份额详情', `
    <div class="detail-content">
      <div class="detail-item">
        <label>当前份额：</label>
        <span>32.5%</span>
      </div>
      <div class="detail-item">
        <label>排名：</label>
        <span>第2位</span>
      </div>
      <div class="detail-item">
        <label>主要竞争对手：</label>
        <span>街电(35.2%)、小电(18.3%)、怪兽充电(14.0%)</span>
      </div>
    </div>
  `, [
    { text: '关闭', class: 'secondary', onclick: 'closeModal()' },
    { text: '导出数据', class: 'primary', onclick: 'exportMarketShareData()' }
  ]);
}

// 显示竞争对手数量详情
function showCompetitorCountDetail() {
  showModal('竞争对手数量详情', `
    <div class="detail-content">
      <div class="detail-item">
        <label>主要竞争对手：</label>
        <span>8个</span>
      </div>
      <div class="detail-item">
        <label>潜在竞争对手：</label>
        <span>12个</span>
      </div>
      <div class="detail-item">
        <label>新增竞争对手：</label>
        <span>2个</span>
      </div>
    </div>
  `, [
    { text: '关闭', class: 'secondary', onclick: 'closeModal()' },
    { text: '导出数据', class: 'primary', onclick: 'exportCompetitorCountData()' }
  ]);
}

// 显示竞争优势详情
function showAdvantageDetail() {
  showModal('竞争优势详情', `
    <div class="detail-content">
      <div class="detail-item">
        <label>技术优势：</label>
        <span>85%</span>
      </div>
      <div class="detail-item">
        <label>成本优势：</label>
        <span>72%</span>
      </div>
      <div class="detail-item">
        <label>服务优势：</label>
        <span>78%</span>
      </div>
    </div>
  `, [
    { text: '关闭', class: 'secondary', onclick: 'closeModal()' },
    { text: '导出数据', class: 'primary', onclick: 'exportAdvantageData()' }
  ]);
}

// 显示威胁详情
function showThreatDetail() {
  showModal('威胁等级详情', `
    <div class="detail-content">
      <div class="detail-item">
        <label>威胁等级：</label>
        <span>中等</span>
      </div>
      <div class="detail-item">
        <label>主要威胁：</label>
        <span>价格战、技术突破、政策变化</span>
      </div>
      <div class="detail-item">
        <label>应对策略：</label>
        <span>差异化竞争、技术创新、合规经营</span>
      </div>
    </div>
  `, [
    { text: '关闭', class: 'secondary', onclick: 'closeModal()' },
    { text: '导出数据', class: 'primary', onclick: 'exportThreatData()' }
  ]);
}

// 搜索竞争对手
function searchCompetitors(value) {
  if (!value) {
    return;
  }
  
  showToast(`搜索竞争对手: ${value}`, 'info');
}

// 按威胁等级筛选
function filterCompetitorsByThreat(threat) {
  if (!threat) {
    return;
  }
  
  showToast(`筛选威胁等级: ${threat}`, 'info');
}

// 按市场份额筛选
function filterCompetitorsByMarketShare(share) {
  if (!share) {
    return;
  }
  
  showToast(`筛选市场份额: ${share}`, 'info');
}

// 批量导出竞争对手
function batchExportCompetitors() {
  showToast('正在准备批量导出竞争对手数据...', 'info');
  setTimeout(() => {
    showToast('竞争对手数据批量导出成功', 'success');
  }, 2000);
}

// 查看竞争对手详情
function viewCompetitorDetail(competitorName) {
  showModal(`竞争对手详情 - ${competitorName}`, `
    <div class="detail-content">
      <div class="detail-item">
        <label>竞争对手：</label>
        <span>${competitorName}</span>
      </div>
      <div class="detail-item">
        <label>市场份额：</label>
        <span>25.0%</span>
      </div>
      <div class="detail-item">
        <label>设备数量：</label>
        <span>8,000</span>
      </div>
      <div class="detail-item">
        <label>价格策略：</label>
        <span>中端定位</span>
      </div>
      <div class="detail-item">
        <label>威胁等级：</label>
        <span>中</span>
      </div>
    </div>
  `, [
    { text: '关闭', class: 'secondary', onclick: 'closeModal()' },
    { text: '导出详情', class: 'primary', onclick: 'exportCompetitorDetailData()' }
  ]);
}

// 编辑竞争对手
function editCompetitor(competitorName) {
  showModal(`编辑竞争对手 - ${competitorName}`, `
    <form id="editCompetitorForm">
      <div class="form-group">
        <label>竞争对手名称：</label>
        <input type="text" value="${competitorName}" readonly>
      </div>
      <div class="form-group">
        <label>市场份额：</label>
        <input type="number" value="25.0" step="0.1" min="0" max="100">
      </div>
      <div class="form-group">
        <label>设备数量：</label>
        <input type="number" value="8000">
      </div>
      <div class="form-group">
        <label>价格策略：</label>
        <select>
          <option value="低价策略">低价策略</option>
          <option value="中端定位" selected>中端定位</option>
          <option value="高价策略">高价策略</option>
            </select>
          </div>
      <div class="form-group">
        <label>威胁等级：</label>
        <select>
          <option value="高">高</option>
          <option value="中" selected>中</option>
          <option value="低">低</option>
        </select>
        </div>
    </form>
  `, [
    { text: '取消', class: 'secondary', onclick: 'closeModal()' },
    { text: '保存', class: 'primary', onclick: 'saveCompetitorEdit()' }
  ]);
}

// 保存竞争对手编辑
function saveCompetitorEdit() {
  showToast('竞争对手信息更新成功', 'success');
  closeModal();
}

// 分析竞争对手
function analyzeCompetitor(competitorName) {
  showModal(`竞争对手分析 - ${competitorName}`, `
    <div class="detail-content">
      <div class="detail-item">
        <label>SWOT分析：</label>
        <span>优势：技术领先、劣势：成本较高、机会：市场扩张、威胁：价格战</span>
        </div>
      <div class="detail-item">
        <label>竞争策略：</label>
        <span>差异化竞争、技术创新</span>
      </div>
      <div class="detail-item">
        <label>市场份额趋势：</label>
        <span>稳定增长</span>
      </div>
    </div>
  `, [
    { text: '关闭', class: 'secondary', onclick: 'closeModal()' },
    { text: '生成报告', class: 'primary', onclick: 'generateCompetitorReport()' }
  ]);
}

// 对比竞争对手
function compareCompetitor(competitorName) {
  showModal(`竞争对手对比 - ${competitorName}`, `
    <div class="detail-content">
      <div class="detail-item">
        <label>市场份额对比：</label>
        <span>我方32.5% vs ${competitorName} 25.0%</span>
      </div>
      <div class="detail-item">
        <label>设备数量对比：</label>
        <span>我方10,000 vs ${competitorName} 8,000</span>
      </div>
      <div class="detail-item">
        <label>价格策略对比：</label>
        <span>我方中端 vs ${competitorName} 中端</span>
      </div>
    </div>
  `, [
    { text: '关闭', class: 'secondary', onclick: 'closeModal()' },
    { text: '导出对比', class: 'primary', onclick: 'exportComparisonData()' }
  ]);
}

// 删除竞争对手
function deleteCompetitor(competitorName) {
  if (confirm(`确定要删除竞争对手 ${competitorName} 吗？`)) {
    showToast('竞争对手删除成功', 'success');
  }
}

// 切换全选竞争对手
function toggleSelectAllCompetitors(checked) {
  const checkboxes = document.querySelectorAll('input[type="checkbox"][value]');
  checkboxes.forEach(checkbox => {
    checkbox.checked = checked;
  });
}

// 切换竞争对手选择
function toggleCompetitorSelection(checkbox) {
  // 可以在这里添加选择逻辑
}

// 刷新市场份额图表
function refreshMarketShareChart() {
  showToast('市场份额图表已刷新', 'success');
}

// 导出市场份额数据
function exportMarketShareData() {
  const marketShareData = [
    { '公司': '我方', '市场份额': '32.5%', '设备数量': 10000, '增长率': '+5.2%' },
    { '公司': '竞争对手A', '市场份额': '25.0%', '设备数量': 8000, '增长率': '+3.1%' },
    { '公司': '竞争对手B', '市场份额': '18.5%', '设备数量': 6000, '增长率': '+2.8%' },
    { '公司': '其他', '市场份额': '24.0%', '设备数量': 7500, '增长率': '+1.5%' }
  ];
  
  downloadCSVFile(marketShareData, `市场份额数据_${new Date().toISOString().slice(0, 10)}`);
}

// 刷新竞争态势分析
function refreshCompetitiveAnalysis() {
  showToast('竞争态势分析已刷新', 'success');
}

// 导出竞争态势数据
function exportCompetitiveData() {
  const competitiveData = [
    { '竞争维度': '价格', '我方': '中等', '竞争对手A': '低', '竞争对手B': '高' },
    { '竞争维度': '技术', '我方': '领先', '竞争对手A': '中等', '竞争对手B': '中等' },
    { '竞争维度': '服务', '我方': '优秀', '竞争对手A': '良好', '竞争对手B': '一般' },
    { '竞争维度': '覆盖', '我方': '广泛', '竞争对手A': '中等', '竞争对手B': '有限' }
  ];
  
  downloadCSVFile(competitiveData, `竞争态势数据_${new Date().toISOString().slice(0, 10)}`);
}

// 刷新价格策略对比
function refreshPriceComparison() {
  showToast('价格策略对比已刷新', 'success');
}

// 导出价格数据
function exportPriceData() {
  const priceData = [
    { '价格策略': '我方', '基础价格': '2元/小时', '优惠策略': '会员折扣', '竞争力': '中等' },
    { '价格策略': '竞争对手A', '基础价格': '1.5元/小时', '优惠策略': '低价竞争', '竞争力': '高' },
    { '价格策略': '竞争对手B', '基础价格': '3元/小时', '优惠策略': '高端定位', '竞争力': '低' }
  ];
  
  downloadCSVFile(priceData, `价格数据_${new Date().toISOString().slice(0, 10)}`);
}

// 刷新威胁趋势预测
function refreshThreatPrediction() {
  showToast('威胁趋势预测已刷新', 'success');
}

// 导出威胁数据
function exportThreatData() {
  const threatData = [
    { '威胁类型': '价格战', '威胁等级': '高', '影响程度': '严重', '应对策略': '差异化竞争' },
    { '威胁类型': '技术突破', '威胁等级': '中', '影响程度': '中等', '应对策略': '持续创新' },
    { '威胁类型': '政策变化', '威胁等级': '低', '影响程度': '轻微', '应对策略': '合规经营' },
    { '威胁类型': '新进入者', '威胁等级': '中', '影响程度': '中等', '应对策略': '品牌建设' }
  ];
  
  downloadCSVFile(threatData, `威胁数据_${new Date().toISOString().slice(0, 10)}`);
}

// 导出用户详情数据
function exportUserDetailData() {
  const userDetailData = [
    {
      '用户ID': 'U001',
      '用户名': '张三',
      '注册时间': '2024-01-01',
      '使用次数': 25,
      '总消费': 200,
      '最后登录': '2024-01-15'
    }
  ];
  
  downloadCSVFile(userDetailData, `用户详情数据_${new Date().toISOString().slice(0, 10)}`);
}

// 导出热点详情数据
function exportHotspotDetailData() {
  const hotspotDetailData = [
    {
      '热点名称': '商业广场',
      '设备数量': 50,
      '日使用量': 150,
      '日收入': 1200,
      '使用率': '85%'
    }
  ];
  
  downloadCSVFile(hotspotDetailData, `热点详情数据_${new Date().toISOString().slice(0, 10)}`);
}

// 导出高峰时段数据
function exportPeakHoursData() {
  const peakHoursData = [
    { '时段': '08:00-10:00', '使用量': 150, '占比': '25%', '收入': 1200 },
    { '时段': '12:00-14:00', '使用量': 200, '占比': '33%', '收入': 1600 },
    { '时段': '18:00-20:00', '使用量': 180, '占比': '30%', '收入': 1440 },
    { '时段': '其他时段', '使用量': 70, '占比': '12%', '收入': 560 }
  ];
  
  downloadCSVFile(peakHoursData, `高峰时段数据_${new Date().toISOString().slice(0, 10)}`);
}

// 导出增长数据
function exportGrowthData() {
  const growthData = [
    { '指标': '用户数量', '当前值': 1000, '增长率': '+15%', '目标值': 1200 },
    { '指标': '设备数量', '当前值': 10000, '增长率': '+10%', '目标值': 11000 },
    { '指标': '日收入', '当前值': 4800, '增长率': '+20%', '目标值': 5760 },
    { '指标': '使用率', '当前值': '75%', '增长率': '+5%', '目标值': '80%' }
  ];
  
  downloadCSVFile(growthData, `增长数据_${new Date().toISOString().slice(0, 10)}`);
}

// 导出竞争数据
function exportCompetitionData() {
  const competitionData = [
    { '竞争对手': '竞争对手A', '市场份额': '25%', '威胁等级': '高', '应对策略': '差异化竞争' },
    { '竞争对手': '竞争对手B', '市场份额': '18.5%', '威胁等级': '中', '应对策略': '技术创新' },
    { '竞争对手': '竞争对手C', '市场份额': '12%', '威胁等级': '低', '应对策略': '服务优化' }
  ];
  
  downloadCSVFile(competitionData, `竞争数据_${new Date().toISOString().slice(0, 10)}`);
}

// 导出市场份额数据
function exportMarketShareData() {
  const marketShareData = [
    { '公司': '我方', '市场份额': '32.5%', '设备数量': 10000, '增长率': '+5.2%' },
    { '公司': '竞争对手A', '市场份额': '25.0%', '设备数量': 8000, '增长率': '+3.1%' },
    { '公司': '竞争对手B', '市场份额': '18.5%', '设备数量': 6000, '增长率': '+2.8%' },
    { '公司': '其他', '市场份额': '24.0%', '设备数量': 7500, '增长率': '+1.5%' }
  ];
  
  downloadCSVFile(marketShareData, `市场份额数据_${new Date().toISOString().slice(0, 10)}`);
}

// 导出竞争对手数量数据
function exportCompetitorCountData() {
  const competitorCountData = [
    { '区域': '一线城市', '竞争对手数量': 15, '威胁等级': '高', '市场饱和度': '80%' },
    { '区域': '二线城市', '竞争对手数量': 8, '威胁等级': '中', '市场饱和度': '60%' },
    { '区域': '三线城市', '竞争对手数量': 3, '威胁等级': '低', '市场饱和度': '30%' }
  ];
  
  downloadCSVFile(competitorCountData, `竞争对手数量数据_${new Date().toISOString().slice(0, 10)}`);
}

// 导出竞争优势数据
function exportAdvantageData() {
  const advantageData = [
    { '优势类型': '技术优势', '描述': '自主研发充电技术', '竞争力': '强', '保持策略': '持续创新' },
    { '优势类型': '服务优势', '描述': '24小时客服支持', '竞争力': '强', '保持策略': '服务升级' },
    { '优势类型': '成本优势', '描述': '规模化运营', '竞争力': '中等', '保持策略': '效率提升' },
    { '优势类型': '品牌优势', '描述': '知名品牌认可', '竞争力': '强', '保持策略': '品牌建设' }
  ];
  
  downloadCSVFile(advantageData, `竞争优势数据_${new Date().toISOString().slice(0, 10)}`);
}

// 导出竞争对手详情数据
function exportCompetitorDetailData() {
  const competitorDetailData = [
    {
      '竞争对手名称': '竞争对手A',
      '市场份额': '25.0%',
      '设备数量': 8000,
      '价格策略': '低价策略',
      '威胁等级': '高',
      '市场定位': '挑战者'
    }
  ];
  
  downloadCSVFile(competitorDetailData, `竞争对手详情数据_${new Date().toISOString().slice(0, 10)}`);
}

// 生成热点分析报告
function generateHotspotReport() {
  const reportData = [
    {
      '报告类型': '市场热点分析报告',
      '生成时间': new Date().toLocaleString(),
      '热点数量': 15,
      '总设备数': 10000,
      '平均使用率': '75%',
      '分析结论': '市场热点分布合理，使用率良好'
    }
  ];
  
  downloadCSVFile(reportData, `热点分析报告_${new Date().toISOString().slice(0, 10)}`);
}

// 生成竞争对手分析报告
function generateCompetitorReport() {
  const reportData = [
    {
      '报告类型': '竞争对手分析报告',
      '生成时间': new Date().toLocaleString(),
      '竞争对手数量': 3,
      '主要威胁': '价格竞争',
      '应对策略': '差异化竞争',
      '分析结论': '市场竞争激烈，需要持续创新'
    }
  ];
  
  downloadCSVFile(reportData, `竞争对手分析报告_${new Date().toISOString().slice(0, 10)}`);
}

// 导出对比数据
function exportComparisonData() {
  const comparisonData = [
    {
      '对比项目': '市场份额',
      '我方': '32.5%',
      '竞争对手A': '25.0%',
      '竞争对手B': '18.5%',
      '优势': '领先7.5%'
    },
    {
      '对比项目': '设备数量',
      '我方': '10000',
      '竞争对手A': '8000',
      '竞争对手B': '6000',
      '优势': '领先2000台'
    },
    {
      '对比项目': '价格策略',
      '我方': '中端定位',
      '竞争对手A': '低价策略',
      '竞争对手B': '高价策略',
      '优势': '平衡定位'
    }
  ];
  
  downloadCSVFile(comparisonData, `对比数据_${new Date().toISOString().slice(0, 10)}`);
}

// 刷新热点设备图表
function refreshHotDevicesChart() {
  showToast('热点设备图表已刷新', 'success');
}

// 刷新热度地图
function refreshHeatMap() {
  showToast('热度地图已刷新', 'success');
}

// 刷新趋势预测
function refreshTrendPrediction() {
  showToast('趋势预测已刷新', 'success');
}

// 刷新市场份额图表
function refreshMarketShareChart() {
  showToast('市场份额图表已刷新', 'success');
}

// 刷新竞争态势分析
function refreshCompetitiveAnalysis() {
  showToast('竞争态势分析已刷新', 'success');
}

// 刷新价格策略对比
function refreshPriceComparison() {
  showToast('价格策略对比已刷新', 'success');
}

// 刷新威胁趋势预测
function refreshThreatPrediction() {
  showToast('威胁趋势预测已刷新', 'success');
}
// 刷新威胁趋势预测
function refreshThreatPrediction() {
  showToast('威胁趋势预测已刷新', 'success');
}

// 初始化用户行为分析图表
function initUserBehaviorCharts() {
  if (typeof Chart === 'undefined') {
    console.error('Chart.js 未加载');
    return;
  }
  
  if (typeof mockData === 'undefined') {
    console.error('模拟数据未加载');
    return;
  }
  
  try {
    const userBehavior = mockData.userBehavior || {};
    
    // 用户活跃度趋势图表
    const userActivityCtx = document.getElementById('userActivityChart');
    if (userActivityCtx) {
      destroyChart('userActivityChart');
      const dailyStats = userBehavior.dailyStats || [];
      
      window.chartInstances['userActivityChart'] = new Chart(userActivityCtx, {
        type: 'line',
        data: {
          labels: dailyStats.map(item => item.date),
          datasets: [{
            label: '活跃用户',
            data: dailyStats.map(item => item.activeUsers),
            borderColor: '#0052d9',
            backgroundColor: 'rgba(0, 82, 217, 0.1)',
            tension: 0.4,
            borderWidth: 3,
            fill: true
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: false },
            tooltip: {
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              titleColor: '#000000',
              bodyColor: '#666666',
              borderColor: '#e7e7e7',
              borderWidth: 1,
              cornerRadius: 8,
              displayColors: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              grid: { color: 'rgba(0, 0, 0, 0.05)' },
              ticks: { color: '#666666' }
            },
            x: {
              grid: { color: 'rgba(0, 0, 0, 0.05)' },
              ticks: { color: '#666666' }
            }
          }
        }
      });
    }
    
    // 用户画像分布图表
    const userProfileCtx = document.getElementById('userProfileChart');
    if (userProfileCtx) {
      destroyChart('userProfileChart');
      const genderData = userBehavior.genderDistribution || {};
      
      window.chartInstances['userProfileChart'] = new Chart(userProfileCtx, {
        type: 'doughnut',
        data: {
          labels: Object.keys(genderData),
          datasets: [{
            data: Object.values(genderData),
            backgroundColor: ['#0052d9', '#ff4d4f'],
            borderWidth: 0,
            cutout: '60%'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: { color: '#666666', usePointStyle: true }
            }
          }
        }
      });
    }
    
    // 使用时段分布图表
    const usageTimeCtx = document.getElementById('usageTimeChart');
    if (usageTimeCtx) {
      destroyChart('usageTimeChart');
      
      window.chartInstances['usageTimeChart'] = new Chart(usageTimeCtx, {
        type: 'bar',
        data: {
          labels: ['00-06', '06-12', '12-18', '18-24'],
          datasets: [{
            label: '使用频次',
            data: [15, 45, 85, 95],
            backgroundColor: '#0052d9',
            borderRadius: 4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: false },
            tooltip: {
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              titleColor: '#000000',
              bodyColor: '#666666'
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              grid: { color: 'rgba(0, 0, 0, 0.05)' },
              ticks: { color: '#666666' }
            },
            x: {
              grid: { color: 'rgba(0, 0, 0, 0.05)' },
              ticks: { color: '#666666' }
            }
          }
        }
      });
    }
    
    // 用户留存率图表
    const retentionCtx = document.getElementById('retentionChart');
    if (retentionCtx) {
      destroyChart('retentionChart');
      
      window.chartInstances['retentionChart'] = new Chart(retentionCtx, {
        type: 'line',
        data: {
          labels: ['1天', '7天', '30天', '90天'],
          datasets: [{
            label: '留存率',
            data: [85, 65, 45, 35],
            borderColor: '#00a870',
            backgroundColor: 'rgba(0, 168, 112, 0.1)',
            tension: 0.4,
            borderWidth: 3,
            fill: true
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: false },
            tooltip: {
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              titleColor: '#000000',
              bodyColor: '#666666'
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              max: 100,
              grid: { color: 'rgba(0, 0, 0, 0.05)' },
              ticks: { color: '#666666' }
            },
            x: {
              grid: { color: 'rgba(0, 0, 0, 0.05)' },
              ticks: { color: '#666666' }
            }
          }
        }
      });
    }
  } catch (error) {
    console.error('初始化用户行为图表失败:', error);
  }
}

// 初始化市场分析图表
function initMarketAnalysisCharts() {
  if (typeof Chart === 'undefined') {
    console.error('Chart.js 未加载');
    return;
  }
  
  if (typeof mockData === 'undefined') {
    console.error('模拟数据未加载');
    return;
  }
  
  try {
    const marketAnalysis = mockData.marketAnalysis || {};
    
    // 高峰时段分析图表
    const peakHoursCtx = document.getElementById('peakHoursChart');
    if (peakHoursCtx) {
      destroyChart('peakHoursChart');
      const peakHours = marketAnalysis.peakHours || [];
      
      window.chartInstances['peakHoursChart'] = new Chart(peakHoursCtx, {
        type: 'bar',
        data: {
          labels: peakHours.map(item => item.hour),
          datasets: [{
            label: '使用频次',
            data: peakHours.map(item => item.usage),
            backgroundColor: '#0052d9',
            borderRadius: 4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: false },
            tooltip: {
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              titleColor: '#000000',
              bodyColor: '#666666'
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              grid: { color: 'rgba(0, 0, 0, 0.05)' },
              ticks: { color: '#666666' }
            },
            x: {
              grid: { color: 'rgba(0, 0, 0, 0.05)' },
              ticks: { color: '#666666' }
            }
          }
        }
      });
    }
    
    // 热点设备排行图表
    const hotDevicesCtx = document.getElementById('hotDevicesChart');
    if (hotDevicesCtx) {
      destroyChart('hotDevicesChart');
      const hotDevices = marketAnalysis.hotDevices || [];
      
      window.chartInstances['hotDevicesChart'] = new Chart(hotDevicesCtx, {
        type: 'bar',
        data: {
          labels: hotDevices.map(item => item.name),
          datasets: [{
            label: '使用次数',
            data: hotDevices.map(item => item.usage),
            backgroundColor: '#ff4d4f',
            borderRadius: 4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          indexAxis: 'y',
          plugins: {
            legend: { display: false },
            tooltip: {
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              titleColor: '#000000',
              bodyColor: '#666666'
            }
          },
          scales: {
            y: {
              grid: { color: 'rgba(0, 0, 0, 0.05)' },
              ticks: { color: '#666666' }
            },
            x: {
              beginAtZero: true,
              grid: { color: 'rgba(0, 0, 0, 0.05)' },
              ticks: { color: '#666666' }
            }
          }
        }
      });
    }
    
    // 区域热度地图图表
    const heatMapCtx = document.getElementById('heatMapChart');
    if (heatMapCtx) {
      destroyChart('heatMapChart');
      const hotLocations = marketAnalysis.hotLocations || [];
      
      window.chartInstances['heatMapChart'] = new Chart(heatMapCtx, {
        type: 'bar',
        data: {
          labels: hotLocations.map(item => item.name),
          datasets: [{
            label: '热度指数',
            data: hotLocations.map(item => item.heat),
            backgroundColor: '#ed7b2f',
            borderRadius: 4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: false },
            tooltip: {
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              titleColor: '#000000',
              bodyColor: '#666666'
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              grid: { color: 'rgba(0, 0, 0, 0.05)' },
              ticks: { color: '#666666' }
            },
            x: {
              grid: { color: 'rgba(0, 0, 0, 0.05)' },
              ticks: { color: '#666666' }
            }
          }
        }
      });
    }
    
    // 市场趋势预测图表
    const trendCtx = document.getElementById('trendPredictionChart');
    if (trendCtx) {
      destroyChart('trendPredictionChart');
      
      window.chartInstances['trendPredictionChart'] = new Chart(trendCtx, {
        type: 'line',
        data: {
          labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
          datasets: [{
            label: '市场趋势',
            data: [100, 120, 140, 160, 180, 200],
            borderColor: '#00a870',
            backgroundColor: 'rgba(0, 168, 112, 0.1)',
            tension: 0.4,
            borderWidth: 3,
            fill: true
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: false },
            tooltip: {
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              titleColor: '#000000',
              bodyColor: '#666666'
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              grid: { color: 'rgba(0, 0, 0, 0.05)' },
              ticks: { color: '#666666' }
            },
            x: {
              grid: { color: 'rgba(0, 0, 0, 0.05)' },
              ticks: { color: '#666666' }
            }
          }
        }
      });
    }
  } catch (error) {
    console.error('初始化市场分析图表失败:', error);
  }
}

// 初始化竞争对手分析图表
function initCompetitorCharts() {
  if (typeof Chart === 'undefined') {
    console.error('Chart.js 未加载');
    return;
  }
  
  if (typeof mockData === 'undefined') {
    console.error('模拟数据未加载');
    return;
  }
  
  try {
    const competitorAnalysis = mockData.competitorAnalysis || {};
    
    // 市场份额分布图表
    const marketShareCtx = document.getElementById('marketShareChart');
    if (marketShareCtx) {
      destroyChart('marketShareChart');
      const competitors = competitorAnalysis.competitors || [];
      
      window.chartInstances['marketShareChart'] = new Chart(marketShareCtx, {
        type: 'doughnut',
        data: {
          labels: competitors.map(item => item.name),
          datasets: [{
            data: competitors.map(item => item.marketShare),
            backgroundColor: ['#0052d9', '#ff4d4f', '#ed7b2f', '#00a870'],
            borderWidth: 0,
            cutout: '60%'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: { color: '#666666', usePointStyle: true }
            }
          }
        }
      });
    }
    
    // 竞争态势分析图表
    const competitiveCtx = document.getElementById('competitiveAnalysisChart');
    if (competitiveCtx) {
      destroyChart('competitiveAnalysisChart');
      
      window.chartInstances['competitiveAnalysisChart'] = new Chart(competitiveCtx, {
        type: 'radar',
        data: {
          labels: ['价格优势', '技术优势', '服务优势', '品牌优势', '渠道优势'],
          datasets: [{
            label: '我方',
            data: [85, 75, 90, 70, 80],
            borderColor: '#0052d9',
            backgroundColor: 'rgba(0, 82, 217, 0.2)',
            borderWidth: 2
          }, {
            label: '竞争对手',
            data: [70, 85, 75, 90, 85],
            borderColor: '#ff4d4f',
            backgroundColor: 'rgba(255, 77, 79, 0.2)',
            borderWidth: 2
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: { color: '#666666' }
            }
          },
          scales: {
            r: {
              beginAtZero: true,
              max: 100,
              ticks: { color: '#666666' },
              grid: { color: 'rgba(0, 0, 0, 0.1)' }
            }
          }
        }
      });
    }
    
    // 价格策略对比图表
    const priceCtx = document.getElementById('priceComparisonChart');
    if (priceCtx) {
      destroyChart('priceComparisonChart');
      
      window.chartInstances['priceComparisonChart'] = new Chart(priceCtx, {
        type: 'bar',
        data: {
          labels: ['我方', '街电', '小电', '怪兽充电', '来电'],
          datasets: [{
            label: '价格策略',
            data: [2.0, 2.0, 1.8, 2.2, 1.9],
            backgroundColor: ['#0052d9', '#ff4d4f', '#ed7b2f', '#00a870', '#ffc107'],
            borderRadius: 4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: false },
            tooltip: {
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              titleColor: '#000000',
              bodyColor: '#666666'
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              grid: { color: 'rgba(0, 0, 0, 0.05)' },
              ticks: { color: '#666666' }
            },
            x: {
              grid: { color: 'rgba(0, 0, 0, 0.05)' },
              ticks: { color: '#666666' }
            }
          }
        }
      });
    }
    
    // 威胁趋势预测图表
    const threatCtx = document.getElementById('threatPredictionChart');
    if (threatCtx) {
      destroyChart('threatPredictionChart');
      
      window.chartInstances['threatPredictionChart'] = new Chart(threatCtx, {
        type: 'line',
        data: {
          labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
          datasets: [{
            label: '威胁指数',
            data: [60, 65, 70, 75, 80, 85],
            borderColor: '#ff4d4f',
            backgroundColor: 'rgba(255, 77, 79, 0.1)',
            tension: 0.4,
            borderWidth: 3,
            fill: true
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: false },
            tooltip: {
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              titleColor: '#000000',
              bodyColor: '#666666'
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              max: 100,
              grid: { color: 'rgba(0, 0, 0, 0.05)' },
              ticks: { color: '#666666' }
            },
            x: {
              grid: { color: 'rgba(0, 0, 0, 0.05)' },
              ticks: { color: '#666666' }
            }
          }
        }
      });
    }
  } catch (error) {
    console.error('初始化竞争对手分析图表失败:', error);
  }
}

// 初始化财务分析图表
function initFinancialCharts() {
  if (typeof Chart === 'undefined') {
    console.error('Chart.js 未加载');
    return;
  }
  
  if (typeof mockData === 'undefined') {
    console.error('模拟数据未加载');
    return;
  }
  
  try {
    const financial = mockData.financial || {};
    
    // 收入趋势图表
    const revenueTrendCtx = document.getElementById('revenueChart');
    if (revenueTrendCtx) {
      destroyChart('revenueChart');
      const monthlyRevenue = financial.monthlyRevenue || [];
      
      window.chartInstances['revenueChart'] = new Chart(revenueTrendCtx, {
        type: 'line',
        data: {
          labels: monthlyRevenue.map(item => item.month),
          datasets: [{
            label: '收入',
            data: monthlyRevenue.map(item => item.revenue),
            borderColor: '#0052d9',
            backgroundColor: 'rgba(0, 82, 217, 0.1)',
            tension: 0.4,
            borderWidth: 3,
            fill: true
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: false },
            tooltip: {
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              titleColor: '#000000',
              bodyColor: '#666666'
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              grid: { color: 'rgba(0, 0, 0, 0.05)' },
              ticks: { color: '#666666' }
            },
            x: {
              grid: { color: 'rgba(0, 0, 0, 0.05)' },
              ticks: { color: '#666666' }
            }
          }
        }
      });
    }
    
    // 收入构成图表
    const revenueCompositionCtx = document.getElementById('revenueCompositionChart');
    if (revenueCompositionCtx) {
      destroyChart('revenueCompositionChart');
      
      window.chartInstances['revenueCompositionChart'] = new Chart(revenueCompositionCtx, {
        type: 'doughnut',
        data: {
          labels: ['充电收入', '广告收入', '会员收入', '其他收入'],
          datasets: [{
            data: [75, 15, 8, 2],
            backgroundColor: ['#0052d9', '#ff4d4f', '#ed7b2f', '#00a870'],
            borderWidth: 0,
            cutout: '60%'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: { color: '#666666', usePointStyle: true }
            }
          }
        }
      });
    }
    
    // 成本分析图表
    const costCtx = document.getElementById('costAnalysisChart');
    if (costCtx) {
      destroyChart('costAnalysisChart');
      const costBreakdown = financial.costBreakdown || [];
      
      window.chartInstances['costAnalysisChart'] = new Chart(costCtx, {
        type: 'bar',
        data: {
          labels: costBreakdown.map(item => item.category),
          datasets: [{
            label: '成本金额',
            data: costBreakdown.map(item => item.amount),
            backgroundColor: '#ff4d4f',
            borderRadius: 4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: false },
            tooltip: {
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              titleColor: '#000000',
              bodyColor: '#666666'
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              grid: { color: 'rgba(0, 0, 0, 0.05)' },
              ticks: { color: '#666666' }
            },
            x: {
              grid: { color: 'rgba(0, 0, 0, 0.05)' },
              ticks: { color: '#666666' }
            }
          }
        }
      });
    }
    
    // 利润预测图表
    const profitCtx = document.getElementById('profitForecastChart');
    if (profitCtx) {
      destroyChart('profitForecastChart');
      
      window.chartInstances['profitForecastChart'] = new Chart(profitCtx, {
        type: 'line',
        data: {
          labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
          datasets: [{
            label: '利润预测',
            data: [20000, 22000, 24000, 26000, 28000, 30000],
            borderColor: '#00a870',
            backgroundColor: 'rgba(0, 168, 112, 0.1)',
            tension: 0.4,
            borderWidth: 3,
            fill: true
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: false },
            tooltip: {
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              titleColor: '#000000',
              bodyColor: '#666666'
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              grid: { color: 'rgba(0, 0, 0, 0.05)' },
              ticks: { color: '#666666' }
            },
            x: {
              grid: { color: 'rgba(0, 0, 0, 0.05)' },
              ticks: { color: '#666666' }
            }
          }
        }
      });
    }
  } catch (error) {
    console.error('初始化财务分析图表失败:', error);
  }
}市场趋势预测