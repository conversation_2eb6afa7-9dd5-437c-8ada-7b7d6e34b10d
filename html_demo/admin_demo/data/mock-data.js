// 模拟数据
const mockData = {
  // 用户信息
  userInfo: {
    username: 'admin',
    password: 'admin123',
    name: '管理员',
    avatar: 'https://tdesign.gtimg.com/starter/default-avatar.png'
  },

  // 设备数据
  devices: [
    { id: 1, name: '充电宝001', type: '标准型', status: '在线', location: '朝阳区万达广场', group: 'A组', battery: 85, usageCount: 156, lastUpdate: '2024-01-15 18:30', lastMaintenance: '2024-01-15', firmware: 'v2.1.0', serialNumber: 'CP001', mac: '00:11:22:33:44:01', ip: '*************', installDate: '2023-01-10', warranty: '2025-01-10', vendor: '小米', remark: '无' },
    { id: 2, name: '充电宝002', type: '快充型', status: '在线', location: '浦东区陆家嘴', group: 'A组', battery: 92, usageCount: 142, lastUpdate: '2024-01-15 19:15', lastMaintenance: '2024-01-10', firmware: 'v2.1.0', serialNumber: 'CP002', mac: '00:11:22:33:44:02', ip: '*************', installDate: '2023-01-12', warranty: '2025-01-12', vendor: '华为', remark: '无' },
    { id: 3, name: '充电宝003', type: '标准型', status: '离线', location: '天河区珠江新城', group: 'B组', battery: 45, usageCount: 98, lastUpdate: '2024-01-15 14:20', lastMaintenance: '2024-01-08', firmware: 'v2.0.5', serialNumber: 'CP003', mac: '00:11:22:33:44:03', ip: '*************', installDate: '2023-01-15', warranty: '2025-01-15', vendor: '小米', remark: '需维护' },
    { id: 4, name: '充电宝004', type: '无线型', status: '故障', location: '南山区科技园', group: 'B组', battery: 12, usageCount: 67, lastUpdate: '2024-01-15 10:30', lastMaintenance: '2024-01-05', firmware: 'v2.0.5', serialNumber: 'CP004', mac: '00:11:22:33:44:04', ip: '*************', installDate: '2023-01-18', warranty: '2025-01-18', vendor: '华为', remark: '电池故障' },
    { id: 5, name: '充电宝005', type: '快充型', status: '在线', location: '西湖区文三路', group: 'C组', battery: 78, usageCount: 125, lastUpdate: '2024-01-15 17:30', lastMaintenance: '2024-01-12', firmware: 'v2.1.0', serialNumber: 'CP005', mac: '00:11:22:33:44:05', ip: '*************', installDate: '2023-01-20', warranty: '2025-01-20', vendor: '小米', remark: '无' },
    { id: 6, name: '充电宝006', type: '标准型', status: '在线', location: '朝阳区三里屯', group: 'A组', battery: 95, usageCount: 138, lastUpdate: '2024-01-15 21:20', lastMaintenance: '2024-01-14', firmware: 'v2.1.0', serialNumber: 'CP006', mac: '00:11:22:33:44:06', ip: '*************', installDate: '2023-01-22', warranty: '2025-01-22', vendor: '华为', remark: '无' },
    { id: 7, name: '充电宝007', type: '无线型', status: '离线', location: '浦东区张江', group: 'B组', battery: 33, usageCount: 89, lastUpdate: '2024-01-15 16:10', lastMaintenance: '2024-01-06', firmware: 'v2.0.5', serialNumber: 'CP007', mac: '00:11:22:33:44:07', ip: '*************', installDate: '2023-01-25', warranty: '2025-01-25', vendor: '小米', remark: '需检查网络' },
    { id: 8, name: '充电宝008', type: '快充型', status: '在线', location: '天河区体育西路', group: 'C组', battery: 88, usageCount: 118, lastUpdate: '2024-01-15 22:15', lastMaintenance: '2024-01-13', firmware: 'v2.1.0', serialNumber: 'CP008', mac: '00:11:22:33:44:08', ip: '*************', installDate: '2023-01-28', warranty: '2025-01-28', vendor: '华为', remark: '无' }
  ],

  // 点位数据
  locations: [
    { id: 1, name: '万达广场', address: '朝阳区建国路93号', area: '朝阳区', deviceCount: 15, usageRate: 85, revenue: 2850, heat: 95, status: '正常', manager: '张三', phone: '13800000001', openTime: '08:00', closeTime: '22:00', remark: '商圈核心点位' },
    { id: 2, name: '陆家嘴金融中心', address: '浦东区世纪大道100号', area: '浦东区', deviceCount: 20, usageRate: 78, revenue: 2640, heat: 88, status: '正常', manager: '李四', phone: '13800000002', openTime: '08:00', closeTime: '22:00', remark: '写字楼集中' },
    { id: 3, name: '珠江新城', address: '天河区珠江新城花城大道', area: '天河区', deviceCount: 12, usageRate: 58, revenue: 2280, heat: 76, status: '正常', manager: '王五', phone: '13800000003', openTime: '08:00', closeTime: '22:00', remark: 'CBD' },
    { id: 4, name: '科技园', address: '南山区高新南一道', area: '南山区', deviceCount: 18, usageRate: 65, revenue: 2460, heat: 82, status: '停用', manager: '赵六', phone: '13800000004', openTime: '08:00', closeTime: '22:00', remark: '园区' },
    { id: 5, name: '文三路', address: '西湖区文三路259号', area: '西湖区', deviceCount: 10, usageRate: 42, revenue: 1890, heat: 65, status: '正常', manager: '钱七', phone: '13800000005', openTime: '08:00', closeTime: '22:00', remark: '高校周边' },
    { id: 6, name: '三里屯', address: '朝阳区三里屯路19号', area: '朝阳区', deviceCount: 25, usageRate: 92, revenue: 2760, heat: 92, status: '正常', manager: '孙八', phone: '13800000006', openTime: '08:00', closeTime: '22:00', remark: '夜生活聚集地' },
    { id: 7, name: '张江高科技园区', address: '浦东区张江高科技园区', area: '浦东区', deviceCount: 16, usageRate: 72, revenue: 2520, heat: 71, status: '正常', manager: '周九', phone: '13800000007', openTime: '08:00', closeTime: '22:00', remark: '科技园区' },
    { id: 8, name: '体育西路', address: '天河区体育西路191号', area: '天河区', deviceCount: 14, usageRate: 68, revenue: 2380, heat: 68, status: '正常', manager: '吴十', phone: '13800000008', openTime: '08:00', closeTime: '22:00', remark: '交通枢纽' }
  ],

  // 用户行为数据
  userBehavior: {
    totalUsers: 12580,
    activeUsers: 8560,
    avgUsageTime: 2.5,
    repurchaseRate: 68.5,
    satisfaction: 92,
    users: [
      { id: 'U001', type: '新用户', activity: '高', usageCount: 15, avgTime: 2.8, lastUsage: '2024-01-15 18:30', preferredTime: '18:00-20:00', gender: '男', age: 24, region: '朝阳区', phone: '13800138011', email: '<EMAIL>' },
      { id: 'U002', type: '活跃用户', activity: '高', usageCount: 28, avgTime: 2.2, lastUsage: '2024-01-15 19:15', preferredTime: '19:00-21:00', gender: '女', age: 29, region: '浦东区', phone: '13800138012', email: '<EMAIL>' },
      { id: 'U003', type: '流失用户', activity: '低', usageCount: 3, avgTime: 1.5, lastUsage: '2024-01-10 14:20', preferredTime: '14:00-16:00', gender: '男', age: 35, region: '天河区', phone: '13800138013', email: '<EMAIL>' },
      { id: 'U004', type: '活跃用户', activity: '中', usageCount: 12, avgTime: 2.1, lastUsage: '2024-01-15 20:45', preferredTime: '20:00-22:00', gender: '女', age: 31, region: '南山区', phone: '13800138014', email: '<EMAIL>' },
      { id: 'U005', type: '新用户', activity: '中', usageCount: 8, avgTime: 2.3, lastUsage: '2024-01-15 17:30', preferredTime: '17:00-19:00', gender: '男', age: 22, region: '西湖区', phone: '13800138015', email: '<EMAIL>' },
      { id: 'U006', type: '活跃用户', activity: '高', usageCount: 22, avgTime: 2.6, lastUsage: '2024-01-15 21:20', preferredTime: '21:00-23:00', gender: '女', age: 27, region: '朝阳区', phone: '13800138016', email: '<EMAIL>' },
      { id: 'U007', type: '流失用户', activity: '低', usageCount: 5, avgTime: 1.8, lastUsage: '2024-01-08 16:10', preferredTime: '16:00-18:00', gender: '男', age: 40, region: '浦东区', phone: '13800138017', email: '<EMAIL>' },
      { id: 'U008', type: '新用户', activity: '高', usageCount: 18, avgTime: 2.4, lastUsage: '2024-01-15 22:15', preferredTime: '22:00-24:00', gender: '女', age: 23, region: '天河区', phone: '13800138018', email: '<EMAIL>' }
    ],
    dailyStats: [
      { date: '2024-01-01', activeUsers: 1250, borrowCount: 3200, returnCount: 3150 },
      { date: '2024-01-02', activeUsers: 1380, borrowCount: 3500, returnCount: 3480 },
      { date: '2024-01-03', activeUsers: 1420, borrowCount: 3650, returnCount: 3620 },
      { date: '2024-01-04', activeUsers: 1350, borrowCount: 3400, returnCount: 3380 },
      { date: '2024-01-05', activeUsers: 1480, borrowCount: 3800, returnCount: 3780 },
      { date: '2024-01-06', activeUsers: 1520, borrowCount: 3900, returnCount: 3880 },
      { date: '2024-01-07', activeUsers: 1450, borrowCount: 3700, returnCount: 3680 }
    ],
    userProfile: {
      gender: { male: 58, female: 42 },
      age: { '18-25': 25, '26-35': 45, '36-45': 20, '46+': 10 },
      region: { '朝阳区': 30, '浦东区': 25, '天河区': 20, '南山区': 15, '西湖区': 10 }
    },
    genderDistribution: { male: 58, female: 42 },
    ageDistribution: { '18-25': 25, '26-35': 45, '36-45': 20, '46+': 10 },
    regionDistribution: { '朝阳区': 30, '浦东区': 25, '天河区': 20, '南山区': 15, '西湖区': 10 }
  },

  // 市场分析数据
  market: {
    hotLocations: 15,
    peakTime: '18:00-20:00',
    trend: '上升',
    hotspots: [
      { id: 'H001', name: '万达广场', area: '朝阳区', heat: '热门', deviceCount: 15, usageRate: 85, revenue: 2850 },
      { id: 'H002', name: '陆家嘴金融中心', area: '浦东区', heat: '热门', deviceCount: 20, usageRate: 78, revenue: 2640 },
      { id: 'H003', name: '三里屯', area: '朝阳区', heat: '热门', deviceCount: 25, usageRate: 92, revenue: 2760 },
      { id: 'H004', name: '科技园', area: '南山区', heat: '普通', deviceCount: 18, usageRate: 65, revenue: 2460 },
      { id: 'H005', name: '珠江新城', area: '天河区', heat: '普通', deviceCount: 12, usageRate: 58, revenue: 2280 },
      { id: 'H006', name: '文三路', area: '西湖区', heat: '冷门', deviceCount: 10, usageRate: 42, revenue: 1890 },
      { id: 'H007', name: '张江高科技园区', area: '浦东区', heat: '普通', deviceCount: 16, usageRate: 72, revenue: 2520 },
      { id: 'H008', name: '体育西路', area: '天河区', heat: '普通', deviceCount: 14, usageRate: 68, revenue: 2380 }
    ]
  },
  marketAnalysis: {
    hotSpots: 15,
    peakHours: '18:00-22:00',
    growthRate: 12.5,
    competitionIndex: 7.8,
    hotDevices: [
      { name: '充电宝001', usage: 156, revenue: 468, location: '朝阳区万达广场', status: '在线', type: '标准型' },
      { name: '充电宝002', usage: 142, revenue: 426, location: '浦东区陆家嘴', status: '在线', type: '快充型' },
      { name: '充电宝006', usage: 138, revenue: 414, location: '朝阳区三里屯', status: '在线', type: '标准型' },
      { name: '充电宝005', usage: 125, revenue: 375, location: '西湖区文三路', status: '在线', type: '快充型' },
      { name: '充电宝008', usage: 118, revenue: 354, location: '天河区体育西路', status: '在线', type: '快充型' }
    ],
    hotLocations: [
      { name: '万达广场', devices: 15, heat: 95, revenue: 2850, area: '朝阳区' },
      { name: '陆家嘴金融中心', devices: 20, heat: 88, revenue: 2640, area: '浦东区' },
      { name: '三里屯', devices: 25, heat: 92, revenue: 2760, area: '朝阳区' },
      { name: '科技园', devices: 18, heat: 82, revenue: 2460, area: '南山区' },
      { name: '珠江新城', devices: 12, heat: 76, revenue: 2280, area: '天河区' }
    ],
    peakHours: [
      { hour: '08:00', usage: 45 },
      { hour: '09:00', usage: 78 },
      { hour: '10:00', usage: 92 },
      { hour: '11:00', usage: 85 },
      { hour: '12:00', usage: 120 },
      { hour: '13:00', usage: 95 },
      { hour: '14:00', usage: 88 },
      { hour: '15:00', usage: 102 },
      { hour: '16:00', usage: 115 },
      { hour: '17:00', usage: 135 },
      { hour: '18:00', usage: 145 },
      { hour: '19:00', usage: 128 },
      { hour: '20:00', usage: 95 },
      { hour: '21:00', usage: 75 },
      { hour: '22:00', usage: 45 }
    ]
  },

  // 竞争对手数据
  competitors: [
    { name: '街电', devices: 1200, locations: 85, price: 2.0, marketShare: 35, strategy: '价格战', activity: '高', contact: '400-800-8888', website: 'https://www.jiadian.com' },
    { name: '小电', devices: 980, locations: 72, price: 1.8, marketShare: 28, strategy: '服务优化', activity: '中', contact: '400-800-6666', website: 'https://www.xdian.com' },
    { name: '怪兽充电', devices: 850, locations: 65, price: 2.2, marketShare: 22, strategy: '技术创新', activity: '高', contact: '400-800-5555', website: 'https://www.gmonster.com' },
    { name: '来电', devices: 720, locations: 58, price: 1.9, marketShare: 15, strategy: '市场扩张', activity: '低', contact: '400-800-3333', website: 'https://www.laidian.com' }
  ],
  competitorAnalysis: {
    marketShare: 28.5,
    competitors: [
      { name: '街电', marketShare: 35.2, threatLevel: '高', position: '市场领导者', deviceCount: 12500, priceStrategy: '高端定位', lastActivity: '2024-01-15 新增500台设备', website: 'https://www.jiadian.com' },
      { name: '小电', marketShare: 22.8, threatLevel: '高', position: '挑战者', deviceCount: 8900, priceStrategy: '价格战', lastActivity: '2024-01-14 降价促销', website: 'https://www.xdian.com' },
      { name: '怪兽充电', marketShare: 18.5, threatLevel: '中', position: '跟随者', deviceCount: 7200, priceStrategy: '中端定位', lastActivity: '2024-01-13 优化用户体验', website: 'https://www.gmonster.com' },
      { name: '来电', marketShare: 12.3, threatLevel: '中', position: '跟随者', deviceCount: 5800, priceStrategy: '差异化服务', lastActivity: '2024-01-12 推出新功能', website: 'https://www.laidian.com' }
    ],
    advantages: 5,
    threatLevel: 7
  },

  // 财务数据
  financial: {
    totalRevenue: 125000,
    netProfit: 41000,
    profitMargin: 32.8,
    costControl: 85,
    totalCost: 440000,
    costBreakdown: [
      { category: '设备采购', amount: 150000 },
      { category: '运营维护', amount: 80000 },
      { category: '人工成本', amount: 120000 },
      { category: '场地租金', amount: 60000 },
      { category: '其他费用', amount: 30000 }
    ],
    monthlyRevenue: [
      { month: '2024-01', revenue: 125000, orders: 12500, refunds: 125, cost: 105000, profit: 20000 },
      { month: '2023-12', revenue: 118000, orders: 11800, refunds: 118, cost: 98000, profit: 20000 },
      { month: '2023-11', revenue: 112000, orders: 11200, refunds: 112, cost: 92000, profit: 20000 },
      { month: '2023-10', revenue: 108000, orders: 10800, refunds: 108, cost: 88000, profit: 20000 },
      { month: '2023-09', revenue: 105000, orders: 10500, refunds: 105, cost: 85000, profit: 20000 },
      { month: '2023-08', revenue: 102000, orders: 10200, refunds: 102, cost: 82000, profit: 20000 }
    ],
    dailyRevenue: [
      { date: '2024-01-01', revenue: 4200, type: '充电收入', cost: 2800, orders: 420, avgOrder: 10.0, refunds: 2 },
      { date: '2024-01-02', revenue: 4500, type: '充电收入', cost: 3000, orders: 450, avgOrder: 10.0, refunds: 3 },
      { date: '2024-01-03', revenue: 4800, type: '充电收入', cost: 3200, orders: 480, avgOrder: 10.0, refunds: 1 },
      { date: '2024-01-04', revenue: 4600, type: '充电收入', cost: 3100, orders: 460, avgOrder: 10.0, refunds: 2 },
      { date: '2024-01-05', revenue: 5200, type: '充电收入', cost: 3500, orders: 520, avgOrder: 10.0, refunds: 4 },
      { date: '2024-01-06', revenue: 5400, type: '充电收入', cost: 3600, orders: 540, avgOrder: 10.0, refunds: 2 },
      { date: '2024-01-07', revenue: 5100, type: '充电收入', cost: 3400, orders: 510, avgOrder: 10.0, refunds: 3 }
    ],
    revenue: [
      { month: '1月', amount: 125000 },
      { month: '2月', amount: 118000 },
      { month: '3月', amount: 112000 },
      { month: '4月', amount: 108000 },
      { month: '5月', amount: 105000 },
      { month: '6月', amount: 102000 }
    ]
  },

  // 维护工单数据
  maintenance: [
    { id: 1, deviceId: 4, type: '电池故障', priority: '高', assignee: '张师傅', status: '处理中', createTime: '2024-01-15 10:30', description: '设备电池无法充电', contact: '13900000001', remark: '需更换电池' },
    { id: 2, deviceId: 7, type: '网络连接', priority: '中', assignee: '李师傅', status: '待处理', createTime: '2024-01-15 14:20', description: '设备网络连接异常', contact: '13900000002', remark: '需排查网络' },
    { id: 3, deviceId: 2, type: '固件升级', priority: '低', assignee: '王师傅', status: '已完成', createTime: '2024-01-14 09:15', description: '固件版本升级', contact: '13900000003', remark: '已升级' },
    { id: 4, deviceId: 6, type: '硬件故障', priority: '高', assignee: '张师傅', status: '处理中', createTime: '2024-01-15 16:45', description: '设备硬件损坏', contact: '13900000004', remark: '需更换主板' },
    { id: 5, deviceId: 1, type: '其他', priority: '中', assignee: '李师傅', status: '待处理', createTime: '2024-01-15 11:30', description: '其他维护需求', contact: '13900000005', remark: '待确认' }
  ],
  maintenanceOrders: [],

  // 系统数据
  system: {
    onlineUsers: 12,
    systemLoad: 45,
    storageUsage: 68,
    networkStatus: '正常',
    cpu: 38,
    memory: 62,
    disk: 68,
    uptime: '15天4小时',
    version: 'v3.2.1',
    users: [
      { id: 'U001', username: 'admin', role: 'admin', status: 'active', lastLogin: '2024-01-15 10:30', department: '技术部', email: '<EMAIL>', phone: '13800138000' },
      { id: 'U002', username: 'operator1', role: 'operator', status: 'active', lastLogin: '2024-01-15 09:15', department: '运维部', email: '<EMAIL>', phone: '13800138001' },
      { id: 'U003', username: 'operator2', role: 'operator', status: 'inactive', lastLogin: '2024-01-14 16:45', department: '运维部', email: '<EMAIL>', phone: '13800138002' },
      { id: 'U004', username: 'viewer1', role: 'viewer', status: 'active', lastLogin: '2024-01-15 08:30', department: '市场部', email: '<EMAIL>', phone: '13800138003' }
    ]
  },

  // 系统日志
  systemLogs: [
    { id: 1, type: '设备', user: 'admin', action: '添加设备', time: '2024-01-15 16:30', ip: '*************', details: '添加设备充电宝009', level: 'INFO', module: '设备管理', message: '添加设备充电宝009', timestamp: '2024-01-15T16:30:00Z' },
    { id: 2, type: '点位', user: 'admin', action: '编辑点位', time: '2024-01-15 15:45', ip: '*************', details: '编辑点位万达广场信息', level: 'INFO', module: '点位管理', message: '编辑点位万达广场信息', timestamp: '2024-01-15T15:45:00Z' },
    { id: 3, type: '维护', user: 'admin', action: '创建工单', time: '2024-01-15 14:20', ip: '*************', details: '创建维护工单#001', level: 'INFO', module: '维护管理', message: '创建维护工单#001', timestamp: '2024-01-15T14:20:00Z' },
    { id: 4, type: '系统', user: 'system', action: '自动备份', time: '2024-01-15 12:00', ip: '127.0.0.1', details: '系统数据自动备份完成', level: 'INFO', module: '系统备份', message: '系统数据自动备份完成', timestamp: '2024-01-15T12:00:00Z' },
    { id: 5, type: '用户', user: 'admin', action: '导出报告', time: '2024-01-15 10:15', ip: '*************', details: '导出财务分析报告', level: 'INFO', module: '报告管理', message: '导出财务分析报告', timestamp: '2024-01-15T10:15:00Z' },
    { id: 6, type: '设备', user: 'admin', action: '删除设备', time: '2024-01-15 09:30', ip: '*************', details: '删除设备充电宝010', level: 'WARNING', module: '设备管理', message: '删除设备充电宝010', timestamp: '2024-01-15T09:30:00Z' },
    { id: 7, type: '点位', user: 'admin', action: '添加点位', time: '2024-01-15 08:45', ip: '*************', details: '添加新点位', level: 'INFO', module: '点位管理', message: '添加新点位', timestamp: '2024-01-15T08:45:00Z' },
    { id: 8, type: '系统', user: 'system', action: '系统启动', time: '2024-01-15 08:00', ip: '127.0.0.1', details: '系统正常启动', level: 'INFO', module: '系统启动', message: '系统正常启动', timestamp: '2024-01-15T08:00:00Z' },
    { id: 9, type: '设备', user: 'system', action: '设备故障', time: '2024-01-15 07:30', ip: '127.0.0.1', details: '设备充电宝004电池故障', level: 'ERROR', module: '设备监控', message: '设备充电宝004电池故障', timestamp: '2024-01-15T07:30:00Z' },
    { id: 10, type: '网络', user: 'system', action: '网络异常', time: '2024-01-15 06:15', ip: '127.0.0.1', details: '网络连接超时', level: 'WARNING', module: '网络监控', message: '网络连接超时', timestamp: '2024-01-15T06:15:00Z' }
  ],

  // 统计数据
  statistics: {
    todayRevenue: 5100,
    todayOrders: 510,
    monthlyRevenue: 125000,
    monthlyOrders: 12500,
    totalRevenue: 750000,
    totalUsers: 12580,
    totalDevices: 8,
    onlineDevices: 5,
    totalLocations: 8,
    activeLocations: 7
  }
};