### 1
文件路径：/admin
对应的 README 路径：/README.md
- 后台为 html,js,css 文件
- 后台功能所有页面文件都在 /admin/pages 下
- 后台登录页面文件为 /admin/index.html
- 后台登录以后的面板文件为 /admin/home.html

后台测试路径为：http://localhost:8000/index.html
不需要考虑 admin 文件夹，因为测试服务器根目录就是 admin 内。
如果有创建目录、文件 的需要，应该一个命令批量执行完成。而不是多个命令。

需求如下：
1. 制作一个后台，代码放到目录：/admin
2. 默认先展示登录页面，使用这个demo ：/html_demo/login_demo/index.html, 默认用户名 admin 密码 admin123
3. 登录以后，跳转到控制台，/admin/index.html 作为后台首页
3. 先创建 menu.js，用来控制菜单功能，保存到 /admin/js，内容参考 /README.md
4. 制作 common.js 文件，保存到 /admin/js，后台每个功能都应该有：数据搜索，数据导出（CSV），数据筛选，分页，功能。common 只负责实现逻辑，样式还是有当前页面的 html 和 css 去实现。
4. 后台样式参考 /html_demo/admin_demo。
6. 后台功能页面的 mock 数据，希望使用 json 格式，对应的测试用数据文件，存放路径为：/admin/data，mock 数据每个功能至少 100 条
7. 先实现 menu.js 页面不要用 锚点，而是用 url 直接访问的方式
8. 根据 menu.js 上的页面文件列表，先生成所有的 mock 数据。

### 2

文件路径：/admin
对应的 README 路径：/README.md
- 后台为 html,js,css 文件
- 后台功能所有页面文件都在 /admin/pages 下
- 后台登录页面文件为 /admin/index.html
- 后台登录以后的面板文件为 /admin/home.html

后台测试路径为：http://localhost:8000/index.html
不需要考虑 admin 文件夹，因为测试服务器根目录就是 admin 内。
如果有创建目录、文件 的需要，应该一个命令批量执行完成。而不是多个命令。

2. 后台功能页面的 mock 数据，希望使用 json 格式，对应的测试用数据文件，存放路径为：/admin/data，mock 数据每个功能至少 50 条, 体现出分页功能。
3. /README.md 里有需要制作的文件清单： ## 需要制作的页面，先制作下列页面
    ### 智能巡检管理

- **自动化巡检流程**: `auto_inspection.html`
- **多终端协同操作**: `multi_terminal.html`
- **异常闭环管理**: `exception_management.html`

4. 每个页面的按钮需要有弹出层，弹出层需要有【关闭】按钮，点击【关闭】按钮，弹出层关闭。弹出层的交互界面应该美观和 home.html 样式保持一致
5. 功能页面的 css 和 js 是单独文件，保存到 /admin/css 和 /admin/js

### 3

文件路径：/admin
对应的 README 路径：/README.md
- 后台为 html,js,css 文件
- 后台功能所有页面文件都在 /admin/pages 下
- 后台登录页面文件为 /admin/index.html
- 后台登录以后的面板文件为 /admin/home.html

后台测试路径为：http://localhost:8000/index.html
不需要考虑 admin 文件夹，因为测试服务器根目录就是 admin 内。
如果有创建目录、文件 的需要，应该一个命令批量执行完成。而不是多个命令。

1. menu.js MENU_CONFIG 变量期望最上面增加     {title: '仪表盘', url: './home.html'}
2. home.html 顶部是蓝色的，菜单颜色也不对，页面样式参考 ai_inspection.html，这 2 点应该每个页面都统一
3. ai_inspection.html 页面 ，点击菜单，其他的大分类，没法展开，这个问题解决一下。
4. 解决上述问题以后，继续依据 /README.md 描述，继续制作其他页面。

### 4

文件路径：/admin
对应的 README 路径：/README.md
- 后台为 html,js,css 文件
- 后台功能所有页面文件都在 /admin/pages 下
- 后台登录页面文件为 /admin/index.html
- 后台登录以后的面板文件为 /admin/home.html

后台测试路径为：http://localhost:8000/index.html
不需要考虑 admin 文件夹，因为测试服务器根目录就是 admin 内。
如果有创建目录、文件 的需要，应该一个命令批量执行完成。而不是多个命令。

1. auto_inspection.html 页面顶部 banner 样式 和 ai_inspection.html 不一致，以 ai_inspection.html 为准。
2. home.html 页面顶部 banner 样式 和 ai_inspection.html 不一致，以 ai_inspection.html 为准。
    - 菜单高亮颜色 也和 ai_inspection.html 不一致，以 ai_inspection.html 为准。
    - 菜单顶部样式不一致，菜单应该抽象出 menu.css 文件，保存到 /admin/css，每个页面统一引用这个 css 文件，菜单最后效果应该以 ai_inspection.html 为准。
3. 制作页面先创建 mock 数据
4. 再制作 html 页面，最后制作 css 和 js 保证页面上的元素都是适配的，而不会产生，对象不存在，css 名称不对这样的问题。

### 5
1. 菜单顶部应该统一用 menu.js 的 MENU_TITLE 变量，保证菜单顶部的页面内容是一样的。
2. 