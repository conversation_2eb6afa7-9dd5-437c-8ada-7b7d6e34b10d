/* 异常闭环管理页面样式 */

/* 统计卡片样式 */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: var(--td-bg-color-container);
    border: 1px solid var(--td-border-level-1-color);
    border-radius: var(--td-radius-default);
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 16px;
    transition: all 0.3s ease;
    box-shadow: var(--td-shadow-1);
}

.stat-card:hover {
    box-shadow: var(--td-shadow-2);
    transform: translateY(-2px);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.stat-icon.critical {
    background: var(--td-error-color);
}

.stat-icon.high {
    background: var(--td-warning-color);
}

.stat-icon.medium {
    background: var(--td-brand-color);
}

.stat-icon.low {
    background: var(--td-success-color);
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 28px;
    font-weight: 600;
    color: var(--td-text-color-primary);
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: var(--td-text-color-secondary);
}

/* 优先级标签样式 */
.priority-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    color: white;
    text-align: center;
    min-width: 60px;
}

.priority-badge.critical {
    background: var(--td-error-color);
}

.priority-badge.high {
    background: var(--td-warning-color);
}

.priority-badge.medium {
    background: var(--td-brand-color);
}

.priority-badge.low {
    background: var(--td-success-color);
}

/* 状态标签样式 */
.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    min-width: 60px;
}

.status-badge.pending {
    background: var(--td-warning-color-1);
    color: var(--td-warning-color);
    border: 1px solid var(--td-warning-color-3);
}

.status-badge.processing {
    background: var(--td-brand-color-1);
    color: var(--td-brand-color);
    border: 1px solid var(--td-brand-color-3);
}

.status-badge.resolved {
    background: var(--td-success-color-1);
    color: var(--td-success-color);
    border: 1px solid var(--td-success-color-3);
}

.status-badge.closed {
    background: var(--td-gray-color-1);
    color: var(--td-gray-color-6);
    border: 1px solid var(--td-gray-color-3);
}

/* SLA状态样式 */
.sla-status {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 500;
}

.sla-status.normal {
    color: var(--td-success-color);
}

.sla-status.warning {
    color: var(--td-warning-color);
}

.sla-status.overdue {
    color: var(--td-error-color);
}

.sla-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
}

/* 操作按钮样式 */
.action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

.btn-action {
    padding: 4px 8px;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.btn-action.view {
    background: var(--td-brand-color-1);
    color: var(--td-brand-color);
}

.btn-action.view:hover {
    background: var(--td-brand-color-2);
}

.btn-action.process {
    background: var(--td-warning-color-1);
    color: var(--td-warning-color);
}

.btn-action.process:hover {
    background: var(--td-warning-color-2);
}

.btn-action.resolve {
    background: var(--td-success-color-1);
    color: var(--td-success-color);
}

.btn-action.resolve:hover {
    background: var(--td-success-color-2);
}

.btn-action.close {
    background: var(--td-gray-color-1);
    color: var(--td-gray-color-6);
}

.btn-action.close:hover {
    background: var(--td-gray-color-2);
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: var(--td-bg-color-container);
    margin: 5% auto;
    padding: 0;
    border-radius: var(--td-radius-default);
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: var(--td-shadow-3);
}

.modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--td-border-level-1-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--td-text-color-primary);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--td-text-color-secondary);
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: var(--td-bg-color-component-hover);
    color: var(--td-text-color-primary);
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    padding: 16px 24px;
    border-top: 1px solid var(--td-border-level-1-color);
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--td-text-color-primary);
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--td-border-level-1-color);
    border-radius: var(--td-radius-default);
    font-size: 14px;
    transition: all 0.2s ease;
    background: var(--td-bg-color-container);
    color: var(--td-text-color-primary);
}

.form-control:focus {
    outline: none;
    border-color: var(--td-brand-color);
    box-shadow: 0 0 0 2px var(--td-brand-color-1);
}

.form-control.textarea {
    resize: vertical;
    min-height: 80px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

/* 详情页面样式 */
.detail-section {
    margin-bottom: 24px;
}

.detail-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--td-text-color-primary);
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--td-border-level-1-color);
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.detail-label {
    font-size: 12px;
    color: var(--td-text-color-secondary);
    font-weight: 500;
}

.detail-value {
    font-size: 14px;
    color: var(--td-text-color-primary);
}

/* 时间轴样式 */
.timeline {
    position: relative;
    padding-left: 32px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 16px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--td-border-level-1-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 24px;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -24px;
    top: 4px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--td-brand-color);
    border: 2px solid var(--td-bg-color-container);
}

.timeline-content {
    background: var(--td-bg-color-container);
    border: 1px solid var(--td-border-level-1-color);
    border-radius: var(--td-radius-default);
    padding: 16px;
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.timeline-title {
    font-weight: 600;
    color: var(--td-text-color-primary);
}

.timeline-time {
    font-size: 12px;
    color: var(--td-text-color-secondary);
}

.timeline-description {
    color: var(--td-text-color-secondary);
    font-size: 14px;
    line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stats-cards {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .detail-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .action-buttons {
        flex-direction: column;
        gap: 4px;
    }

    .btn-action {
        width: 100%;
        justify-content: center;
    }
}

/* 加载状态 */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: var(--td-text-color-secondary);
}

.loading::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid var(--td-border-level-1-color);
    border-top: 2px solid var(--td-brand-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--td-text-color-secondary);
}

.empty-state-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state-text {
    font-size: 16px;
    margin-bottom: 8px;
}

.empty-state-desc {
    font-size: 14px;
    opacity: 0.7;
}