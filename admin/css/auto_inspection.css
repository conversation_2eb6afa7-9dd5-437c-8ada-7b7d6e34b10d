/* ========================================
   自动化巡检流程页面样式
   ======================================== */

/* 工具栏样式 */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: var(--td-bg-color-container);
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: var(--td-shadow-1);
}

.toolbar-left {
    display: flex;
    gap: 15px;
    align-items: center;
}

.toolbar-right {
    display: flex;
    gap: 10px;
}

/* 搜索框样式 */
.search-box {
    display: flex;
    align-items: center;
    background: var(--td-bg-color-secondarycontainer);
    border-radius: 6px;
    border: 1px solid var(--td-border-color);
    overflow: hidden;
}

.search-input {
    border: none;
    outline: none;
    padding: 8px 12px;
    background: transparent;
    font-size: 14px;
    width: 280px;
    color: var(--td-text-color-primary);
}

.search-input::placeholder {
    color: var(--td-text-color-placeholder);
}

.search-btn {
    background: var(--td-brand-color);
    color: white;
    border: none;
    padding: 8px 12px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.search-btn:hover {
    background: var(--td-brand-color-hover);
}

/* 筛选框样式 */
.filter-box {
    display: flex;
    gap: 10px;
}

.filter-box select {
    padding: 8px 12px;
    border: 1px solid var(--td-border-color);
    border-radius: 6px;
    background: var(--td-bg-color-container);
    color: var(--td-text-color-primary);
    font-size: 14px;
    cursor: pointer;
    outline: none;
}

.filter-box select:focus {
    border-color: var(--td-brand-color);
}

/* 统计卡片样式 */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.stat-card {
    background: var(--td-bg-color-container);
    border-radius: 8px;
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    box-shadow: var(--td-shadow-1);
    transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--td-shadow-2);
}

.stat-icon {
    font-size: 32px;
    padding: 12px;
    border-radius: 50%;
    background: var(--td-brand-color-light);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 56px;
    height: 56px;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 28px;
    font-weight: 700;
    color: var(--td-text-color-primary);
    line-height: 1;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: var(--td-text-color-secondary);
    font-weight: 500;
}

/* 表格样式 */
.table-container {
    background: var(--td-bg-color-container);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--td-shadow-1);
    margin-bottom: 20px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.data-table th {
    background: var(--td-bg-color-secondarycontainer);
    color: var(--td-text-color-primary);
    font-weight: 600;
    padding: 16px 12px;
    text-align: left;
    border-bottom: 1px solid var(--td-border-color);
    cursor: pointer;
    user-select: none;
    position: relative;
}

.data-table th:hover {
    background: var(--td-brand-color-light);
}

.data-table td {
    padding: 16px 12px;
    border-bottom: 1px solid var(--td-border-color-light);
    color: var(--td-text-color-primary);
}

.data-table tbody tr:hover {
    background: var(--td-bg-color-secondarycontainer);
}

/* 排序指示器 */
.sort-indicator {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.5;
    transition: opacity 0.2s;
}

.data-table th:hover .sort-indicator {
    opacity: 1;
}

.data-table th.sort-asc .sort-indicator::after {
    content: '↑';
    color: var(--td-brand-color);
}

.data-table th.sort-desc .sort-indicator::after {
    content: '↓';
    color: var(--td-brand-color);
}

/* 状态标签样式 */
.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    min-width: 60px;
}

.status-badge.completed {
    background: var(--td-success-color-light);
    color: var(--td-success-color);
}

.status-badge.processing {
    background: var(--td-brand-color-light);
    color: var(--td-brand-color);
}

.status-badge.planned {
    background: var(--td-warning-color-light);
    color: var(--td-warning-color);
}

.status-badge.error {
    background: var(--td-error-color-light);
    color: var(--td-error-color);
}

/* 进度条样式 */
.progress-bar {
    width: 100px;
    height: 6px;
    background: var(--td-border-color-light);
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    border-radius: 3px;
    transition: width 0.3s ease;
}

.progress-fill.high {
    background: var(--td-success-color);
}

.progress-fill.medium {
    background: var(--td-warning-color);
}

.progress-fill.low {
    background: var(--td-error-color);
}

/* 效率指标样式 */
.efficiency-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.efficiency-value {
    font-weight: 500;
    width: 45px;
}

.efficiency-high {
    color: var(--td-success-color);
}

.efficiency-medium {
    color: var(--td-warning-color);
}

.efficiency-low {
    color: var(--td-error-color);
}

/* 操作按钮样式 */
.action-buttons {
    display: flex;
    gap: 8px;
}

.btn-sm {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-view {
    background: var(--td-brand-color-light);
    color: var(--td-brand-color);
}

.btn-view:hover {
    background: var(--td-brand-color);
    color: white;
}

.btn-edit {
    background: var(--td-warning-color-light);
    color: var(--td-warning-color);
}

.btn-edit:hover {
    background: var(--td-warning-color);
    color: white;
}

.btn-delete {
    background: var(--td-error-color-light);
    color: var(--td-error-color);
}

.btn-delete:hover {
    background: var(--td-error-color);
    color: white;
}

/* 分页样式 */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: var(--td-bg-color-container);
    border-radius: 8px;
    box-shadow: var(--td-shadow-1);
}

.pagination-info {
    color: var(--td-text-color-secondary);
    font-size: 14px;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.pagination-btn {
    min-width: 32px;
    height: 32px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border: 1px solid var(--td-border-color);
    background: var(--td-bg-color-container);
    color: var(--td-text-color-primary);
    transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
    border-color: var(--td-brand-color);
    color: var(--td-brand-color);
}

.pagination-btn.active {
    background: var(--td-brand-color);
    color: white;
    border-color: var(--td-brand-color);
}

.pagination-btn:disabled {
    cursor: not-allowed;
    color: var(--td-text-color-placeholder);
}

.pagination-size select {
    padding: 6px 8px;
    border: 1px solid var(--td-border-color);
    border-radius: 4px;
    background: var(--td-bg-color-container);
    color: var(--td-text-color-primary);
    font-size: 14px;
    cursor: pointer;
    outline: none;
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-container {
    background: var(--td-bg-color-container);
    border-radius: 8px;
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--td-shadow-3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--td-border-color);
}

.modal-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--td-text-color-primary);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 20px;
    color: var(--td-text-color-secondary);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.modal-close:hover {
    background: var(--td-bg-color-secondarycontainer);
}

.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 16px 20px;
    border-top: 1px solid var(--td-border-color);
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--td-text-color-primary);
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--td-border-color);
    border-radius: 6px;
    font-size: 14px;
    color: var(--td-text-color-primary);
    background: var(--td-bg-color-container);
    transition: border-color 0.2s;
}

.form-control:focus {
    border-color: var(--td-brand-color);
    outline: none;
}

.form-row {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;
}

.form-col {
    flex: 1;
}

/* 路径可视化样式 */
.route-visualization {
    margin-top: 16px;
    padding: 16px;
    background: var(--td-bg-color-secondarycontainer);
    border-radius: 8px;
}

.route-points {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 12px;
}

.route-point {
    padding: 6px 12px;
    background: var(--td-bg-color-container);
    border: 1px solid var(--td-border-color);
    border-radius: 16px;
    font-size: 13px;
    color: var(--td-text-color-primary);
}

.route-point.active {
    background: var(--td-brand-color);
    color: white;
    border-color: var(--td-brand-color);
}

/* 异常计数标签 */
.anomaly-count {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 24px;
    height: 24px;
    border-radius: 12px;
    background: var(--td-error-color-light);
    color: var(--td-error-color);
    font-size: 12px;
    font-weight: 600;
    padding: 0 8px;
}

.anomaly-count.zero {
    background: var(--td-success-color-light);
    color: var(--td-success-color);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .toolbar {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .toolbar-left {
        flex-direction: column;
    }

    .search-input {
        width: 100%;
    }

    .stats-cards {
        grid-template-columns: 1fr;
    }

    .table-container {
        overflow-x: auto;
    }

    .pagination-container {
        flex-direction: column;
        gap: 16px;
    }

    .action-buttons {
        flex-wrap: wrap;
    }
}
}