/* ========================================
   零售巡检系统后台首页样式
   ======================================== */

/* TDesign 颜色变量定义 */
:root {
  /* 品牌色 */
  --td-brand-color: #0052d9;
  --td-brand-color-hover: #266fe8;
  --td-brand-color-active: #0034b5;
  --td-brand-color-light: #e3f2fd;

  /* 功能色 */
  --td-success-color: #00a870;
  --td-success-color-light: #e8f5e8;
  --td-warning-color: #ed7b2f;
  --td-warning-color-light: #fff3e0;
  --td-error-color: #d54941;
  --td-error-color-light: #ffebee;

  /* 文字色 */
  --td-text-color-primary: #000000;
  --td-text-color-secondary: #666666;
  --td-text-color-placeholder: #bbbbbb;
  --td-text-color-anti: #ffffff;

  /* 背景色 */
  --td-bg-color-page: #f5f5f5;
  --td-bg-color-container: #ffffff;
  --td-bg-color-secondarycontainer: #f8f9fa;

  /* 边框色 */
  --td-border-color: #e7e7e7;
  --td-border-color-light: #f0f0f0;

  /* 阴影 */
  --td-shadow-1: 0 1px 10px rgba(0, 0, 0, 0.05);
  --td-shadow-2: 0 3px 14px 2px rgba(0, 0, 0, 0.05);

  /* 圆角 */
  --td-radius-default: 6px;
  --td-radius-medium: 8px;

  /* 间距 */
  --td-comp-size-m: 16px;
  --td-comp-size-l: 20px;
  --td-comp-size-xl: 24px;
  --td-comp-size-xxl: 32px;
}

/* 基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background: var(--td-bg-color-page);
  color: var(--td-text-color-primary);
  line-height: 1.6;
  font-size: 14px;
}

/* 主容器布局 */
.app-container {
  display: flex;
  min-height: 100vh;
  background: var(--td-bg-color-page);
}

/* 侧边栏样式已移至menu.css统一管理 */

/* 导航菜单样式已移至menu.css统一管理 */

/* 主内容区样式 - margin-left由menu.css统一管理 */

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--td-comp-size-xl);
  padding: var(--td-comp-size-l);
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  box-shadow: var(--td-shadow-1);
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--td-text-color-primary);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--td-comp-size-m);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--td-brand-color);
  color: var(--td-text-color-anti);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.user-name {
  font-weight: 500;
  color: var(--td-text-color-primary);
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: var(--td-comp-size-l);
  margin-bottom: var(--td-comp-size-xxl);
}

.stat-card {
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  padding: var(--td-comp-size-xl);
  box-shadow: var(--td-shadow-1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--td-shadow-2);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, var(--td-brand-color) 0%, var(--td-brand-color-hover) 100%);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--td-comp-size-m);
}

.stat-title {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  font-weight: 500;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--td-radius-medium);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: var(--td-text-color-anti);
}

.stat-icon.success {
  background: var(--td-success-color);
}

.stat-icon.warning {
  background: var(--td-warning-color);
}

.stat-icon.error {
  background: var(--td-error-color);
}

.stat-icon.primary {
  background: var(--td-brand-color);
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.stat-desc {
  font-size: 12px;
  color: var(--td-text-color-secondary);
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
}

.stat-trend.up {
  color: var(--td-success-color);
}

.stat-trend.down {
  color: var(--td-error-color);
}

/* 图表网格 */
.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--td-comp-size-l);
  margin-bottom: var(--td-comp-size-xxl);
}

.chart-card {
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  box-shadow: var(--td-shadow-1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.chart-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--td-shadow-2);
}

.chart-header {
  padding: var(--td-comp-size-l);
  border-bottom: 1px solid var(--td-border-color-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--td-text-color-primary);
}

.chart-actions {
  display: flex;
  gap: 8px;
}

.btn-small {
  padding: 4px 8px;
  font-size: 12px;
  border: 1px solid var(--td-border-color);
  background: var(--td-bg-color-container);
  color: var(--td-text-color-secondary);
  border-radius: var(--td-radius-default);
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-small:hover {
  background: var(--td-brand-color);
  color: var(--td-text-color-anti);
  border-color: var(--td-brand-color);
}

.chart-container {
  padding: var(--td-comp-size-l);
  height: 300px;
  position: relative;
}

.chart-container canvas {
  max-width: 100%;
  max-height: 100%;
}

/* 快速功能网格 */
.function-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--td-comp-size-l);
}

.function-card {
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  padding: var(--td-comp-size-xl);
  box-shadow: var(--td-shadow-1);
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  text-decoration: none;
  color: inherit;
}

.function-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--td-shadow-2);
  background: var(--td-brand-color-light);
}

.function-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--td-brand-color);
  color: var(--td-text-color-anti);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin: 0 auto var(--td-comp-size-m);
  transition: all 0.3s ease;
}

.function-card:hover .function-icon {
  background: var(--td-brand-color-active);
  transform: scale(1.1);
}

.function-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.function-desc {
  font-size: 12px;
  color: var(--td-text-color-secondary);
  line-height: 1.5;
}

/* 响应式设计 - 侧边栏样式已移至menu.css统一管理 */
@media (max-width: 768px) {

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }

  .function-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .page-header {
    flex-direction: column;
    gap: var(--td-comp-size-m);
    text-align: center;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: var(--td-comp-size-l);
  }

  .stat-card {
    padding: var(--td-comp-size-l);
  }

  .chart-container {
    height: 250px;
  }

  .function-card {
    padding: var(--td-comp-size-l);
  }

  .function-icon {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: var(--td-text-color-secondary);
}

.loading::after {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid var(--td-border-color);
  border-top: 2px solid var(--td-brand-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}