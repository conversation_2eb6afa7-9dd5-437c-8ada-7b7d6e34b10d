/* 多终端协同操作页面样式 */

/* 工具栏样式 */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.toolbar-left {
    display: flex;
    gap: 10px;
}

.toolbar-right {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-box {
    position: relative;
}

.search-box input {
    padding-right: 35px;
    width: 250px;
}

.search-box i {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

/* 统计卡片样式 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.stat-card {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #fff;
}

.stat-icon.active {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.stat-icon.idle {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.stat-icon.offline {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
}

.stat-icon.syncing {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 28px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 14px;
    color: #666;
    margin: 0;
}

/* 表格容器样式 */
.table-container {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.table {
    margin-bottom: 0;
}

.table th {
    background: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
    cursor: pointer;
    user-select: none;
    position: relative;
}

.table th:hover {
    background: #e9ecef;
}

.table th i {
    margin-left: 5px;
    opacity: 0.5;
}

.table th.sort-asc i:before {
    content: "\f0de";
    opacity: 1;
}

.table th.sort-desc i:before {
    content: "\f0dd";
    opacity: 1;
}

.table td {
    vertical-align: middle;
    padding: 12px 15px;
}

/* 状态标签样式 */
.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
}

.status-badge.active {
    background: #d4edda;
    color: #155724;
}

.status-badge.idle {
    background: #fff3cd;
    color: #856404;
}

.status-badge.offline {
    background: #f8d7da;
    color: #721c24;
}

.status-badge.error {
    background: #f5c6cb;
    color: #721c24;
}

/* 终端类型标签样式 */
.terminal-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.terminal-badge.pda {
    background: #e3f2fd;
    color: #1565c0;
}

.terminal-badge.ar {
    background: #f3e5f5;
    color: #7b1fa2;
}

.terminal-badge.tablet {
    background: #e8f5e8;
    color: #2e7d32;
}

.terminal-badge.mobile {
    background: #fff3e0;
    color: #ef6c00;
}

/* 电量显示样式 */
.battery-indicator {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
}

.battery-icon {
    width: 20px;
    height: 12px;
    border: 1px solid #666;
    border-radius: 2px;
    position: relative;
    background: #fff;
}

.battery-icon::after {
    content: '';
    position: absolute;
    right: -3px;
    top: 3px;
    width: 2px;
    height: 6px;
    background: #666;
    border-radius: 0 1px 1px 0;
}

.battery-fill {
    height: 100%;
    border-radius: 1px;
    transition: width 0.3s ease;
}

.battery-fill.high {
    background: #28a745;
}

.battery-fill.medium {
    background: #ffc107;
}

.battery-fill.low {
    background: #dc3545;
}

.battery-text {
    color: #666;
    font-weight: 500;
}

/* 数据量显示样式 */
.data-size {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
}

.data-size-value {
    font-weight: 500;
    color: #495057;
}

/* 操作按钮样式 */
.action-buttons {
    display: flex;
    gap: 5px;
}

.btn-action {
    padding: 4px 8px;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-action:hover {
    transform: translateY(-1px);
}

.btn-action.view {
    background: #17a2b8;
    color: #fff;
}

.btn-action.sync {
    background: #28a745;
    color: #fff;
}

.btn-action.disconnect {
    background: #dc3545;
    color: #fff;
}

.btn-action.edit {
    background: #ffc107;
    color: #212529;
}

/* 模态框样式 */
.modal-content {
    border-radius: 8px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.modal-header {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
}

.modal-title {
    font-weight: 600;
    color: #495057;
}

.close {
    background: none;
    border: none;
    font-size: 24px;
    font-weight: 300;
    color: #6c757d;
    opacity: 0.8;
}

.close:hover {
    opacity: 1;
}

.modal-body {
    padding: 25px;
}

.form-group label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 5px;
}

.form-control {
    border-radius: 4px;
    border: 1px solid #ced4da;
    padding: 8px 12px;
    font-size: 14px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.form-check {
    margin-bottom: 10px;
}

.form-check-input {
    margin-top: 0.3rem;
}

.form-check-label {
    font-size: 14px;
    color: #495057;
    margin-left: 5px;
}

/* 详情模态框样式 */
.detail-section {
    margin-bottom: 25px;
}

.detail-section h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #e9ecef;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.detail-label {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
    text-transform: uppercase;
}

.detail-value {
    font-size: 14px;
    color: #495057;
    font-weight: 400;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .toolbar {
        flex-direction: column;
        gap: 15px;
    }

    .toolbar-left,
    .toolbar-right {
        width: 100%;
        justify-content: center;
    }

    .search-box input {
        width: 100%;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .table-container {
        overflow-x: auto;
    }

    .detail-grid {
        grid-template-columns: 1fr;
    }
}

/* 加载动画 */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h5 {
    margin-bottom: 10px;
    color: #495057;
}

.empty-state p {
    margin-bottom: 0;
    font-size: 14px;
}
