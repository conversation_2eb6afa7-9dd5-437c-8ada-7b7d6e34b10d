@charset "UTF-8";

/* ========================================
   CSS变量定义
   ======================================== */
:root {
    /* 主色调 */
    --primary-color: #1890ff;
    --primary-hover: #40a9ff;
    --primary-active: #096dd9;

    /* 功能色彩 */
    --success-color: #52c41a;
    --warning-color: #faad14;
    --error-color: #ff4d4f;
    --info-color: #1890ff;

    /* TDesign 兼容变量 */
    --td-brand-color: #1890ff;
    --td-brand-color-hover: #40a9ff;
    --td-brand-color-light: rgba(24, 144, 255, 0.1);
    --td-success-color: #52c41a;
    --td-success-color-light: rgba(82, 196, 26, 0.1);
    --td-warning-color: #faad14;
    --td-warning-color-light: rgba(250, 173, 20, 0.1);
    --td-error-color: #ff4d4f;
    --td-error-color-light: rgba(255, 77, 79, 0.1);
    --td-text-color-primary: #262626;
    --td-text-color-secondary: #8c8c8c;
    --td-text-color-placeholder: #bfbfbf;
    --td-border-color: #d9d9d9;
    --td-border-color-light: #f0f0f0;
    --td-bg-color-container: #ffffff;
    --td-bg-color-secondarycontainer: #fafafa;
    --td-shadow-1: 0 2px 8px rgba(0, 0, 0, 0.06);
    --td-shadow-2: 0 4px 12px rgba(0, 0, 0, 0.1);
    --td-shadow-3: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* ========================================
   基础重置样式
   ======================================== */
* {
    margin: 0;
    padding: 0;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -o-box-sizing: border-box;
    box-sizing: border-box;
}

.container-fluid {
    margin: 0;
    padding: 0;
}

.dowebok {
    overflow-y: scroll;
    overflow-x: hidden;
    height: 100vh;
}

body {
    font-family: 'Open Sans', sans-serif;
    font-size: 16px;
    color: #353535;
    overflow: hidden;
    height: 100vh;
}

.heading {
    font-family: 'Lato', sans-serif;
    color: #2196f3;
    letter-spacing: -2px;
}

.paragraph {
    color: rgba(53, 53, 53, 0.7);
}

form {
    width: 270px;
    margin: 80px auto;
}
form .form-control {
    border-radius: 0;
    -webkit-transition: all 0.3s linear;
    -moz-transition: all 0.3s linear;
    -o-transition: all 0.3s linear;
    transition: all 0.3s linear;
}
form .form-control:focus,
form .form-control:active,
form .form-control:hover {
    color: rgba(53, 53, 53, 0.4);
    outline: none;
    border: 1px solid #2196f3;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    -o-box-shadow: none;
    box-shadow: none;
}
form .input-group-prepend {
    border: 1px solid #ced4da;
    width: 50px;
}

.link {
    color: rgba(53, 53, 53, 0.7);
    -webkit-transition: all 0.3s linear;
    -moz-transition: all 0.3s linear;
    -o-transition: all 0.3s linear;
    transition: all 0.3s linear;
}
.link:hover {
    color: #2196f3;
    text-decoration: none;
}

.btn-primary {
    border-radius: 0;
    position: relative;
    -webkit-transition: all 0.3s linear;
    -moz-transition: all 0.3s linear;
    -o-transition: all 0.3s linear;
    transition: all 0.3s linear;
    -webkit-box-shadow: 0 24px 10px -20px rgba(0, 0, 0, 0.5);
    -moz-box-shadow: 0 24px 10px -20px rgba(0, 0, 0, 0.5);
    -o-box-shadow: 0 24px 10px -20px rgba(0, 0, 0, 0.5);
    box-shadow: 0 24px 10px -20px rgba(0, 0, 0, 0.5);
}
.btn-primary:focus,
.btn-primary:active {
    color: rgba(53, 53, 53, 0.7);
    outline: none;
    border: 1px solid #2196f3;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    -o-box-shadow: none;
    box-shadow: none;
}

.divider {
    color: rgba(53, 53, 53, 0.7);
    position: relative;
    width: 270px;
    display: block;
    margin: 0 auto;
}
.divider::before,
.divider::after {
    content: '';
    display: block;
    width: 40%;
    height: 1px;
    background: rgba(53, 53, 53, 0.3);
    position: absolute;
    bottom: 10px;
}
.divider::before {
    left: 0;
}
.divider::after {
    right: 0;
}

.btn-facebook {
    background-color: #0062cc;
}

.form-check-input {
    opacity: 0;
}
.form-check-input:checked ~ .form-check-label::before {
    color: rgba(53, 53, 53, 0.7);
}

.form-check-label {
    color: rgba(53, 53, 53, 0.7);
    margin: 0 0 0 10px;
}
.form-check-label::before {
    content: '✓';
    display: block;
    width: 20px;
    height: 20px;
    line-height: 20px;
    color: rgba(53, 53, 53, 0);
    border: 1px solid #ced4da;
    position: absolute;
    top: 0;
    left: 0;
    margin: 0 40px 0 0;
}

.bg {
    background: url('../images/bg.jpg') no-repeat;
    width: 100%;
    height: 100%;
    background-size: cover;
}

/* ========================================
   后台管理系统布局样式
   ======================================== */

/* 应用容器 */
.app-container {
    display: flex;
    height: 100vh;
    background: var(--td-bg-color-secondarycontainer);
}

/* 侧边栏 */
.sidebar {
    width: 280px;
    background: var(--td-bg-color-container);
    border-right: 1px solid var(--td-border-color);
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    z-index: 1000;
    transition: transform 0.3s ease;
}

.sidebar-header {
    padding: 24px 20px;
    border-bottom: 1px solid var(--td-border-color);
}

.sidebar-header h1 {
    font-size: 18px;
    font-weight: 600;
    color: var(--td-text-color-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo-icon {
    font-size: 24px;
}

.sidebar-nav {
    flex: 1;
    overflow-y: auto;
    padding: 16px 0;
}

/* 菜单样式 */
.menu-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.menu-category {
    margin-bottom: 8px;
}

.category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    color: var(--td-text-color-primary);
    font-weight: 600;
    font-size: 14px;
    transition: background-color 0.2s;
    border-radius: 6px;
    margin: 0 8px;
}

.category-header:hover {
    background: var(--td-bg-color-secondarycontainer);
}

.category-header.expanded {
    background: var(--td-brand-color-light);
    color: var(--td-brand-color);
}

.category-title {
    flex: 1;
}

.expand-icon {
    font-size: 16px;
    font-weight: bold;
    transition: transform 0.2s;
}

.category-header.expanded .expand-icon {
    transform: rotate(180deg);
}

.sub-menu {
    list-style: none;
    margin: 0;
    padding: 0;
    background: var(--td-bg-color-secondarycontainer);
    border-radius: 6px;
    margin: 4px 8px 8px 8px;
    overflow: hidden;
}

.menu-item {
    margin: 0;
}

.menu-link {
    display: block;
    padding: 10px 20px;
    color: var(--td-text-color-secondary);
    text-decoration: none;
    font-size: 13px;
    transition: all 0.2s;
    border-left: 3px solid transparent;
}

.menu-link:hover {
    background: var(--td-brand-color-light);
    color: var(--td-brand-color);
    border-left-color: var(--td-brand-color);
    text-decoration: none;
}

.menu-link.active {
    background: var(--td-brand-color-light);
    color: var(--td-brand-color);
    border-left-color: var(--td-brand-color);
    font-weight: 500;
}

/* 主内容区 */
.main-content {
    flex: 1;
    margin-left: 280px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* 顶部导航栏 */
.top-bar {
    height: 60px;
    background: var(--td-bg-color-container);
    border-bottom: 1px solid var(--td-border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    position: sticky;
    top: 0;
    z-index: 999;
}

.top-bar-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.sidebar-toggle {
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.sidebar-toggle:hover {
    background: var(--td-bg-color-secondarycontainer);
}

.hamburger {
    display: block;
    width: 20px;
    height: 2px;
    background: var(--td-text-color-primary);
    position: relative;
}

.hamburger::before,
.hamburger::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 2px;
    background: var(--td-text-color-primary);
    transition: all 0.3s;
}

.hamburger::before {
    top: -6px;
}

.hamburger::after {
    bottom: -6px;
}

.page-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--td-text-color-primary);
    margin: 0;
}

.top-bar-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.user-name {
    font-size: 14px;
    color: var(--td-text-color-primary);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--td-brand-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 500;
}

/* 内容包装器 */
.content-wrapper {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.btn-primary {
    background: var(--td-brand-color);
    color: white;
}

.btn-primary:hover {
    background: var(--td-brand-color-hover);
}

.btn-secondary {
    background: var(--td-bg-color-secondarycontainer);
    color: var(--td-text-color-primary);
    border: 1px solid var(--td-border-color);
}

.btn-secondary:hover {
    background: var(--td-border-color-light);
}

.btn-success {
    background: var(--td-success-color);
    color: white;
}

.btn-success:hover {
    background: var(--td-success-color);
    opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    body.sidebar-collapsed .sidebar {
        transform: translateX(0);
    }
}
