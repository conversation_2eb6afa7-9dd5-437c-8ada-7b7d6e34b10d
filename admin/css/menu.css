/* ========================================
   菜单样式 - 提取自style.css
   ======================================== */

/* 侧边栏 */
.sidebar {
    width: 280px;
    background: var(--td-bg-color-container);
    border-right: 1px solid var(--td-border-color);
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    z-index: 1000;
    transition: transform 0.3s ease;
}

.sidebar-header {
    padding: 24px 20px;
    border-bottom: 1px solid var(--td-border-color);
}

.sidebar-header h1 {
    font-size: 18px;
    font-weight: 600;
    color: var(--td-text-color-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo-icon {
    font-size: 24px;
}

.sidebar-nav {
    flex: 1;
    overflow-y: auto;
    padding: 16px 0;
}

/* 菜单列表 */
.menu-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.menu-category {
    margin-bottom: 8px;
}

.category-header {
    padding: 12px 20px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--td-text-color-primary);
    font-weight: 500;
    transition: background-color 0.2s, color 0.2s;
    border-left: 3px solid transparent;
}

.category-header:hover {
    background-color: var(--td-brand-color-light);
    color: var(--td-brand-color);
}

.category-header.expanded {
    background-color: var(--td-brand-color-light);
    color: var(--td-brand-color);
    border-left-color: var(--td-brand-color);
}

.category-title {
    font-size: 14px;
}

.expand-icon {
    font-size: 16px;
    font-weight: bold;
    transition: transform 0.2s;
}

.category-header.expanded .expand-icon {
    transform: rotate(180deg);
}

.sub-menu {
    list-style: none;
    padding: 0;
    margin: 0;
    background-color: rgba(0, 0, 0, 0.02);
    overflow: hidden;
    transition: height 0.3s;
}

.menu-item {
    margin: 0;
}

.menu-link {
    display: block;
    padding: 10px 20px 10px 40px;
    color: var(--td-text-color-secondary);
    text-decoration: none;
    font-size: 14px;
    transition: background-color 0.2s, color 0.2s;
    border-left: 3px solid transparent;
}

.menu-link:hover {
    background-color: var(--td-brand-color-light);
    color: var(--td-brand-color);
    text-decoration: none;
}

.menu-link.active {
    background-color: var(--td-brand-color-light);
    color: var(--td-brand-color);
    border-left-color: var(--td-brand-color);
    font-weight: 500;
}

/* 顶部导航栏 */
.top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64px;
    padding: 0 24px;
    background: var(--td-bg-color-container);
    border-bottom: 1px solid var(--td-border-color);
    position: sticky;
    top: 0;
    z-index: 900;
}

.top-bar-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.sidebar-toggle {
    background: none;
    border: none;
    cursor: pointer;
    width: 24px;
    height: 24px;
    position: relative;
    padding: 0;
}

.sidebar-toggle:hover {
    opacity: 0.8;
}

.hamburger {
    width: 18px;
    height: 2px;
    background: var(--td-text-color-primary);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.hamburger::before,
.hamburger::after {
    content: '';
    width: 18px;
    height: 2px;
    background: var(--td-text-color-primary);
    position: absolute;
    left: 0;
    transition: transform 0.3s;
}

.hamburger::before {
    top: -6px;
}

.hamburger::after {
    top: 6px;
}

.page-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--td-text-color-primary);
    margin: 0;
}

.top-bar-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.user-name {
    font-size: 14px;
    color: var(--td-text-color-primary);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--td-brand-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    body.sidebar-collapsed .sidebar {
        transform: translateX(-100%);
    }
}