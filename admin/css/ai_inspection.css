/* ========================================
   AI视觉识别巡检页面样式
   ======================================== */

/* 工具栏样式 */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: var(--td-bg-color-container);
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: var(--td-shadow-1);
}

.toolbar-left {
    display: flex;
    gap: 15px;
    align-items: center;
}

.toolbar-right {
    display: flex;
    gap: 10px;
}

/* 搜索框样式 */
.search-box {
    display: flex;
    align-items: center;
    background: var(--td-bg-color-secondarycontainer);
    border-radius: 6px;
    border: 1px solid var(--td-border-color);
    overflow: hidden;
}

.search-input {
    border: none;
    outline: none;
    padding: 8px 12px;
    background: transparent;
    font-size: 14px;
    width: 280px;
    color: var(--td-text-color-primary);
}

.search-input::placeholder {
    color: var(--td-text-color-placeholder);
}

.search-btn {
    background: var(--td-brand-color);
    color: white;
    border: none;
    padding: 8px 12px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.search-btn:hover {
    background: var(--td-brand-color-hover);
}

/* 筛选框样式 */
.filter-box {
    display: flex;
    gap: 10px;
}

.filter-box select {
    padding: 8px 12px;
    border: 1px solid var(--td-border-color);
    border-radius: 6px;
    background: var(--td-bg-color-container);
    color: var(--td-text-color-primary);
    font-size: 14px;
    cursor: pointer;
    outline: none;
}

.filter-box select:focus {
    border-color: var(--td-brand-color);
}

/* 统计卡片样式 */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.stat-card {
    background: var(--td-bg-color-container);
    border-radius: 8px;
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    box-shadow: var(--td-shadow-1);
    transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--td-shadow-2);
}

.stat-icon {
    font-size: 32px;
    padding: 12px;
    border-radius: 50%;
    background: var(--td-brand-color-light);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 56px;
    height: 56px;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 28px;
    font-weight: 700;
    color: var(--td-text-color-primary);
    line-height: 1;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: var(--td-text-color-secondary);
    font-weight: 500;
}

/* 表格样式 */
.table-container {
    background: var(--td-bg-color-container);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--td-shadow-1);
    margin-bottom: 20px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.data-table th {
    background: var(--td-bg-color-secondarycontainer);
    color: var(--td-text-color-primary);
    font-weight: 600;
    padding: 16px 12px;
    text-align: left;
    border-bottom: 1px solid var(--td-border-color);
    cursor: pointer;
    user-select: none;
    position: relative;
}

.data-table th:hover {
    background: var(--td-brand-color-light);
}

.data-table td {
    padding: 16px 12px;
    border-bottom: 1px solid var(--td-border-color-light);
    color: var(--td-text-color-primary);
}

.data-table tbody tr:hover {
    background: var(--td-bg-color-secondarycontainer);
}

/* 排序指示器 */
.sort-indicator {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.5;
    transition: opacity 0.2s;
}

.data-table th:hover .sort-indicator {
    opacity: 1;
}

.data-table th.sort-asc .sort-indicator::after {
    content: '↑';
    color: var(--td-brand-color);
}

.data-table th.sort-desc .sort-indicator::after {
    content: '↓';
    color: var(--td-brand-color);
}

/* 状态标签样式 */
.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    min-width: 60px;
}

.status-badge.completed {
    background: var(--td-success-color-light);
    color: var(--td-success-color);
}

.status-badge.processing {
    background: var(--td-brand-color-light);
    color: var(--td-brand-color);
}

.status-badge.error {
    background: var(--td-error-color-light);
    color: var(--td-error-color);
}

.status-badge.warning {
    background: var(--td-warning-color-light);
    color: var(--td-warning-color);
}

/* 置信度进度条 */
.confidence-bar {
    width: 80px;
    height: 6px;
    background: var(--td-border-color-light);
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}

.confidence-fill {
    height: 100%;
    border-radius: 3px;
    transition: width 0.3s ease;
}

.confidence-fill.high {
    background: var(--td-success-color);
}

.confidence-fill.medium {
    background: var(--td-warning-color);
}

.confidence-fill.low {
    background: var(--td-error-color);
}

/* 操作按钮样式 */
.action-buttons {
    display: flex;
    gap: 8px;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s;
}

.btn-view {
    background: var(--td-brand-color);
    color: white;
}

.btn-view:hover {
    background: var(--td-brand-color-hover);
}

.btn-edit {
    background: var(--td-warning-color);
    color: white;
}

.btn-edit:hover {
    background: var(--td-warning-color);
    opacity: 0.8;
}

.btn-delete {
    background: var(--td-error-color);
    color: white;
}

.btn-delete:hover {
    background: var(--td-error-color);
    opacity: 0.8;
}

/* 分页样式 */
.pagination-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: var(--td-bg-color-container);
    border-radius: 8px;
    box-shadow: var(--td-shadow-1);
}

.pagination-info {
    color: var(--td-text-color-secondary);
    font-size: 14px;
}

.pagination-controls {
    display: flex;
    gap: 8px;
    align-items: center;
}

.pagination-btn {
    padding: 8px 12px;
    border: 1px solid var(--td-border-color);
    background: var(--td-bg-color-container);
    color: var(--td-text-color-primary);
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
    background: var(--td-brand-color-light);
    border-color: var(--td-brand-color);
}

.pagination-btn.active {
    background: var(--td-brand-color);
    color: white;
    border-color: var(--td-brand-color);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-size select {
    padding: 6px 8px;
    border: 1px solid var(--td-border-color);
    border-radius: 4px;
    background: var(--td-bg-color-container);
    color: var(--td-text-color-primary);
    font-size: 14px;
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.modal-container {
    background: var(--td-bg-color-container);
    border-radius: 8px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: var(--td-shadow-3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid var(--td-border-color);
}

.modal-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--td-text-color-primary);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--td-text-color-secondary);
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s;
}

.modal-close:hover {
    background: var(--td-error-color-light);
    color: var(--td-error-color);
}

.modal-body {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 16px 24px;
    border-top: 1px solid var(--td-border-color);
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: var(--td-text-color-primary);
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--td-border-color);
    border-radius: 6px;
    background: var(--td-bg-color-container);
    color: var(--td-text-color-primary);
    font-size: 14px;
    transition: border-color 0.2s;
}

.form-control:focus {
    outline: none;
    border-color: var(--td-brand-color);
}

.form-row {
    display: flex;
    gap: 16px;
}

.form-col {
    flex: 1;
}

/* Toast 提示样式 */
.toast {
    border-radius: 6px;
    box-shadow: var(--td-shadow-2);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .toolbar {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .toolbar-left {
        flex-direction: column;
        gap: 12px;
    }

    .search-input {
        width: 100%;
    }

    .stats-cards {
        grid-template-columns: 1fr;
    }

    .table-container {
        overflow-x: auto;
    }

    .pagination-wrapper {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }

    .action-buttons {
        flex-direction: column;
    }
}