<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动化巡检流程 - 基于零售站点前置仓的巡检系统</title>

    <!-- 引入CSS文件 -->
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/perfect-scrollbar.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/menu.css">
    <link rel="stylesheet" href="../css/auto_inspection.css">
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h1>
                    <span class="logo-icon">🏪</span>
                    <!-- 标题将由menu.js动态设置 -->
                </h1>
            </div>
            <nav class="sidebar-nav" id="sidebarNav">
                <!-- 菜单将由menu.js动态生成 -->
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部导航栏 -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <span class="hamburger"></span>
                    </button>
                    <h2 class="page-title">自动化巡检流程</h2>
                </div>
                <div class="top-bar-right">
                    <div class="user-info">
                        <span class="user-name">管理员</span>
                        <div class="user-avatar">A</div>
                    </div>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="content-wrapper">
                <!-- 工具栏 -->
                <div class="toolbar">
                    <div class="toolbar-left">
                        <div class="search-box">
                            <input type="text" id="searchInput" placeholder="搜索巡检ID、名称或位置..." class="search-input">
                            <button class="search-btn" onclick="handleSearch()">🔍</button>
                        </div>
                        <div class="filter-box">
                            <select id="statusFilter" onchange="handleFilter()">
                                <option value="">全部状态</option>
                                <option value="已完成">已完成</option>
                                <option value="进行中">进行中</option>
                                <option value="计划中">计划中</option>
                            </select>
                            <select id="typeFilter" onchange="handleFilter()">
                                <option value="">全部类型</option>
                                <option value="日常巡检">日常巡检</option>
                                <option value="专项巡检">专项巡检</option>
                                <option value="定期巡检">定期巡检</option>
                            </select>
                        </div>
                    </div>
                    <div class="toolbar-right">
                        <button class="btn btn-secondary" onclick="refreshData()">刷新</button>
                        <button class="btn btn-primary" onclick="exportData()">导出CSV</button>
                        <button class="btn btn-success" onclick="showNewInspectionModal()">新建巡检</button>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="stats-cards">
                    <div class="stat-card">
                        <div class="stat-icon">📊</div>
                        <div class="stat-content">
                            <div class="stat-number" id="totalInspections">0</div>
                            <div class="stat-label">总巡检数量</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">✅</div>
                        <div class="stat-content">
                            <div class="stat-number" id="completedInspections">0</div>
                            <div class="stat-label">已完成巡检</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">⚠️</div>
                        <div class="stat-content">
                            <div class="stat-number" id="anomalyCount">0</div>
                            <div class="stat-label">发现异常数</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">🎯</div>
                        <div class="stat-content">
                            <div class="stat-number" id="avgEfficiency">0%</div>
                            <div class="stat-label">平均效率</div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="table-container">
                    <table class="data-table" id="inspectionTable">
                        <thead>
                            <tr>
                                <th onclick="sortTable('id')">巡检ID <span class="sort-indicator"></span></th>
                                <th onclick="sortTable('name')">巡检名称 <span class="sort-indicator"></span></th>
                                <th onclick="sortTable('type')">巡检类型 <span class="sort-indicator"></span></th>
                                <th onclick="sortTable('status')">状态 <span class="sort-indicator"></span></th>
                                <th onclick="sortTable('location')">位置 <span class="sort-indicator"></span></th>
                                <th onclick="sortTable('start_time')">开始时间 <span class="sort-indicator"></span></th>
                                <th onclick="sortTable('duration')">耗时(分钟) <span class="sort-indicator"></span></th>
                                <th onclick="sortTable('anomaly_count')">异常数 <span class="sort-indicator"></span></th>
                                <th onclick="sortTable('route_efficiency')">路径效率 <span class="sort-indicator"></span></th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="tableBody">
                            <!-- 数据将由JavaScript动态填充 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页组件 -->
                <div class="pagination-container" id="paginationContainer">
                    <!-- 分页控件将由JavaScript动态生成 -->
                </div>
            </div>
        </main>
    </div>

    <!-- 新建巡检模态框 -->
    <div class="modal-overlay" id="newInspectionModal">
        <div class="modal-container">
            <div class="modal-header">
                <h3 class="modal-title">新建自动化巡检</h3>
                <button class="modal-close" onclick="closeModal('newInspectionModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="newInspectionForm">
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="inspectionName">巡检名称</label>
                                <input type="text" class="form-control" id="inspectionName" required>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="inspectionType">巡检类型</label>
                                <select class="form-control" id="inspectionType" required>
                                    <option value="日常巡检">日常巡检</option>
                                    <option value="专项巡检">专项巡检</option>
                                    <option value="定期巡检">定期巡检</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="inspectionLocation">巡检位置</label>
                                <input type="text" class="form-control" id="inspectionLocation" required>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="inspectionExecutor">执行者</label>
                                <input type="text" class="form-control" id="inspectionExecutor" value="AI机器人">
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="inspectionStartTime">计划开始时间</label>
                                <input type="datetime-local" class="form-control" id="inspectionStartTime" required>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="inspectionStatus">状态</label>
                                <select class="form-control" id="inspectionStatus" required>
                                    <option value="计划中">计划中</option>
                                    <option value="进行中">进行中</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="inspectionDescription">巡检描述</label>
                        <textarea class="form-control" id="inspectionDescription" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="routePoints">巡检路径点（用逗号分隔）</label>
                        <input type="text" class="form-control" id="routePoints" placeholder="例如：A1,A2,A3,A4">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('newInspectionModal')">取消</button>
                <button class="btn btn-primary" onclick="createInspection()">创建</button>
            </div>
        </div>
    </div>

    <!-- 查看巡检详情模态框 -->
    <div class="modal-overlay" id="viewInspectionModal">
        <div class="modal-container">
            <div class="modal-header">
                <h3 class="modal-title">巡检详情</h3>
                <button class="modal-close" onclick="closeModal('viewInspectionModal')">&times;</button>
            </div>
            <div class="modal-body" id="inspectionDetailContent">
                <!-- 详情内容将由JavaScript动态填充 -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="closeModal('viewInspectionModal')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 编辑巡检模态框 -->
    <div class="modal-overlay" id="editInspectionModal">
        <div class="modal-container">
            <div class="modal-header">
                <h3 class="modal-title">编辑巡检</h3>
                <button class="modal-close" onclick="closeModal('editInspectionModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="editInspectionForm">
                    <input type="hidden" id="editInspectionId">
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="editInspectionName">巡检名称</label>
                                <input type="text" class="form-control" id="editInspectionName" required>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="editInspectionType">巡检类型</label>
                                <select class="form-control" id="editInspectionType" required>
                                    <option value="日常巡检">日常巡检</option>
                                    <option value="专项巡检">专项巡检</option>
                                    <option value="定期巡检">定期巡检</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="editInspectionLocation">巡检位置</label>
                                <input type="text" class="form-control" id="editInspectionLocation" required>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="editInspectionExecutor">执行者</label>
                                <input type="text" class="form-control" id="editInspectionExecutor">
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="editInspectionStatus">状态</label>
                                <select class="form-control" id="editInspectionStatus" required>
                                    <option value="计划中">计划中</option>
                                    <option value="进行中">进行中</option>
                                    <option value="已完成">已完成</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="editAnomalyCount">异常数量</label>
                                <input type="number" class="form-control" id="editAnomalyCount" min="0">
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="editInspectionDescription">巡检描述</label>
                        <textarea class="form-control" id="editInspectionDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('editInspectionModal')">取消</button>
                <button class="btn btn-primary" onclick="saveInspectionEdit()">保存</button>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript文件 -->
    <script src="../js/jquery.min.js"></script>
    <script src="../js/perfect-scrollbar.min.js"></script>
    <script src="../js/popper.min.js"></script>
    <script src="../js/common.js"></script>
    <script src="../js/menu.js"></script>
    <script src="../js/auto_inspection.js"></script>
</body>
</html>