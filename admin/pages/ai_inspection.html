<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI视觉识别巡检 - 基于零售站点前置仓的巡检系统</title>

    <!-- 引入CSS文件 -->
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/perfect-scrollbar.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/menu.css">
    <link rel="stylesheet" href="../css/ai_inspection.css">
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h1>
                    <span class="logo-icon">🏪</span>
                    基于零售站点前置仓的巡检系统
                </h1>
            </div>
            <nav class="sidebar-nav" id="sidebarNav">
                <!-- 菜单将由menu.js动态生成 -->
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部导航栏 -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <span class="hamburger"></span>
                    </button>
                    <h2 class="page-title">AI视觉识别巡检</h2>
                </div>
                <div class="top-bar-right">
                    <div class="user-info">
                        <span class="user-name">管理员</span>
                        <div class="user-avatar">A</div>
                    </div>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="content-wrapper">
                <!-- 工具栏 -->
                <div class="toolbar">
                    <div class="toolbar-left">
                        <div class="search-box">
                            <input type="text" id="searchInput" placeholder="搜索任务编号、位置或结果..." class="search-input">
                            <button class="search-btn" onclick="handleSearch()">🔍</button>
                        </div>
                        <div class="filter-box">
                            <select id="statusFilter" onchange="handleFilter()">
                                <option value="">全部状态</option>
                                <option value="已完成">已完成</option>
                                <option value="进行中">进行中</option>
                                <option value="异常">异常</option>
                            </select>
                            <select id="resultFilter" onchange="handleFilter()">
                                <option value="">全部结果</option>
                                <option value="正常">正常</option>
                                <option value="异常">异常</option>
                                <option value="需要处理">需要处理</option>
                            </select>
                        </div>
                    </div>
                    <div class="toolbar-right">
                        <button class="btn btn-secondary" onclick="refreshData()">刷新</button>
                        <button class="btn btn-primary" onclick="exportData()">导出CSV</button>
                        <button class="btn btn-success" onclick="showNewTaskModal()">新建巡检</button>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="stats-cards">
                    <div class="stat-card">
                        <div class="stat-icon">📊</div>
                        <div class="stat-content">
                            <div class="stat-number" id="totalTasks">0</div>
                            <div class="stat-label">总巡检次数</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">✅</div>
                        <div class="stat-content">
                            <div class="stat-number" id="completedTasks">0</div>
                            <div class="stat-label">已完成</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">⚠️</div>
                        <div class="stat-content">
                            <div class="stat-number" id="anomalyTasks">0</div>
                            <div class="stat-label">发现异常</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">🎯</div>
                        <div class="stat-content">
                            <div class="stat-number" id="avgConfidence">0%</div>
                            <div class="stat-label">平均准确率</div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="table-container">
                    <table class="data-table" id="inspectionTable">
                        <thead>
                            <tr>
                                <th onclick="sortTable('task_id')">任务编号 <span class="sort-indicator"></span></th>
                                <th onclick="sortTable('location')">检测位置 <span class="sort-indicator"></span></th>
                                <th onclick="sortTable('inspection_type')">检测类型 <span class="sort-indicator"></span></th>
                                <th onclick="sortTable('status')">状态 <span class="sort-indicator"></span></th>
                                <th onclick="sortTable('result')">检测结果 <span class="sort-indicator"></span></th>
                                <th onclick="sortTable('confidence')">置信度 <span class="sort-indicator"></span></th>
                                <th onclick="sortTable('start_time')">开始时间 <span class="sort-indicator"></span></th>
                                <th onclick="sortTable('duration')">耗时(秒) <span class="sort-indicator"></span></th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="tableBody">
                            <!-- 数据将由JavaScript动态填充 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页组件 -->
                <div class="pagination-container" id="paginationContainer">
                    <!-- 分页控件将由JavaScript动态生成 -->
                </div>
            </div>
        </main>
    </div>

    <!-- 引入JavaScript文件 -->
    <script src="../js/jquery.min.js"></script>
    <script src="../js/perfect-scrollbar.min.js"></script>
    <script src="../js/popper.min.js"></script>
    <script src="../js/common.js"></script>
    <script src="../js/menu.js"></script>
    <script src="../js/ai_inspection.js"></script>
</body>
</html>