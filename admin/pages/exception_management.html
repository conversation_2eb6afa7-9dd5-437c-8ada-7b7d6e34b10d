<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>异常闭环管理 - 零售站点前置仓巡检系统</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/perfect-scrollbar.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/exception_management.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-2 d-none d-md-block sidebar">
                <div class="sidebar-sticky">
                    <div class="sidebar-header">
                        <h4>巡检系统</h4>
                    </div>
                    <div id="sidebarNav"></div>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-10 ml-sm-auto col-lg-10 px-4">
                <!-- 顶部导航 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">异常闭环管理</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group mr-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportData()">导出数据</button>
                        </div>
                    </div>
                </div>

                <!-- 工具栏 -->
                <div class="toolbar">
                    <div class="toolbar-left">
                        <button class="btn btn-primary" onclick="openCreateModal()">
                            <i class="fas fa-plus"></i> 创建异常
                        </button>
                        <button class="btn btn-success" onclick="batchAssign()">
                            <i class="fas fa-user-plus"></i> 批量分配
                        </button>
                        <button class="btn btn-warning" onclick="escalateSelected()">
                            <i class="fas fa-level-up-alt"></i> 升级处理
                        </button>
                        <button class="btn btn-danger" onclick="closeSelected()">
                            <i class="fas fa-times"></i> 批量关闭
                        </button>
                    </div>
                    <div class="toolbar-right">
                        <div class="search-box">
                            <input type="text" id="searchInput" placeholder="搜索异常ID、描述..." onkeyup="handleSearch()">
                            <i class="fas fa-search"></i>
                        </div>
                        <select id="statusFilter" onchange="handleFilter()">
                            <option value="">全部状态</option>
                            <option value="open">待处理</option>
                            <option value="assigned">已分配</option>
                            <option value="processing">处理中</option>
                            <option value="resolved">已解决</option>
                            <option value="closed">已关闭</option>
                        </select>
                        <select id="priorityFilter" onchange="handleFilter()">
                            <option value="">全部优先级</option>
                            <option value="critical">紧急</option>
                            <option value="high">高</option>
                            <option value="medium">中</option>
                            <option value="low">低</option>
                        </select>
                        <select id="categoryFilter" onchange="handleFilter()">
                            <option value="">全部类别</option>
                            <option value="equipment">设备故障</option>
                            <option value="environment">环境异常</option>
                            <option value="inventory">库存异常</option>
                            <option value="safety">安全问题</option>
                        </select>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon open">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number" id="openCount">0</div>
                            <div class="stat-label">待处理</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon processing">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number" id="processingCount">0</div>
                            <div class="stat-label">处理中</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon resolved">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number" id="resolvedCount">0</div>
                            <div class="stat-label">已解决</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon overdue">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number" id="overdueCount">0</div>
                            <div class="stat-label">超时</div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="table-container">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="selectAll" onchange="toggleSelectAll()"></th>
                                <th onclick="sortTable('exceptionId')">异常ID <i class="fas fa-sort"></i></th>
                                <th onclick="sortTable('title')">标题 <i class="fas fa-sort"></i></th>
                                <th onclick="sortTable('category')">类别 <i class="fas fa-sort"></i></th>
                                <th onclick="sortTable('priority')">优先级 <i class="fas fa-sort"></i></th>
                                <th onclick="sortTable('status')">状态 <i class="fas fa-sort"></i></th>
                                <th onclick="sortTable('reporter')">报告人 <i class="fas fa-sort"></i></th>
                                <th onclick="sortTable('assignee')">处理人 <i class="fas fa-sort"></i></th>
                                <th onclick="sortTable('createdTime')">创建时间 <i class="fas fa-sort"></i></th>
                                <th onclick="sortTable('slaStatus')">SLA状态 <i class="fas fa-sort"></i></th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="dataTableBody">
                            <!-- 数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页组件 -->
                <div id="paginationContainer"></div>
            </main>
        </div>
    </div>

    <!-- 创建/编辑异常模态框 -->
    <div class="modal fade" id="exceptionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exceptionModalTitle">创建异常</h5>
                    <button type="button" class="close" onclick="closeModal('exceptionModal')">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="exceptionForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="title">标题</label>
                                    <input type="text" class="form-control" id="title" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="category">类别</label>
                                    <select class="form-control" id="category" required>
                                        <option value="">请选择</option>
                                        <option value="equipment">设备故障</option>
                                        <option value="environment">环境异常</option>
                                        <option value="inventory">库存异常</option>
                                        <option value="safety">安全问题</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="priority">优先级</label>
                                    <select class="form-control" id="priority" required>
                                        <option value="">请选择</option>
                                        <option value="critical">紧急</option>
                                        <option value="high">高</option>
                                        <option value="medium">中</option>
                                        <option value="low">低</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="assignee">处理人</label>
                                    <select class="form-control" id="assignee">
                                        <option value="">请选择</option>
                                        <option value="张三">张三</option>
                                        <option value="李四">李四</option>
                                        <option value="王五">王五</option>
                                        <option value="赵六">赵六</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="location">位置</label>
                                    <input type="text" class="form-control" id="location" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="expectedResolution">预期解决时间</label>
                                    <input type="datetime-local" class="form-control" id="expectedResolution">
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="description">详细描述</label>
                            <textarea class="form-control" id="description" rows="4" required></textarea>
                        </div>
                        <div class="form-group">
                            <label for="impact">影响范围</label>
                            <textarea class="form-control" id="impact" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('exceptionModal')">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="saveException()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 异常详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">异常详情</h5>
                    <button type="button" class="close" onclick="closeModal('detailModal')">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="exceptionDetailContent">
                        <!-- 详情内容将通过JavaScript动态加载 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('detailModal')">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/jquery.min.js"></script>
    <script src="../js/popper.min.js"></script>
    <script src="../js/perfect-scrollbar.min.js"></script>
    <script src="../js/common.js"></script>
    <script src="../js/menu.js"></script>
    <script src="../js/exception_management.js"></script>
</body>
</html>