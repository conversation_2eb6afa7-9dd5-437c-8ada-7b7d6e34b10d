<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多终端协同操作 - 基于零售站点前置仓的巡检系统</title>

    <!-- 引入CSS文件 -->
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/perfect-scrollbar.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/menu.css">
    <link rel="stylesheet" href="../css/multi_terminal.css">
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h1>
                    <span class="logo-icon">🏪</span>
                    基于零售站点前置仓的巡检系统
                </h1>
            </div>
            <nav class="sidebar-nav" id="sidebarNav">
                <!-- 菜单将由menu.js动态生成 -->
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部导航栏 -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <span class="hamburger"></span>
                    </button>
                    <h2 class="page-title">多终端协同操作</h2>
                </div>
                <div class="top-bar-right">
                    <div class="user-info">
                        <span class="user-name">管理员</span>
                        <div class="user-avatar">A</div>
                    </div>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="content-wrapper">
                <!-- 工具栏 -->
                <div class="toolbar">
                    <div class="toolbar-left">
                        <div class="search-box">
                            <input type="text" id="searchInput" placeholder="搜索终端ID、名称或位置..." class="search-input">
                            <button class="search-btn" onclick="handleSearch()">🔍</button>
                        </div>
                        <div class="filter-box">
                            <select id="statusFilter" onchange="handleFilter()">
                                <option value="">全部状态</option>
                                <option value="在线">在线</option>
                                <option value="离线">离线</option>
                                <option value="空闲">空闲</option>
                            </select>
                            <select id="typeFilter" onchange="handleFilter()">
                                <option value="">全部类型</option>
                                <option value="手持终端">手持终端</option>
                                <option value="平板电脑">平板电脑</option>
                                <option value="信息亭">信息亭</option>
                                <option value="桌面终端">桌面终端</option>
                            </select>
                        </div>
                    </div>
                    <div class="toolbar-right">
                        <button class="btn btn-secondary" onclick="refreshData()">刷新</button>
                        <button class="btn btn-primary" onclick="batchSync()">批量同步</button>
                        <button class="btn btn-success" onclick="showAddTerminalModal()">添加终端</button>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="stats-cards">
                    <div class="stat-card">
                        <div class="stat-icon">📱</div>
                        <div class="stat-content">
                            <div class="stat-number" id="totalTerminals">0</div>
                            <div class="stat-label">总终端数量</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">🟢</div>
                        <div class="stat-content">
                            <div class="stat-number" id="onlineTerminals">0</div>
                            <div class="stat-label">在线终端</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">🔄</div>
                        <div class="stat-content">
                            <div class="stat-number" id="syncedTerminals">0</div>
                            <div class="stat-label">已同步终端</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">⚠️</div>
                        <div class="stat-content">
                            <div class="stat-number" id="lowBatteryTerminals">0</div>
                            <div class="stat-label">低电量终端</div>
                        </div>
                    </div>
                </div>

                <!-- 终端卡片网格 -->
                <div class="terminal-grid" id="terminalGrid">
                    <!-- 终端卡片将由JavaScript动态生成 -->
                </div>

                <!-- 分页组件 -->
                <div class="pagination-container" id="paginationContainer">
                    <!-- 分页控件将由JavaScript动态生成 -->
                </div>
            </div>
        </main>
    </div>

    <!-- 添加终端模态框 -->
    <div class="modal-overlay" id="addTerminalModal">
        <div class="modal-container">
            <div class="modal-header">
                <h3 class="modal-title">添加终端</h3>
                <button class="modal-close" onclick="closeModal('addTerminalModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="addTerminalForm">
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="terminalName">终端名称</label>
                                <input type="text" class="form-control" id="terminalName" required>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="terminalType">终端类型</label>
                                <select class="form-control" id="terminalType" required>
                                    <option value="手持终端">手持终端</option>
                                    <option value="平板电脑">平板电脑</option>
                                    <option value="信息亭">信息亭</option>
                                    <option value="桌面终端">桌面终端</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="terminalLocation">位置</label>
                                <input type="text" class="form-control" id="terminalLocation" required>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="terminalStatus">状态</label>
                                <select class="form-control" id="terminalStatus" required>
                                    <option value="在线">在线</option>
                                    <option value="离线">离线</option>
                                    <option value="空闲">空闲</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="terminalVersion">软件版本</label>
                                <input type="text" class="form-control" id="terminalVersion" value="1.0.0">
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="terminalBattery">电池电量 (%)</label>
                                <input type="number" class="form-control" id="terminalBattery" min="0" max="100" value="100">
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="terminalDescription">描述</label>
                        <textarea class="form-control" id="terminalDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('addTerminalModal')">取消</button>
                <button class="btn btn-primary" onclick="addTerminal()">添加</button>
            </div>
        </div>
    </div>

    <!-- 远程控制模态框 -->
    <div class="modal-overlay" id="remoteControlModal">
        <div class="modal-container">
            <div class="modal-header">
                <h3 class="modal-title">远程控制 - <span id="remoteTerminalName"></span></h3>
                <button class="modal-close" onclick="closeModal('remoteControlModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div class="remote-control-container">
                    <div class="remote-screen" id="remoteScreen">
                        <div class="screen-placeholder">
                            <p>终端屏幕预览</p>
                            <p>正在连接...</p>
                        </div>
                    </div>
                    <div class="remote-controls">
                        <button class="remote-btn remote-btn-primary" onclick="remoteRefresh()">
                            🔄 刷新屏幕
                        </button>
                        <button class="remote-btn remote-btn-secondary" onclick="remoteRestart()">
                            🔁 重启应用
                        </button>
                        <button class="remote-btn remote-btn-secondary" onclick="remoteClearCache()">
                            🧹 清除缓存
                        </button>
                        <button class="remote-btn remote-btn-secondary" onclick="remoteUpdateApp()">
                            📦 更新应用
                        </button>
                        <button class="remote-btn remote-btn-danger" onclick="remoteShutdown()">
                            ⚠️ 关闭终端
                        </button>
                    </div>
                    <div class="terminal-logs" id="remoteLogs">
                        <div class="log-entry">
                            <span class="log-time">10:45:23</span> 远程控制会话已建立
                        </div>
                        <div class="log-entry">
                            <span class="log-time">10:45:24</span> 正在获取终端信息...
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('remoteControlModal')">关闭连接</button>
            </div>
        </div>
    </div>

    <!-- 终端详情模态框 -->
    <div class="modal-overlay" id="terminalDetailModal">
        <div class="modal-container">
            <div class="modal-header">
                <h3 class="modal-title">终端详情</h3>
                <button class="modal-close" onclick="closeModal('terminalDetailModal')">&times;</button>
            </div>
            <div class="modal-body" id="terminalDetailContent">
                <!-- 详情内容将由JavaScript动态填充 -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="closeModal('terminalDetailModal')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 编辑终端模态框 -->
    <div class="modal-overlay" id="editTerminalModal">
        <div class="modal-container">
            <div class="modal-header">
                <h3 class="modal-title">编辑终端</h3>
                <button class="modal-close" onclick="closeModal('editTerminalModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="editTerminalForm">
                    <input type="hidden" id="editTerminalId">
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="editTerminalName">终端名称</label>
                                <input type="text" class="form-control" id="editTerminalName" required>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="editTerminalType">终端类型</label>
                                <select class="form-control" id="editTerminalType" required>
                                    <option value="手持终端">手持终端</option>
                                    <option value="平板电脑">平板电脑</option>
                                    <option value="信息亭">信息亭</option>
                                    <option value="桌面终端">桌面终端</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="editTerminalLocation">位置</label>
                                <input type="text" class="form-control" id="editTerminalLocation" required>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="editTerminalStatus">状态</label>
                                <select class="form-control" id="editTerminalStatus" required>
                                    <option value="在线">在线</option>
                                    <option value="离线">离线</option>
                                    <option value="空闲">空闲</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="editTerminalVersion">软件版本</label>
                                <input type="text" class="form-control" id="editTerminalVersion">
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label" for="editTerminalBattery">电池电量 (%)</label>
                                <input type="number" class="form-control" id="editTerminalBattery" min="0" max="100">
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="editTerminalDescription">描述</label>
                        <textarea class="form-control" id="editTerminalDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('editTerminalModal')">取消</button>
                <button class="btn btn-primary" onclick="saveTerminalEdit()">保存</button>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript文件 -->
    <script src="../js/jquery.min.js"></script>
    <script src="../js/perfect-scrollbar.min.js"></script>
    <script src="../js/popper.min.js"></script>
    <script src="../js/common.js"></script>
    <script src="../js/menu.js"></script>
    <script src="../js/multi_terminal.js"></script>
</body>
</html>