<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多终端协同操作 - 零售站点前置仓巡检系统</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/perfect-scrollbar.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/multi_terminal.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-2 d-none d-md-block sidebar">
                <div class="sidebar-sticky">
                    <div class="sidebar-header">
                        <h4>巡检系统</h4>
                    </div>
                    <div id="sidebarNav"></div>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-10 ml-sm-auto col-lg-10 px-4">
                <!-- 顶部导航 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">多终端协同操作</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group mr-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportData()">导出数据</button>
                        </div>
                    </div>
                </div>

                <!-- 工具栏 -->
                <div class="toolbar">
                    <div class="toolbar-left">
                        <button class="btn btn-primary" onclick="openCreateModal()">
                            <i class="fas fa-plus"></i> 创建会话
                        </button>
                        <button class="btn btn-success" onclick="syncAllTerminals()">
                            <i class="fas fa-sync"></i> 同步所有终端
                        </button>
                        <button class="btn btn-warning" onclick="disconnectOfflineTerminals()">
                            <i class="fas fa-unlink"></i> 断开离线终端
                        </button>
                    </div>
                    <div class="toolbar-right">
                        <div class="search-box">
                            <input type="text" id="searchInput" placeholder="搜索会话ID、操作员..." onkeyup="handleSearch()">
                            <i class="fas fa-search"></i>
                        </div>
                        <select id="statusFilter" onchange="handleFilter()">
                            <option value="">全部状态</option>
                            <option value="active">活跃</option>
                            <option value="idle">空闲</option>
                            <option value="offline">离线</option>
                            <option value="error">错误</option>
                        </select>
                        <select id="terminalFilter" onchange="handleFilter()">
                            <option value="">全部终端</option>
                            <option value="pda">PDA</option>
                            <option value="ar">AR眼镜</option>
                            <option value="tablet">平板电脑</option>
                            <option value="mobile">手机</option>
                        </select>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon active">
                            <i class="fas fa-circle"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number" id="activeCount">0</div>
                            <div class="stat-label">活跃终端</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon idle">
                            <i class="fas fa-pause-circle"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number" id="idleCount">0</div>
                            <div class="stat-label">空闲终端</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon offline">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number" id="offlineCount">0</div>
                            <div class="stat-label">离线终端</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon syncing">
                            <i class="fas fa-sync-alt"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number" id="syncingCount">0</div>
                            <div class="stat-label">同步中</div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="table-container">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="selectAll" onchange="toggleSelectAll()"></th>
                                <th onclick="sortTable('sessionId')">会话ID <i class="fas fa-sort"></i></th>
                                <th onclick="sortTable('operator')">操作员 <i class="fas fa-sort"></i></th>
                                <th onclick="sortTable('terminalType')">终端类型 <i class="fas fa-sort"></i></th>
                                <th onclick="sortTable('status')">状态 <i class="fas fa-sort"></i></th>
                                <th onclick="sortTable('location')">位置 <i class="fas fa-sort"></i></th>
                                <th onclick="sortTable('lastSync')">最后同步 <i class="fas fa-sort"></i></th>
                                <th onclick="sortTable('dataSize')">数据量 <i class="fas fa-sort"></i></th>
                                <th onclick="sortTable('battery')">电量 <i class="fas fa-sort"></i></th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="dataTableBody">
                            <!-- 数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页组件 -->
                <div id="paginationContainer"></div>
            </main>
        </div>
    </div>

    <!-- 创建/编辑会话模态框 -->
    <div class="modal fade" id="sessionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="sessionModalTitle">创建会话</h5>
                    <button type="button" class="close" onclick="closeModal('sessionModal')">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="sessionForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="operator">操作员</label>
                                    <select class="form-control" id="operator" required>
                                        <option value="">请选择</option>
                                        <option value="张三">张三</option>
                                        <option value="李四">李四</option>
                                        <option value="王五">王五</option>
                                        <option value="赵六">赵六</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="terminalType">终端类型</label>
                                    <select class="form-control" id="terminalType" required>
                                        <option value="">请选择</option>
                                        <option value="pda">PDA</option>
                                        <option value="ar">AR眼镜</option>
                                        <option value="tablet">平板电脑</option>
                                        <option value="mobile">手机</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="location">位置</label>
                                    <input type="text" class="form-control" id="location" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="priority">优先级</label>
                                    <select class="form-control" id="priority" required>
                                        <option value="">请选择</option>
                                        <option value="high">高</option>
                                        <option value="medium">中</option>
                                        <option value="low">低</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="description">描述</label>
                            <textarea class="form-control" id="description" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="syncSettings">同步设置</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="autoSync" checked>
                                <label class="form-check-label" for="autoSync">自动同步</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="realTimeSync">
                                <label class="form-check-label" for="realTimeSync">实时同步</label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('sessionModal')">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="saveSession()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 会话详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">会话详情</h5>
                    <button type="button" class="close" onclick="closeModal('detailModal')">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="sessionDetailContent">
                        <!-- 详情内容将通过JavaScript动态加载 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('detailModal')">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/jquery.min.js"></script>
    <script src="../js/popper.min.js"></script>
    <script src="../js/perfect-scrollbar.min.js"></script>
    <script src="../js/common.js"></script>
    <script src="../js/menu.js"></script>
    <script src="../js/multi_terminal.js"></script>
</body>
</html>