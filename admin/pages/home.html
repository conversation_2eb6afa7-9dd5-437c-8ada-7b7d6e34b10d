<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基于零售站点前置仓的巡检系统 - 后台管理</title>

    <!-- 引入CSS文件 -->
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/perfect-scrollbar.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/menu.css">
    <link rel="stylesheet" href="../css/home.css">

    <!-- Chart.js图表库 -->
    <script src="../js/chart.min.js"></script>
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h1>
                    <span class="logo-icon">🏪</span>
                    基于零售站点前置仓的巡检系统
                </h1>
            </div>
            <nav class="sidebar-nav" id="sidebarNav">
                <!-- 菜单将由menu.js动态生成 -->
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部导航栏 -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <span class="hamburger"></span>
                    </button>
                    <h2 class="page-title">系统概览</h2>
                </div>
                <div class="top-bar-right">
                    <div class="user-info">
                        <span class="user-name">管理员</span>
                        <div class="user-avatar">A</div>
                    </div>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="content-wrapper">

            <!-- 统计卡片网格 -->
            <section class="stats-grid">
                <!-- 设备总数 -->
                <div class="stat-card" id="totalDevices">
                    <div class="stat-header">
                        <span class="stat-title">设备总数</span>
                        <div class="stat-icon primary">📱</div>
                    </div>
                    <div class="stat-value">142台</div>
                    <div class="stat-desc">
                        <span class="stat-trend up">+5.6%</span>
                        相比上月
                    </div>
                </div>

                <!-- 在线设备 -->
                <div class="stat-card" id="onlineDevices">
                    <div class="stat-header">
                        <span class="stat-title">在线设备</span>
                        <div class="stat-icon success">✅</div>
                    </div>
                    <div class="stat-value">125台</div>
                    <div class="stat-desc">
                        <span class="stat-trend up">+5.9%</span>
                        相比上月
                    </div>
                </div>

                <!-- 巡检次数 -->
                <div class="stat-card" id="inspectionCount">
                    <div class="stat-header">
                        <span class="stat-title">本月巡检</span>
                        <div class="stat-icon primary">🔍</div>
                    </div>
                    <div class="stat-value">1580次</div>
                    <div class="stat-desc">
                        <span class="stat-trend up">+12.8%</span>
                        相比上月
                    </div>
                </div>

                <!-- 异常数量 -->
                <div class="stat-card" id="exceptionCount">
                    <div class="stat-header">
                        <span class="stat-title">异常数量</span>
                        <div class="stat-icon error">⚠️</div>
                    </div>
                    <div class="stat-value">23个</div>
                    <div class="stat-desc">
                        <span class="stat-trend down">-18.2%</span>
                        相比上月
                    </div>
                </div>

                <!-- 今日巡检 -->
                <div class="stat-card" id="todayInspections">
                    <div class="stat-header">
                        <span class="stat-title">今日巡检</span>
                        <div class="stat-icon warning">📊</div>
                    </div>
                    <div class="stat-value">86次</div>
                    <div class="stat-desc">
                        进行中: 3次
                    </div>
                </div>

                <!-- 平均效率 -->
                <div class="stat-card" id="avgEfficiency">
                    <div class="stat-header">
                        <span class="stat-title">平均效率</span>
                        <div class="stat-icon success">📈</div>
                    </div>
                    <div class="stat-value">92.5%</div>
                    <div class="stat-desc">
                        <span class="stat-trend up">+2.3%</span>
                        相比上月
                    </div>
                </div>
            </section>

            <!-- 图表网格 -->
            <section class="charts-grid">
                <!-- 设备状态分布 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">设备状态分布</h3>
                        <div class="chart-actions">
                            <button class="btn-small btn-refresh">刷新</button>
                            <button class="btn-small btn-export">导出</button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="deviceStatusChart"></canvas>
                    </div>
                </div>

                <!-- 巡检趋势 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">巡检趋势（最近7天）</h3>
                        <div class="chart-actions">
                            <button class="btn-small btn-refresh">刷新</button>
                            <button class="btn-small btn-export">导出</button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="inspectionTrendChart"></canvas>
                    </div>
                </div>

                <!-- 异常类型分布 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">异常类型分布</h3>
                        <div class="chart-actions">
                            <button class="btn-small btn-refresh">刷新</button>
                            <button class="btn-small btn-export">导出</button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="exceptionTypesChart"></canvas>
                    </div>
                </div>

                <!-- 热点区域分析 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">热点区域巡检频次</h3>
                        <div class="chart-actions">
                            <button class="btn-small btn-refresh">刷新</button>
                            <button class="btn-small btn-export">导出</button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="hotAreasChart"></canvas>
                    </div>
                </div>
            </section>

            <!-- 快速功能 -->
            <section class="function-section">
                <h3 style="margin-bottom: 24px; color: var(--td-text-color-primary); font-size: 18px; font-weight: 600;">快速功能</h3>
                <div class="function-grid">
                    <a href="ai_inspection.html" class="function-card">
                        <div class="function-icon">👁️</div>
                        <div class="function-title">AI视觉识别</div>
                        <div class="function-desc">查看AI识别结果和分析报告</div>
                    </a>

                    <a href="realtime_monitoring.html" class="function-card">
                        <div class="function-icon">📊</div>
                        <div class="function-title">实时监控</div>
                        <div class="function-desc">查看设备状态和环境参数</div>
                    </a>

                    <a href="data_collection.html" class="function-card">
                        <div class="function-icon">📈</div>
                        <div class="function-title">数据分析</div>
                        <div class="function-desc">查看数据采集和分析结果</div>
                    </a>

                    <a href="exception_management.html" class="function-card">
                        <div class="function-icon">⚠️</div>
                        <div class="function-title">异常管理</div>
                        <div class="function-desc">处理异常问题和闭环管理</div>
                    </a>

                    <a href="sensor_device.html" class="function-card">
                        <div class="function-icon">🔧</div>
                        <div class="function-title">设备管理</div>
                        <div class="function-desc">管理传感器和终端设备</div>
                    </a>

                    <a href="platform_architecture.html" class="function-card">
                        <div class="function-icon">⚙️</div>
                        <div class="function-title">系统设置</div>
                        <div class="function-desc">配置系统参数和权限</div>
                    </a>
                </div>
            </section>
            </div>
        </main>
    </div>

    <!-- 引入JavaScript文件 -->
    <script src="../js/jquery.min.js"></script>
    <script src="../js/popper.min.js"></script>
    <script src="../js/perfect-scrollbar.min.js"></script>
    <script src="../js/common.js"></script>
    <script src="../js/menu.js"></script>
    <script src="../js/home.js"></script>
</body>
</html>