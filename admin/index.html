<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8">
    <title>零售巡检系统 - 管理后台登录</title>
    <meta name="viewport" content="width=device-width, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel='stylesheet' href="css/bootstrap.min.css">
    <link rel='stylesheet' href="css/perfect-scrollbar.min.css">
    <link rel="stylesheet" href="css/style.css">
</head>

<body>
    <div class="container-fluid">
        <div class="row no-gutters">
            <div class="col-12 col-sm-12 col-md-12 col-lg-5 text-center left__section">
                <div class="dowebok">
                    <h1 class="heading my-5">基于零售站点前置仓的巡检系统</h1>
                    <p class="paragraph">管理后台登录</p>
                    <form id="loginForm" class="mt-4">
                        <div class="input-group my-4">
                            <div class="input-group-prepend">
                                <label for="username" class="d-block mx-auto"><img src="images/8dq.svg"
                                        width="18" height="18" class="mt-2"></label>
                            </div>
                            <input type="text" id="username" class="form-control" placeholder="请输入用户名" aria-label=""
                                aria-describedby="basic-addon1" value="admin">
                        </div>
                        <div class="input-group my-4">
                            <div class="input-group-prepend">
                                <label for="password" class="d-block mx-auto"><img src="images/8dT.svg"
                                        width="18" height="18" class="mt-2"></label>
                            </div>
                            <input type="password" id="password" class="form-control" placeholder="请输入密码" aria-label=""
                                aria-describedby="basic-addon1" value="admin123">
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
<!--                            <a href="javascript:" class="link">忘记密码？</a>-->
                            <button type="submit" class="btn btn-primary">登录</button>
                        </div>
<!--                        <div class="form-check float-left my-3">-->
<!--                            <input class="form-check-input" type="checkbox" value="" id="defaultCheck1">-->
<!--                            <label class="form-check-label" for="defaultCheck1">记住我</label>-->
<!--                        </div>-->
                    </form>
                    <div class="col-12 my-4">
                        <span class="divider">系统说明</span>
                    </div>
                    <div class="system-info">
                        <p>基于零售站点前置仓的智能巡检管理系统</p>
                        <p>默认账号：admin / admin123</p>
                    </div>
                </div>
            </div>

            <div class="col-12 col-sm-12 col-md-12 col-lg-7">
                <div class="bg"></div>
            </div>
        </div>
    </div>
    <script src="js/jquery.min.js"></script>
    <script src="js/popper.min.js"></script>
    <script src="js/perfect-scrollbar.min.js"></script>
    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (username === 'admin' && password === 'admin123') {
                // 登录成功，跳转到后台首页
                alert('登录成功！');
                window.location.href = 'pages/home.html';
            } else {
                alert('用户名或密码错误！');
            }
        });
    </script>

    <div style="display: none"><a href="http://www.dowebok.com/">dowebok</a></div>
</body>

</html>