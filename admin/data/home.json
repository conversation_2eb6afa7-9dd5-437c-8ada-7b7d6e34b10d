{"systemOverview": {"totalDevices": 142, "onlineDevices": 125, "offlineDevices": 17, "faultDevices": 8, "maintenanceDevices": 12, "totalLocations": 38, "activeLocations": 35, "inspectionCount": 1580, "exceptionCount": 23, "completedTasks": 1557, "todayInspections": 86, "avgEfficiency": 92.5, "monthlyTrend": 12.8, "alertCount": 5}, "recentInspections": [{"id": 1, "location": "A区-货架001", "type": "AI视觉识别", "status": "已完成", "time": "2024-01-15 14:30:25", "inspector": "张维修", "result": "正常", "details": "商品陈列规范，无过期商品"}, {"id": 2, "location": "B区-冷柜002", "type": "温度监控", "status": "已完成", "time": "2024-01-15 14:25:18", "inspector": "李巡检", "result": "异常", "details": "温度超出范围，需要维护"}, {"id": 3, "location": "C区-货架005", "type": "库存同步", "status": "进行中", "time": "2024-01-15 14:20:42", "inspector": "王技术", "result": "处理中", "details": "正在进行库存核对"}, {"id": 4, "location": "D区-收银台", "type": "设备检测", "status": "已完成", "time": "2024-01-15 14:15:33", "inspector": "赵工程师", "result": "正常", "details": "设备运行正常，数据同步正常"}, {"id": 5, "location": "E区-入口闸机", "type": "人员动线", "status": "已完成", "time": "2024-01-15 14:10:29", "inspector": "钱分析师", "result": "正常", "details": "客流数据采集正常"}], "exceptionAlerts": [{"id": 1, "type": "商品有效期", "level": "高", "location": "A区-货架003", "message": "发现8件商品即将过期", "time": "2024-01-15 14:35:12", "status": "待处理", "assignee": "张管理员"}, {"id": 2, "type": "设备故障", "level": "中", "location": "B区-冷柜001", "message": "冷柜温度传感器异常", "time": "2024-01-15 14:30:45", "status": "处理中", "assignee": "李技术员"}, {"id": 3, "type": "货架陈列", "level": "低", "location": "C区-货架008", "message": "商品陈列不规范", "time": "2024-01-15 14:25:33", "status": "已处理", "assignee": "王营业员"}, {"id": 4, "type": "环境异常", "level": "中", "location": "D区-仓储区", "message": "湿度超出标准范围", "time": "2024-01-15 14:20:18", "status": "待处理", "assignee": "赵维护员"}, {"id": 5, "type": "库存异常", "level": "高", "location": "E区-货架012", "message": "库存数据与实物不符", "time": "2024-01-15 14:15:56", "status": "处理中", "assignee": "钱库管员"}], "deviceStatus": {"totalDevices": 142, "statusDistribution": {"online": 125, "offline": 17, "fault": 8, "maintenance": 12}, "typeDistribution": {"cameras": 45, "sensors": 38, "rfidReaders": 28, "terminals": 21, "robots": 10}, "locationDistribution": {"areaA": 35, "areaB": 42, "areaC": 28, "areaD": 25, "areaE": 12}}, "inspectionTrend": {"daily": [{"date": "2024-01-09", "count": 156, "completed": 152, "exceptions": 4}, {"date": "2024-01-10", "count": 142, "completed": 138, "exceptions": 4}, {"date": "2024-01-11", "count": 178, "completed": 171, "exceptions": 7}, {"date": "2024-01-12", "count": 165, "completed": 160, "exceptions": 5}, {"date": "2024-01-13", "count": 189, "completed": 182, "exceptions": 7}, {"date": "2024-01-14", "count": 176, "completed": 169, "exceptions": 7}, {"date": "2024-01-15", "count": 186, "completed": 179, "exceptions": 7}], "hourly": [{"hour": "08:00", "count": 12}, {"hour": "09:00", "count": 18}, {"hour": "10:00", "count": 25}, {"hour": "11:00", "count": 22}, {"hour": "12:00", "count": 15}, {"hour": "13:00", "count": 20}, {"hour": "14:00", "count": 28}, {"hour": "15:00", "count": 24}, {"hour": "16:00", "count": 19}, {"hour": "17:00", "count": 16}, {"hour": "18:00", "count": 14}, {"hour": "19:00", "count": 11}]}, "hotAreas": [{"areaId": "A", "areaName": "A区-生鲜区", "inspectionCount": 95, "exceptionCount": 8, "efficiency": 91.6, "lastInspection": "2024-01-15 14:30:25"}, {"areaId": "B", "areaName": "B区-冷链区", "inspectionCount": 87, "exceptionCount": 6, "efficiency": 93.1, "lastInspection": "2024-01-15 14:25:18"}, {"areaId": "C", "areaName": "C区-百货区", "inspectionCount": 78, "exceptionCount": 4, "efficiency": 94.9, "lastInspection": "2024-01-15 14:20:42"}, {"areaId": "D", "areaName": "D区-收银区", "inspectionCount": 92, "exceptionCount": 3, "efficiency": 96.7, "lastInspection": "2024-01-15 14:15:33"}, {"areaId": "E", "areaName": "E区-仓储区", "inspectionCount": 84, "exceptionCount": 2, "efficiency": 97.6, "lastInspection": "2024-01-15 14:10:29"}, {"areaId": "F", "areaName": "F区-办公区", "inspectionCount": 76, "exceptionCount": 1, "efficiency": 98.7, "lastInspection": "2024-01-15 14:05:15"}], "exceptionTypes": {"distribution": {"商品有效期异常": 8, "货架陈列问题": 6, "设备故障": 4, "环境异常": 3, "库存异常": 2}, "trends": [{"type": "商品有效期异常", "trend": "+2.3%"}, {"type": "货架陈列问题", "trend": "-1.8%"}, {"type": "设备故障", "trend": "+0.5%"}, {"type": "环境异常", "trend": "-3.2%"}, {"type": "库存异常", "trend": "+1.1%"}]}, "systemHealth": {"cpuUsage": 45.2, "memoryUsage": 68.7, "diskUsage": 73.1, "networkStatus": "正常", "databaseStatus": "正常", "apiResponseTime": 125, "onlineUsers": 12, "systemUptime": "15天6小时32分钟"}, "quickActions": [{"id": "aiInspection", "title": "AI视觉识别", "description": "查看AI识别结果和分析报告", "icon": "👁️", "url": "pages/ai_inspection.html", "color": "#0052d9"}, {"id": "realtimeMonitor", "title": "实时监控", "description": "查看设备状态和环境参数", "icon": "📊", "url": "pages/realtime_monitoring.html", "color": "#00a870"}, {"id": "dataAnalysis", "title": "数据分析", "description": "查看数据采集和分析结果", "icon": "📈", "url": "pages/data_collection.html", "color": "#ed7b2f"}, {"id": "exceptionManage", "title": "异常管理", "description": "处理异常问题和闭环管理", "icon": "⚠️", "url": "pages/exception_management.html", "color": "#d54941"}, {"id": "deviceManage", "title": "设备管理", "description": "管理传感器和终端设备", "icon": "🔧", "url": "pages/sensor_device.html", "color": "#666666"}, {"id": "systemSettings", "title": "系统设置", "description": "配置系统参数和权限", "icon": "⚙️", "url": "pages/platform_architecture.html", "color": "#9c27b0"}]}