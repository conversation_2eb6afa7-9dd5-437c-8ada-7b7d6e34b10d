{"ai_inspections": [{"id": 1, "task_id": "AI-20240115-001", "location": "A区-货架001", "device_name": "AI摄像头-001", "inspection_type": "商品陈列检查", "status": "已完成", "result": "正常", "confidence": 95.6, "detected_objects": ["可口可乐", "雪碧", "芬达", "美年达"], "anomalies": [], "start_time": "2024-01-15 14:30:25", "end_time": "2024-01-15 14:30:45", "duration": 20, "inspector": "AI系统", "images": ["snapshot_001.jpg", "analysis_001.jpg"], "temperature": 22.5, "humidity": 65, "lighting": "良好", "notes": "商品陈列规范，库存充足"}, {"id": 2, "task_id": "AI-20240115-002", "location": "B区-冷柜002", "device_name": "AI摄像头-002", "inspection_type": "温度异常检测", "status": "异常", "result": "温度过高", "confidence": 88.9, "detected_objects": ["酸奶", "牛奶", "果汁"], "anomalies": ["温度超标", "门密封不良"], "start_time": "2024-01-15 14:25:18", "end_time": "2024-01-15 14:25:35", "duration": 17, "inspector": "AI系统", "images": ["snapshot_002.jpg", "thermal_002.jpg"], "temperature": 8.5, "humidity": 85, "lighting": "正常", "notes": "冷柜温度异常，需要维修"}, {"id": 3, "task_id": "AI-20240115-003", "location": "C区-货架003", "device_name": "AI摄像头-003", "inspection_type": "商品过期检测", "status": "已完成", "result": "发现过期商品", "confidence": 92.1, "detected_objects": ["面包", "饼干", "方便面"], "anomalies": ["过期商品3件"], "start_time": "2024-01-15 14:20:10", "end_time": "2024-01-15 14:20:28", "duration": 18, "inspector": "AI系统", "images": ["snapshot_003.jpg", "expiry_003.jpg"], "temperature": 23.1, "humidity": 58, "lighting": "良好", "notes": "发现3件过期商品，已标记"}, {"id": 4, "task_id": "AI-20240115-004", "location": "D区-收银台004", "device_name": "AI摄像头-004", "inspection_type": "人员行为分析", "status": "已完成", "result": "正常", "confidence": 97.3, "detected_objects": ["收银员", "顾客", "购物车"], "anomalies": [], "start_time": "2024-01-15 14:15:05", "end_time": "2024-01-15 14:15:25", "duration": 20, "inspector": "AI系统", "images": ["snapshot_004.jpg", "behavior_004.jpg"], "temperature": 24.2, "humidity": 62, "lighting": "良好", "notes": "收银台运营正常"}, {"id": 5, "task_id": "AI-20240115-005", "location": "E区-入口005", "device_name": "AI摄像头-005", "inspection_type": "人流统计", "status": "已完成", "result": "正常", "confidence": 94.7, "detected_objects": ["顾客进入", "顾客离开"], "anomalies": [], "start_time": "2024-01-15 14:10:30", "end_time": "2024-01-15 14:10:50", "duration": 20, "inspector": "AI系统", "images": ["snapshot_005.jpg", "count_005.jpg"], "temperature": 25.0, "humidity": 60, "lighting": "良好", "notes": "入口人流正常"}, {"id": 6, "task_id": "AI-20240115-006", "location": "A区-货架006", "device_name": "AI摄像头-006", "inspection_type": "库存盘点", "status": "进行中", "result": "处理中", "confidence": 0, "detected_objects": [], "anomalies": [], "start_time": "2024-01-15 14:35:00", "end_time": null, "duration": 0, "inspector": "AI系统", "images": [], "temperature": 22.8, "humidity": 64, "lighting": "良好", "notes": "正在进行库存盘点"}, {"id": 7, "task_id": "AI-20240115-007", "location": "F区-冷冻柜007", "device_name": "AI摄像头-007", "inspection_type": "冷冻食品检查", "status": "已完成", "result": "正常", "confidence": 91.4, "detected_objects": ["冰淇淋", "速冻食品", "冷冻肉类"], "anomalies": [], "start_time": "2024-01-15 14:05:15", "end_time": "2024-01-15 14:05:33", "duration": 18, "inspector": "AI系统", "images": ["snapshot_007.jpg", "frozen_007.jpg"], "temperature": -18.2, "humidity": 90, "lighting": "正常", "notes": "冷冻食品储存正常"}, {"id": 8, "task_id": "AI-20240115-008", "location": "G区-货架008", "device_name": "AI摄像头-008", "inspection_type": "价格标签检查", "status": "异常", "result": "标签缺失", "confidence": 89.6, "detected_objects": ["商品", "货架"], "anomalies": ["价格标签缺失5个"], "start_time": "2024-01-15 14:00:20", "end_time": "2024-01-15 14:00:38", "duration": 18, "inspector": "AI系统", "images": ["snapshot_008.jpg", "label_008.jpg"], "temperature": 23.5, "humidity": 61, "lighting": "良好", "notes": "发现5个商品缺失价格标签"}, {"id": 9, "task_id": "AI-20240115-009", "location": "H区-货架009", "device_name": "AI摄像头-009", "inspection_type": "清洁状态检查", "status": "已完成", "result": "需要清洁", "confidence": 86.3, "detected_objects": ["货架", "地面"], "anomalies": ["地面有污渍", "货架灰尘"], "start_time": "2024-01-15 13:55:45", "end_time": "2024-01-15 13:56:02", "duration": 17, "inspector": "AI系统", "images": ["snapshot_009.jpg", "clean_009.jpg"], "temperature": 24.1, "humidity": 59, "lighting": "良好", "notes": "货架区域需要清洁"}, {"id": 10, "task_id": "AI-20240115-010", "location": "I区-货架010", "device_name": "AI摄像头-010", "inspection_type": "商品摆放检查", "status": "已完成", "result": "摆放不规范", "confidence": 93.8, "detected_objects": ["商品A", "商品B", "商品C"], "anomalies": ["摆放位置错误"], "start_time": "2024-01-15 13:50:12", "end_time": "2024-01-15 13:50:30", "duration": 18, "inspector": "AI系统", "images": ["snapshot_010.jpg", "layout_010.jpg"], "temperature": 22.9, "humidity": 63, "lighting": "良好", "notes": "商品摆放需要调整"}, {"id": 11, "task_id": "AI-20240114-011", "location": "A区-货架011", "device_name": "AI摄像头-011", "inspection_type": "商品陈列检查", "status": "已完成", "result": "正常", "confidence": 96.2, "detected_objects": ["洗发水", "沐浴露", "牙膏"], "anomalies": [], "start_time": "2024-01-14 16:30:25", "end_time": "2024-01-14 16:30:42", "duration": 17, "inspector": "AI系统", "images": ["snapshot_011.jpg", "analysis_011.jpg"], "temperature": 23.2, "humidity": 66, "lighting": "良好", "notes": "日化用品陈列规范"}, {"id": 12, "task_id": "AI-20240114-012", "location": "B区-冷柜012", "device_name": "AI摄像头-012", "inspection_type": "温度监控", "status": "已完成", "result": "正常", "confidence": 94.1, "detected_objects": ["饮料", "啤酒"], "anomalies": [], "start_time": "2024-01-14 16:25:10", "end_time": "2024-01-14 16:25:27", "duration": 17, "inspector": "AI系统", "images": ["snapshot_012.jpg", "thermal_012.jpg"], "temperature": 4.2, "humidity": 82, "lighting": "正常", "notes": "饮料冷柜温度正常"}, {"id": 13, "task_id": "AI-20240114-013", "location": "C区-货架013", "device_name": "AI摄像头-013", "inspection_type": "安全检查", "status": "异常", "result": "安全隐患", "confidence": 87.5, "detected_objects": ["货架", "包装箱"], "anomalies": ["货架倾斜", "包装箱堆放过高"], "start_time": "2024-01-14 16:20:05", "end_time": "2024-01-14 16:20:23", "duration": 18, "inspector": "AI系统", "images": ["snapshot_013.jpg", "safety_013.jpg"], "temperature": 23.8, "humidity": 58, "lighting": "良好", "notes": "发现安全隐患，需要处理"}, {"id": 14, "task_id": "AI-20240114-014", "location": "D区-收银台014", "device_name": "AI摄像头-014", "inspection_type": "排队情况分析", "status": "已完成", "result": "排队较长", "confidence": 91.7, "detected_objects": ["顾客队列", "收银员"], "anomalies": ["排队人数超过5人"], "start_time": "2024-01-14 16:15:30", "end_time": "2024-01-14 16:15:48", "duration": 18, "inspector": "AI系统", "images": ["snapshot_014.jpg", "queue_014.jpg"], "temperature": 24.5, "humidity": 61, "lighting": "良好", "notes": "收银台排队较长，建议开启更多收银台"}, {"id": 15, "task_id": "AI-20240114-015", "location": "E区-出口015", "device_name": "AI摄像头-015", "inspection_type": "防盗检测", "status": "已完成", "result": "正常", "confidence": 98.4, "detected_objects": ["顾客", "购物袋", "防盗门"], "anomalies": [], "start_time": "2024-01-14 16:10:15", "end_time": "2024-01-14 16:10:32", "duration": 17, "inspector": "AI系统", "images": ["snapshot_015.jpg", "security_015.jpg"], "temperature": 25.2, "humidity": 59, "lighting": "良好", "notes": "出口防盗检测正常"}, {"id": 16, "task_id": "AI-20240114-016", "location": "F区-货架016", "device_name": "AI摄像头-016", "inspection_type": "库存预警", "status": "已完成", "result": "库存不足", "confidence": 89.9, "detected_objects": ["商品A", "商品B"], "anomalies": ["库存低于阈值"], "start_time": "2024-01-14 16:05:20", "end_time": "2024-01-14 16:05:38", "duration": 18, "inspector": "AI系统", "images": ["snapshot_016.jpg", "stock_016.jpg"], "temperature": 22.7, "humidity": 65, "lighting": "良好", "notes": "部分商品库存不足，需要补货"}, {"id": 17, "task_id": "AI-20240114-017", "location": "G区-货架017", "device_name": "AI摄像头-017", "inspection_type": "商品质量检查", "status": "已完成", "result": "发现问题商品", "confidence": 85.6, "detected_objects": ["水果", "蔬菜"], "anomalies": ["部分水果变质", "蔬菜不新鲜"], "start_time": "2024-01-14 16:00:10", "end_time": "2024-01-14 16:00:28", "duration": 18, "inspector": "AI系统", "images": ["snapshot_017.jpg", "quality_017.jpg"], "temperature": 18.5, "humidity": 75, "lighting": "良好", "notes": "发现变质水果和不新鲜蔬菜"}, {"id": 18, "task_id": "AI-20240114-018", "location": "H区-货架018", "device_name": "AI摄像头-018", "inspection_type": "灯光检查", "status": "异常", "result": "照明不足", "confidence": 92.3, "detected_objects": ["灯具", "货架"], "anomalies": ["2盏灯具故障"], "start_time": "2024-01-14 15:55:05", "end_time": "2024-01-14 15:55:22", "duration": 17, "inspector": "AI系统", "images": ["snapshot_018.jpg", "lighting_018.jpg"], "temperature": 23.4, "humidity": 62, "lighting": "暗", "notes": "2盏灯具故障，照明不足"}, {"id": 19, "task_id": "AI-20240114-019", "location": "I区-货架019", "device_name": "AI摄像头-019", "inspection_type": "通道检查", "status": "已完成", "result": "通道阻塞", "confidence": 88.7, "detected_objects": ["购物车", "货物"], "anomalies": ["通道有障碍物"], "start_time": "2024-01-14 15:50:40", "end_time": "2024-01-14 15:50:57", "duration": 17, "inspector": "AI系统", "images": ["snapshot_019.jpg", "aisle_019.jpg"], "temperature": 24.0, "humidity": 60, "lighting": "良好", "notes": "通道有购物车阻塞"}, {"id": 20, "task_id": "AI-20240114-020", "location": "J区-货架020", "device_name": "AI摄像头-020", "inspection_type": "促销标识检查", "status": "已完成", "result": "标识过期", "confidence": 90.5, "detected_objects": ["促销标签", "商品"], "anomalies": ["3个促销标识过期"], "start_time": "2024-01-14 15:45:25", "end_time": "2024-01-14 15:45:42", "duration": 17, "inspector": "AI系统", "images": ["snapshot_020.jpg", "promo_020.jpg"], "temperature": 23.1, "humidity": 64, "lighting": "良好", "notes": "发现3个过期促销标识"}, {"id": 21, "task_id": "AI-20240113-021", "location": "A区-货架021", "device_name": "AI摄像头-021", "inspection_type": "商品陈列检查", "status": "已完成", "result": "正常", "confidence": 97.8, "detected_objects": ["食用油", "调料", "面粉"], "anomalies": [], "start_time": "2024-01-13 18:30:15", "end_time": "2024-01-13 18:30:32", "duration": 17, "inspector": "AI系统", "images": ["snapshot_021.jpg", "analysis_021.jpg"], "temperature": 22.6, "humidity": 63, "lighting": "良好", "notes": "调料区陈列整齐"}, {"id": 22, "task_id": "AI-20240113-022", "location": "B区-冷柜022", "device_name": "AI摄像头-022", "inspection_type": "冷链监控", "status": "已完成", "result": "正常", "confidence": 95.3, "detected_objects": ["肉类", "海鲜", "冷冻食品"], "anomalies": [], "start_time": "2024-01-13 18:25:40", "end_time": "2024-01-13 18:25:58", "duration": 18, "inspector": "AI系统", "images": ["snapshot_022.jpg", "cold_022.jpg"], "temperature": 2.1, "humidity": 88, "lighting": "正常", "notes": "冷链温度稳定"}, {"id": 23, "task_id": "AI-20240113-023", "location": "C区-货架023", "device_name": "AI摄像头-023", "inspection_type": "库存监控", "status": "异常", "result": "缺货", "confidence": 91.2, "detected_objects": ["空货架"], "anomalies": ["多个商品缺货"], "start_time": "2024-01-13 18:20:10", "end_time": "2024-01-13 18:20:28", "duration": 18, "inspector": "AI系统", "images": ["snapshot_023.jpg", "empty_023.jpg"], "temperature": 23.4, "humidity": 61, "lighting": "良好", "notes": "货架存在多处缺货"}, {"id": 24, "task_id": "AI-20240113-024", "location": "D区-收银台024", "device_name": "AI摄像头-024", "inspection_type": "服务质量监控", "status": "已完成", "result": "服务规范", "confidence": 96.5, "detected_objects": ["收银员", "顾客"], "anomalies": [], "start_time": "2024-01-13 18:15:25", "end_time": "2024-01-13 18:15:42", "duration": 17, "inspector": "AI系统", "images": ["snapshot_024.jpg", "service_024.jpg"], "temperature": 24.3, "humidity": 62, "lighting": "良好", "notes": "收银服务态度良好"}, {"id": 25, "task_id": "AI-20240113-025", "location": "E区-货架025", "device_name": "AI摄像头-025", "inspection_type": "商品摆放检查", "status": "已完成", "result": "摆放混乱", "confidence": 87.9, "detected_objects": ["各类商品"], "anomalies": ["商品分类错误", "摆放位置不当"], "start_time": "2024-01-13 18:10:35", "end_time": "2024-01-13 18:10:53", "duration": 18, "inspector": "AI系统", "images": ["snapshot_025.jpg", "mess_025.jpg"], "temperature": 23.7, "humidity": 59, "lighting": "良好", "notes": "商品摆放需要重新整理"}, {"id": 26, "task_id": "AI-20240113-026", "location": "F区-货架026", "device_name": "AI摄像头-026", "inspection_type": "清洁检查", "status": "异常", "result": "清洁不达标", "confidence": 84.6, "detected_objects": ["货架", "地面", "垃圾"], "anomalies": ["地面有垃圾", "货架有灰尘"], "start_time": "2024-01-13 18:05:20", "end_time": "2024-01-13 18:05:38", "duration": 18, "inspector": "AI系统", "images": ["snapshot_026.jpg", "dirty_026.jpg"], "temperature": 23.2, "humidity": 64, "lighting": "良好", "notes": "清洁状况不佳，需要清理"}, {"id": 27, "task_id": "AI-20240113-027", "location": "G区-货架027", "device_name": "AI摄像头-027", "inspection_type": "安全检查", "status": "已完成", "result": "正常", "confidence": 93.4, "detected_objects": ["货架", "安全标识"], "anomalies": [], "start_time": "2024-01-13 18:00:15", "end_time": "2024-01-13 18:00:32", "duration": 17, "inspector": "AI系统", "images": ["snapshot_027.jpg", "safety_027.jpg"], "temperature": 23.8, "humidity": 60, "lighting": "良好", "notes": "安全状况良好"}, {"id": 28, "task_id": "AI-20240113-028", "location": "H区-货架028", "device_name": "AI摄像头-028", "inspection_type": "温湿度监控", "status": "异常", "result": "湿度过高", "confidence": 89.7, "detected_objects": ["商品", "环境"], "anomalies": ["湿度超标"], "start_time": "2024-01-13 17:55:50", "end_time": "2024-01-13 17:56:08", "duration": 18, "inspector": "AI系统", "images": ["snapshot_028.jpg", "humid_028.jpg"], "temperature": 24.5, "humidity": 78, "lighting": "良好", "notes": "环境湿度过高，需要除湿"}, {"id": 29, "task_id": "AI-20240113-029", "location": "I区-货架029", "device_name": "AI摄像头-029", "inspection_type": "商品质量检查", "status": "已完成", "result": "发现问题", "confidence": 86.1, "detected_objects": ["水果", "包装食品"], "anomalies": ["水果腐烂", "包装破损"], "start_time": "2024-01-13 17:50:25", "end_time": "2024-01-13 17:50:43", "duration": 18, "inspector": "AI系统", "images": ["snapshot_029.jpg", "damage_029.jpg"], "temperature": 20.3, "humidity": 72, "lighting": "良好", "notes": "发现腐烂水果和破损包装"}, {"id": 30, "task_id": "AI-20240113-030", "location": "J区-货架030", "device_name": "AI摄像头-030", "inspection_type": "价格核对", "status": "已完成", "result": "价格错误", "confidence": 92.8, "detected_objects": ["商品", "价格标签"], "anomalies": ["价格标签与系统不符"], "start_time": "2024-01-13 17:45:10", "end_time": "2024-01-13 17:45:28", "duration": 18, "inspector": "AI系统", "images": ["snapshot_030.jpg", "price_030.jpg"], "temperature": 23.6, "humidity": 61, "lighting": "良好", "notes": "发现价格标签错误"}, {"id": 31, "task_id": "AI-20240112-031", "location": "A区-货架031", "device_name": "AI摄像头-031", "inspection_type": "夜间安防检查", "status": "已完成", "result": "正常", "confidence": 94.9, "detected_objects": ["货架", "通道"], "anomalies": [], "start_time": "2024-01-12 22:30:15", "end_time": "2024-01-12 22:30:32", "duration": 17, "inspector": "AI系统", "images": ["snapshot_031.jpg", "night_031.jpg"], "temperature": 21.8, "humidity": 65, "lighting": "夜间模式", "notes": "夜间安防检查正常"}, {"id": 32, "task_id": "AI-20240112-032", "location": "B区-冷柜032", "device_name": "AI摄像头-032", "inspection_type": "夜间温度监控", "status": "已完成", "result": "正常", "confidence": 96.1, "detected_objects": ["冷柜设备"], "anomalies": [], "start_time": "2024-01-12 22:25:40", "end_time": "2024-01-12 22:25:58", "duration": 18, "inspector": "AI系统", "images": ["snapshot_032.jpg", "temp_032.jpg"], "temperature": 3.8, "humidity": 85, "lighting": "夜间模式", "notes": "夜间冷柜温度正常"}, {"id": 33, "task_id": "AI-20240112-033", "location": "C区-货架033", "device_name": "AI摄像头-033", "inspection_type": "库存清点", "status": "已完成", "result": "盘点完成", "confidence": 91.5, "detected_objects": ["各类商品"], "anomalies": [], "start_time": "2024-01-12 20:15:20", "end_time": "2024-01-12 20:18:45", "duration": 205, "inspector": "AI系统", "images": ["snapshot_033.jpg", "count_033.jpg"], "temperature": 22.9, "humidity": 63, "lighting": "良好", "notes": "库存清点工作完成"}, {"id": 34, "task_id": "AI-20240112-034", "location": "D区-收银台034", "device_name": "AI摄像头-034", "inspection_type": "设备状态检查", "status": "异常", "result": "设备故障", "confidence": 88.3, "detected_objects": ["收银设备", "POS机"], "anomalies": ["POS机无响应"], "start_time": "2024-01-12 19:45:15", "end_time": "2024-01-12 19:45:33", "duration": 18, "inspector": "AI系统", "images": ["snapshot_034.jpg", "error_034.jpg"], "temperature": 24.1, "humidity": 60, "lighting": "良好", "notes": "POS机出现故障，需要维修"}, {"id": 35, "task_id": "AI-20240112-035", "location": "E区-货架035", "device_name": "AI摄像头-035", "inspection_type": "顾客行为分析", "status": "已完成", "result": "正常购物", "confidence": 95.7, "detected_objects": ["顾客", "购物车", "商品"], "anomalies": [], "start_time": "2024-01-12 19:30:25", "end_time": "2024-01-12 19:30:42", "duration": 17, "inspector": "AI系统", "images": ["snapshot_035.jpg", "customer_035.jpg"], "temperature": 23.5, "humidity": 62, "lighting": "良好", "notes": "顾客购物行为正常"}, {"id": 36, "task_id": "AI-20240112-036", "location": "F区-货架036", "device_name": "AI摄像头-036", "inspection_type": "补货检查", "status": "已完成", "result": "需要补货", "confidence": 89.4, "detected_objects": ["货架", "少量商品"], "anomalies": ["多个商品库存不足"], "start_time": "2024-01-12 19:15:50", "end_time": "2024-01-12 19:16:08", "duration": 18, "inspector": "AI系统", "images": ["snapshot_036.jpg", "restock_036.jpg"], "temperature": 23.2, "humidity": 64, "lighting": "良好", "notes": "多个商品需要补货"}, {"id": 37, "task_id": "AI-20240112-037", "location": "G区-货架037", "device_name": "AI摄像头-037", "inspection_type": "陈列标准检查", "status": "已完成", "result": "不符合标准", "confidence": 86.8, "detected_objects": ["商品陈列"], "anomalies": ["陈列高度不符", "间距不当"], "start_time": "2024-01-12 19:00:35", "end_time": "2024-01-12 19:00:53", "duration": 18, "inspector": "AI系统", "images": ["snapshot_037.jpg", "display_037.jpg"], "temperature": 23.8, "humidity": 59, "lighting": "良好", "notes": "商品陈列需要调整"}, {"id": 38, "task_id": "AI-20240112-038", "location": "H区-货架038", "device_name": "AI摄像头-038", "inspection_type": "货架稳定性检查", "status": "异常", "result": "货架松动", "confidence": 91.6, "detected_objects": ["货架结构"], "anomalies": ["货架螺栓松动"], "start_time": "2024-01-12 18:45:20", "end_time": "2024-01-12 18:45:38", "duration": 18, "inspector": "AI系统", "images": ["snapshot_038.jpg", "loose_038.jpg"], "temperature": 23.4, "humidity": 61, "lighting": "良好", "notes": "货架螺栓松动，存在安全隐患"}, {"id": 39, "task_id": "AI-20240112-039", "location": "I区-货架039", "device_name": "AI摄像头-039", "inspection_type": "环境光线检查", "status": "已完成", "result": "光线不足", "confidence": 87.2, "detected_objects": ["灯具", "照明区域"], "anomalies": ["照明亮度不够"], "start_time": "2024-01-12 18:30:15", "end_time": "2024-01-12 18:30:32", "duration": 17, "inspector": "AI系统", "images": ["snapshot_039.jpg", "light_039.jpg"], "temperature": 23.6, "humidity": 60, "lighting": "暗", "notes": "照明亮度不足，影响购物体验"}, {"id": 40, "task_id": "AI-20240112-040", "location": "J区-货架040", "device_name": "AI摄像头-040", "inspection_type": "导购标识检查", "status": "已完成", "result": "标识不清", "confidence": 89.1, "detected_objects": ["导购牌", "指示标识"], "anomalies": ["标识褪色", "位置不当"], "start_time": "2024-01-12 18:15:40", "end_time": "2024-01-12 18:15:58", "duration": 18, "inspector": "AI系统", "images": ["snapshot_040.jpg", "sign_040.jpg"], "temperature": 23.9, "humidity": 58, "lighting": "良好", "notes": "导购标识需要更新"}, {"id": 41, "task_id": "AI-20240111-041", "location": "A区-货架041", "device_name": "AI摄像头-041", "inspection_type": "空气质量检测", "status": "已完成", "result": "正常", "confidence": 93.5, "detected_objects": ["环境监测"], "anomalies": [], "start_time": "2024-01-11 16:30:25", "end_time": "2024-01-11 16:30:42", "duration": 17, "inspector": "AI系统", "images": ["snapshot_041.jpg", "air_041.jpg"], "temperature": 22.8, "humidity": 63, "lighting": "良好", "notes": "空气质量正常"}, {"id": 42, "task_id": "AI-20240111-042", "location": "B区-冷柜042", "device_name": "AI摄像头-042", "inspection_type": "制冷效果检查", "status": "已完成", "result": "制冷良好", "confidence": 96.8, "detected_objects": ["冷柜内部"], "anomalies": [], "start_time": "2024-01-11 16:25:10", "end_time": "2024-01-11 16:25:28", "duration": 18, "inspector": "AI系统", "images": ["snapshot_042.jpg", "cooling_042.jpg"], "temperature": 1.9, "humidity": 89, "lighting": "正常", "notes": "制冷系统运行正常"}, {"id": 43, "task_id": "AI-20240111-043", "location": "C区-货架043", "device_name": "AI摄像头-043", "inspection_type": "商品保质期检查", "status": "异常", "result": "发现过期商品", "confidence": 88.9, "detected_objects": ["各类食品"], "anomalies": ["7件商品过期"], "start_time": "2024-01-11 16:20:05", "end_time": "2024-01-11 16:20:23", "duration": 18, "inspector": "AI系统", "images": ["snapshot_043.jpg", "expired_043.jpg"], "temperature": 23.3, "humidity": 62, "lighting": "良好", "notes": "发现7件过期商品，需要下架"}, {"id": 44, "task_id": "AI-20240111-044", "location": "D区-收银台044", "device_name": "AI摄像头-044", "inspection_type": "收银效率分析", "status": "已完成", "result": "效率正常", "confidence": 94.3, "detected_objects": ["收银过程"], "anomalies": [], "start_time": "2024-01-11 16:15:30", "end_time": "2024-01-11 16:15:48", "duration": 18, "inspector": "AI系统", "images": ["snapshot_044.jpg", "efficiency_044.jpg"], "temperature": 24.2, "humidity": 61, "lighting": "良好", "notes": "收银效率符合标准"}, {"id": 45, "task_id": "AI-20240111-045", "location": "E区-货架045", "device_name": "AI摄像头-045", "inspection_type": "防盗标签检查", "status": "已完成", "result": "标签缺失", "confidence": 90.7, "detected_objects": ["商品", "防盗标签"], "anomalies": ["12个商品缺失防盗标签"], "start_time": "2024-01-11 16:10:15", "end_time": "2024-01-11 16:10:33", "duration": 18, "inspector": "AI系统", "images": ["snapshot_045.jpg", "tag_045.jpg"], "temperature": 23.7, "humidity": 60, "lighting": "良好", "notes": "12个商品缺失防盗标签"}, {"id": 46, "task_id": "AI-20240111-046", "location": "F区-货架046", "device_name": "AI摄像头-046", "inspection_type": "商品分类检查", "status": "已完成", "result": "分类错误", "confidence": 86.5, "detected_objects": ["混放商品"], "anomalies": ["商品分类混乱"], "start_time": "2024-01-11 16:05:20", "end_time": "2024-01-11 16:05:38", "duration": 18, "inspector": "AI系统", "images": ["snapshot_046.jpg", "category_046.jpg"], "temperature": 23.1, "humidity": 64, "lighting": "良好", "notes": "商品分类存在错误，需要重新整理"}, {"id": 47, "task_id": "AI-20240111-047", "location": "G区-货架047", "device_name": "AI摄像头-047", "inspection_type": "货损检查", "status": "异常", "result": "发现货损", "confidence": 87.8, "detected_objects": ["破损商品"], "anomalies": ["包装破损", "商品变形"], "start_time": "2024-01-11 16:00:10", "end_time": "2024-01-11 16:00:28", "duration": 18, "inspector": "AI系统", "images": ["snapshot_047.jpg", "damage2_047.jpg"], "temperature": 23.5, "humidity": 59, "lighting": "良好", "notes": "发现多件货损商品"}, {"id": 48, "task_id": "AI-20240111-048", "location": "H区-货架048", "device_name": "AI摄像头-048", "inspection_type": "库存流转检查", "status": "已完成", "result": "流转正常", "confidence": 92.4, "detected_objects": ["商品库存"], "anomalies": [], "start_time": "2024-01-11 15:55:45", "end_time": "2024-01-11 15:56:03", "duration": 18, "inspector": "AI系统", "images": ["snapshot_048.jpg", "turnover_048.jpg"], "temperature": 23.8, "humidity": 61, "lighting": "良好", "notes": "库存流转情况正常"}, {"id": 49, "task_id": "AI-20240111-049", "location": "I区-货架049", "device_name": "AI摄像头-049", "inspection_type": "促销活动检查", "status": "已完成", "result": "活动正常", "confidence": 94.1, "detected_objects": ["促销商品", "活动标识"], "anomalies": [], "start_time": "2024-01-11 15:50:30", "end_time": "2024-01-11 15:50:48", "duration": 18, "inspector": "AI系统", "images": ["snapshot_049.jpg", "promotion_049.jpg"], "temperature": 24.0, "humidity": 58, "lighting": "良好", "notes": "促销活动执行正常"}, {"id": 50, "task_id": "AI-20240111-050", "location": "J区-货架050", "device_name": "AI摄像头-050", "inspection_type": "综合巡检", "status": "已完成", "result": "综合评分良好", "confidence": 91.8, "detected_objects": ["整体环境"], "anomalies": ["轻微问题3处"], "start_time": "2024-01-11 15:45:15", "end_time": "2024-01-11 15:45:55", "duration": 40, "inspector": "AI系统", "images": ["snapshot_050.jpg", "overall_050.jpg"], "temperature": 23.4, "humidity": 62, "lighting": "良好", "notes": "综合巡检完成，整体状况良好"}]}