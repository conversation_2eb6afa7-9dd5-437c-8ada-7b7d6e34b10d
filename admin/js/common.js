// ========================================
// 通用工具库 - common.js
// 提供数据搜索、导出、筛选、分页等通用功能
// ========================================

/**
 * 通用数据管理类
 */
class DataManager {
    constructor(options = {}) {
        this.data = options.data || [];
        this.filteredData = [...this.data];
        this.currentPage = 1;
        this.pageSize = options.pageSize || 10;
        this.searchTerm = '';
        this.filters = {};
        this.sortField = '';
        this.sortOrder = 'asc';

        // 回调函数
        this.onDataChange = options.onDataChange || (() => {});
        this.onPageChange = options.onPageChange || (() => {});
    }

    /**
     * 设置数据
     */
    setData(data) {
        this.data = data || [];
        this.filteredData = [...this.data];
        this.currentPage = 1;
        this.applyFilters();
        return this;
    }

    /**
     * 搜索功能
     */
    search(term, fields = []) {
        this.searchTerm = term;
        this.currentPage = 1;
        this.applyFilters();
        return this;
    }

    /**
     * 筛选功能
     */
    filter(filters) {
        this.filters = { ...this.filters, ...filters };
        this.currentPage = 1;
        this.applyFilters();
        return this;
    }

    /**
     * 清除筛选
     */
    clearFilters() {
        this.filters = {};
        this.searchTerm = '';
        this.currentPage = 1;
        this.applyFilters();
        return this;
    }

    /**
     * 排序功能
     */
    sort(field, order = 'asc') {
        this.sortField = field;
        this.sortOrder = order;
        this.applyFilters();
        return this;
    }

    /**
     * 分页功能
     */
    setPage(page) {
        const totalPages = this.getTotalPages();
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.onPageChange(this.getPageData());
        }
        return this;
    }

    /**
     * 设置每页条数
     */
    setPageSize(size) {
        this.pageSize = size;
        this.currentPage = 1;
        this.applyFilters();
        return this;
    }

    /**
     * 应用所有筛选条件
     */
    applyFilters() {
        let result = [...this.data];

        // 应用搜索
        if (this.searchTerm) {
            const term = this.searchTerm.toLowerCase();
            result = result.filter(item => {
                return Object.values(item).some(value =>
                    String(value).toLowerCase().includes(term)
                );
            });
        }

        // 应用筛选条件
        Object.entries(this.filters).forEach(([field, value]) => {
            if (value !== '' && value !== null && value !== undefined) {
                result = result.filter(item => {
                    if (Array.isArray(value)) {
                        return value.includes(item[field]);
                    }
                    return String(item[field]).toLowerCase().includes(String(value).toLowerCase());
                });
            }
        });

        // 应用排序
        if (this.sortField) {
            result.sort((a, b) => {
                const aVal = a[this.sortField];
                const bVal = b[this.sortField];

                if (typeof aVal === 'number' && typeof bVal === 'number') {
                    return this.sortOrder === 'asc' ? aVal - bVal : bVal - aVal;
                }

                const aStr = String(aVal).toLowerCase();
                const bStr = String(bVal).toLowerCase();

                if (this.sortOrder === 'asc') {
                    return aStr.localeCompare(bStr);
        } else {
                    return bStr.localeCompare(aStr);
                }
            });
            }

        this.filteredData = result;
        this.onDataChange(this.getPageData());
        return this;
    }

    /**
     * 获取当前页数据
     */
    getPageData() {
        const start = (this.currentPage - 1) * this.pageSize;
        const end = start + this.pageSize;
        return {
            data: this.filteredData.slice(start, end),
            currentPage: this.currentPage,
            pageSize: this.pageSize,
            total: this.filteredData.length,
            totalPages: this.getTotalPages()
        };
    }

    /**
     * 获取总页数
     */
    getTotalPages() {
        return Math.ceil(this.filteredData.length / this.pageSize);
    }

    /**
     * 获取所有筛选后的数据
     */
    getAllData() {
        return this.filteredData;
    }
}

/**
 * 数据导出工具类
 */
class DataExporter {
    /**
     * 导出为CSV
     */
    static exportToCSV(data, filename = 'data.csv', columns = null) {
        if (!data || data.length === 0) {
            CommonUtils.showToast('没有数据可导出', 'warning');
            return;
        }

        // 确定列
        const headers = columns || Object.keys(data[0]);

        // 构建CSV内容
        let csvContent = '\uFEFF'; // BOM for UTF-8

        // 添加表头
        csvContent += headers.join(',') + '\n';

        // 添加数据行
        data.forEach(row => {
            const values = headers.map(header => {
                let value = row[header] || '';
                // 处理包含逗号或引号的值
                if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
                    value = '"' + value.replace(/"/g, '""') + '"';
                }
                return value;
            });
            csvContent += values.join(',') + '\n';
        });

        // 创建下载链接
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        CommonUtils.showToast('数据导出成功', 'success');
    }

    /**
     * 导出为JSON
     */
    static exportToJSON(data, filename = 'data.json') {
        if (!data || data.length === 0) {
            CommonUtils.showToast('没有数据可导出', 'warning');
            return;
        }

        const jsonContent = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonContent], { type: 'application/json' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        CommonUtils.showToast('数据导出成功', 'success');
    }
    }

/**
 * 分页组件类
 */
class PaginationComponent {
    constructor(container, dataManager) {
        this.container = typeof container === 'string' ? document.getElementById(container) : container;
        this.dataManager = dataManager;
        this.render();
    }

    render() {
        if (!this.container) return;

        const pageData = this.dataManager.getPageData();
        const { currentPage, totalPages, total } = pageData;

        let html = `
            <div class="pagination-wrapper">
            <div class="pagination-info">
                    共 ${total} 条记录，第 ${currentPage} / ${totalPages} 页
            </div>
            <div class="pagination-controls">
                    <button class="pagination-btn" ${currentPage <= 1 ? 'disabled' : ''}
                            onclick="this.paginationComponent.goToPage(1)">首页</button>
                    <button class="pagination-btn" ${currentPage <= 1 ? 'disabled' : ''}
                            onclick="this.paginationComponent.goToPage(${currentPage - 1})">上一页</button>
        `;

        // 页码按钮
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            html += `
                <button class="pagination-btn ${i === currentPage ? 'active' : ''}"
                        onclick="this.paginationComponent.goToPage(${i})">${i}</button>
            `;
        }

        html += `
                    <button class="pagination-btn" ${currentPage >= totalPages ? 'disabled' : ''}
                            onclick="this.paginationComponent.goToPage(${currentPage + 1})">下一页</button>
                    <button class="pagination-btn" ${currentPage >= totalPages ? 'disabled' : ''}
                            onclick="this.paginationComponent.goToPage(${totalPages})">末页</button>
            </div>
                <div class="pagination-size">
                    <select onchange="this.paginationComponent.changePageSize(this.value)">
                        <option value="10" ${this.dataManager.pageSize === 10 ? 'selected' : ''}>10条/页</option>
                        <option value="20" ${this.dataManager.pageSize === 20 ? 'selected' : ''}>20条/页</option>
                        <option value="50" ${this.dataManager.pageSize === 50 ? 'selected' : ''}>50条/页</option>
                        <option value="100" ${this.dataManager.pageSize === 100 ? 'selected' : ''}>100条/页</option>
                </select>
                </div>
            </div>
        `;

        this.container.innerHTML = html;

        // 绑定组件引用到按钮
        this.container.querySelectorAll('button, select').forEach(element => {
            element.paginationComponent = this;
        });
    }

    goToPage(page) {
        this.dataManager.setPage(page);
        this.render();
    }

    changePageSize(size) {
        this.dataManager.setPageSize(parseInt(size));
        this.render();
    }
}

/**
 * 模态框组件类
 */
class ModalComponent {
    constructor() {
        this.modalElement = null;
        this.createModal();
    }

    createModal() {
        // 如果已存在模态框，先移除
        const existingModal = document.getElementById('commonModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 创建模态框HTML
        const modalHTML = `
            <div id="commonModal" class="modal-overlay" style="display: none;">
                <div class="modal-container">
                    <div class="modal-header">
                        <h3 class="modal-title"></h3>
                        <button class="modal-close" onclick="CommonUtils.closeModal()">×</button>
                    </div>
                    <div class="modal-body"></div>
                    <div class="modal-footer"></div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.modalElement = document.getElementById('commonModal');

        // 绑定点击背景关闭事件
        this.modalElement.addEventListener('click', (e) => {
            if (e.target === this.modalElement) {
                this.close();
            }
        });
    }

    show(options = {}) {
        const { title = '', content = '', footer = '', width = '600px' } = options;

        this.modalElement.querySelector('.modal-title').textContent = title;
        this.modalElement.querySelector('.modal-body').innerHTML = content;
        this.modalElement.querySelector('.modal-footer').innerHTML = footer;
        this.modalElement.querySelector('.modal-container').style.width = width;

        this.modalElement.style.display = 'flex';
        document.body.style.overflow = 'hidden';

        return this;
    }

    close() {
        this.modalElement.style.display = 'none';
        document.body.style.overflow = '';
        return this;
    }
}

/**
 * 通用工具类
 */
class CommonUtils {
    static modal = new ModalComponent();

    /**
     * 显示提示消息
     */
    static showToast(message, type = 'info', duration = 3000) {
        // 移除已存在的toast
        const existingToast = document.querySelector('.toast');
        if (existingToast) {
            existingToast.remove();
        }

        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;

        // 样式
        Object.assign(toast.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 24px',
            borderRadius: '6px',
            color: '#fff',
            fontSize: '14px',
            fontWeight: '500',
            zIndex: '9999',
            opacity: '0',
            transform: 'translateX(100%)',
            transition: 'all 0.3s ease'
        });

        // 根据类型设置背景色
        const colors = {
            success: '#00a870',
            warning: '#ed7b2f',
            error: '#d54941',
            info: '#0052d9'
        };
        toast.style.backgroundColor = colors[type] || colors.info;

        document.body.appendChild(toast);

        // 显示动画
        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateX(0)';
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, duration);
    }

    /**
     * 显示模态框
     */
    static showModal(options) {
        return this.modal.show(options);
    }

    /**
     * 关闭模态框
     */
    static closeModal() {
        return this.modal.close();
    }

    /**
     * 确认对话框
     */
    static confirm(message, callback) {
        const footer = `
            <button class="btn btn-secondary" onclick="CommonUtils.closeModal()">取消</button>
            <button class="btn btn-primary" onclick="CommonUtils.handleConfirm(${callback})">确定</button>
        `;

        this.showModal({
            title: '确认操作',
            content: `<p>${message}</p>`,
            footer: footer
        });

        // 存储回调函数
        this._confirmCallback = callback;
    }

    /**
     * 处理确认回调
     */
    static handleConfirm() {
        if (typeof this._confirmCallback === 'function') {
            this._confirmCallback();
    }
        this.closeModal();
    }

    /**
     * 格式化日期
     */
    static formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
        if (!date) return '';

        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');

        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    }

    /**
     * 生成唯一ID
     */
    static generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
        }

    /**
     * 防抖函数
     */
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 节流函数
     */
    static throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    }

    /**
     * Ajax请求工具
     */
    static ajax(options) {
        const defaults = {
            method: 'GET',
            url: '',
            data: null,
            headers: {},
            timeout: 10000
        };

        const config = { ...defaults, ...options };

        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();

            xhr.open(config.method, config.url, true);
            xhr.timeout = config.timeout;

            // 设置请求头
            Object.entries(config.headers).forEach(([key, value]) => {
                xhr.setRequestHeader(key, value);
            });

            xhr.onload = function() {
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (e) {
                        resolve(xhr.responseText);
                    }
                } else {
                    reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                }
            };

            xhr.onerror = () => reject(new Error('Network Error'));
            xhr.ontimeout = () => reject(new Error('Request Timeout'));

            // 发送请求
            if (config.data && config.method !== 'GET') {
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.send(JSON.stringify(config.data));
            } else {
                xhr.send();
            }
        });
    }
}

// 全局暴露
window.DataManager = DataManager;
window.DataExporter = DataExporter;
window.PaginationComponent = PaginationComponent;
window.ModalComponent = ModalComponent;
window.CommonUtils = CommonUtils;