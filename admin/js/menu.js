// 后台管理系统菜单配置
const MENU_TITLE = '基于零售站点前置仓的巡检系统';
const MENU_CONFIG = [
    {title: '仪表盘', url: './home.html'},
    {
        title: '智能巡检管理',
        children: [
            {title: 'AI视觉识别巡检', url: './ai_inspection.html'},
            {title: '自动化巡检流程', url: './auto_inspection.html'},
            {title: '多终端协同操作', url: './multi_terminal.html'},
            {title: '异常闭环管理', url: './exception_management.html'}
        ]
    },
    {
        title: '实时监控与预警',
        children: [
            {title: '多维度实时监控', url: './realtime_monitoring.html'},
            {title: '可视化看板与预警', url: './visual_dashboard.html'}
        ]
    },
    {
        title: '数据采集与分析',
        children: [
            {title: '全要素数据采集', url: './data_collection.html'},
            {title: '智能数据预处理', url: './data_preprocessing.html'},
            {title: '分析模型与决策支持', url: './analysis_model.html'}
        ]
    },
    {
        title: '硬件与技术支持',
        children: [
            {title: '边缘计算与通信', url: './edge_computing.html'},
            {title: '传感器与终端设备', url: './sensor_device.html'},
            {title: '软件平台架构', url: './platform_architecture.html'}
        ]
    },
    {
        title: '实施与运维支持',
        children: [
            {title: '部署前准备', url: './deployment_preparation.html'},
            {title: '系统集成调试', url: './system_integration.html'}
        ]
    },
    {
        title: '未来扩展功能',
        children: [
            {title: '技术升级', url: './technology_upgrade.html'},
            {title: '功能迭代', url: './function_iteration.html'}
        ]
    },
    {
        title: '行业适配与合规',
        children: [
            {title: '多场景应用', url: './multi_scenario.html'},
            {title: '合规与安全保障', url: './compliance_security.html'}
        ]
    }
];

// 菜单渲染类
class MenuRenderer {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.init();
    }

    init() {
        this.render();
        this.bindEvents();
    }

    render() {
        const menuHtml = this.generateMenuHtml();
        this.container.innerHTML = menuHtml;
    }

    generateMenuHtml() {
        let html = '<ul class="menu-list">';

        MENU_CONFIG.forEach((category, index) => {
            // 如果是单个菜单项（没有children），直接渲染
            if (!category.children) {
                html += `
                    <li class="menu-item">
                        <a href="${category.url}" class="menu-link">${category.title}</a>
                    </li>
                `;
                return;
            }

            // 如果有子菜单，渲染分类
            html += `
                <li class="menu-category">
                    <div class="category-header" data-category="${index}">
                        <span class="category-title">${category.title}</span>
                        <span class="expand-icon">+</span>
                    </div>
                    <ul class="sub-menu" style="display: none;">
            `;

            category.children.forEach(item => {
                html += `
                    <li class="menu-item">
                        <a href="${item.url}" class="menu-link">${item.title}</a>
                    </li>
                `;
            });

            html += `
                    </ul>
                </li>
            `;
        });

        html += '</ul>';
        return html;
    }

    bindEvents() {
        // 绑定分类展开/收起事件
        this.container.addEventListener('click', (e) => {
            if (e.target.closest('.category-header')) {
                const header = e.target.closest('.category-header');
                const subMenu = header.nextElementSibling;
                const expandIcon = header.querySelector('.expand-icon');

                if (subMenu.style.display === 'none') {
                    subMenu.style.display = 'block';
                    expandIcon.textContent = '-';
                    header.classList.add('expanded');
                } else {
                    subMenu.style.display = 'none';
                    expandIcon.textContent = '+';
                    header.classList.remove('expanded');
                }
            }
        });

        // 绑定菜单项点击事件
        this.container.addEventListener('click', (e) => {
            if (e.target.classList.contains('menu-link')) {
                e.preventDefault();
                const url = e.target.getAttribute('href');
                // 直接跳转到对应页面
                if (url && url !== '#') {
                    window.location.href = url;
                }
            }
        });
    }

    // 设置当前激活的菜单项
    setActiveMenu(url) {
        const menuLinks = this.container.querySelectorAll('.menu-link');
        menuLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === url) {
                link.classList.add('active');
                // 展开包含当前页面的分类
                const subMenu = link.closest('.sub-menu');
                if (subMenu) {
                    const header = subMenu.previousElementSibling;
                    subMenu.style.display = 'block';
                    header.querySelector('.expand-icon').textContent = '-';
                    header.classList.add('expanded');
                }
            }
        });
    }
}

// 导出菜单配置和渲染器类
window.MENU_TITLE = MENU_TITLE;
window.MENU_CONFIG = MENU_CONFIG;
window.MenuRenderer = MenuRenderer;

// 获取所有页面文件名
function getAllPageFiles() {
    const pages = [];
    MENU_CONFIG.forEach(category => {
        if (category.children) {
            category.children.forEach(item => {
                pages.push(item.url.replace('./', ''));
            });
        } else {
            pages.push(category.url.replace('./', ''));
        }
    });
    return pages;
}

window.getAllPageFiles = getAllPageFiles;

// 更新页面标题函数
function updatePageTitle() {
    // 更新侧边栏标题
    const sidebarHeader = document.querySelector('.sidebar-header h1');
    if (sidebarHeader) {
        sidebarHeader.innerHTML = `
            <span class="logo-icon">🏪</span>
            ${MENU_TITLE}
        `;
    }

    // 更新页面title标签
    const titleElement = document.querySelector('title');
    if (titleElement) {
        const currentTitle = titleElement.textContent;
        const pageName = currentTitle.split(' - ')[0];
        titleElement.textContent = `${pageName} - ${MENU_TITLE}`;
    }
}

window.updatePageTitle = updatePageTitle;

// 页面加载完成后初始化菜单
document.addEventListener('DOMContentLoaded', function () {
    // 更新页面标题
    updatePageTitle();

    // 检查是否有sidebarNav或menuContainer元素
    const menuContainer = document.getElementById('sidebarNav') || document.getElementById('menuContainer');
    if (menuContainer) {
        const menuRenderer = new MenuRenderer(menuContainer.id);

        // 根据当前页面设置激活状态
        const currentPage = window.location.pathname.split('/').pop();
        if (currentPage) {
            menuRenderer.setActiveMenu('./' + currentPage);
        }
    }
});