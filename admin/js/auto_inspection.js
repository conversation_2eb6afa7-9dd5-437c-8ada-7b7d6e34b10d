// 自动化巡检流程页面JavaScript

// 数据管理器
class DataManager {
    constructor() {
        this.data = [];
        this.filteredData = [];
        this.currentPage = 1;
        this.pageSize = 10;
        this.sortField = '';
        this.sortOrder = 'asc';
    }

    async loadData() {
        try {
            const response = await fetch('../data/auto_inspection.json');
            if (!response.ok) {
                throw new Error('Failed to load data');
            }
            this.data = await response.json();
            this.filteredData = [...this.data];
            this.updateStats();
            this.renderTable();
            this.renderPagination();
        } catch (error) {
            console.error('Error loading data:', error);
            this.showError('加载数据失败，请刷新页面重试');
        }
    }

    updateStats() {
        const total = this.data.length;
        const running = this.data.filter(item => item.status === '进行中').length;
        const completed = this.data.filter(item => item.status === '已完成').length;
        const avgEfficiency = this.data.reduce((sum, item) => sum + parseFloat(item.efficiency), 0) / total;

        document.getElementById('totalFlows').textContent = total;
        document.getElementById('runningFlows').textContent = running;
        document.getElementById('completedFlows').textContent = completed;
        document.getElementById('avgEfficiency').textContent = avgEfficiency.toFixed(1) + '%';
    }

    filter(filters) {
        this.filteredData = this.data.filter(item => {
            return Object.keys(filters).every(key => {
                if (!filters[key]) return true;
                return item[key] === filters[key];
            });
        });
        this.currentPage = 1;
        this.renderTable();
        this.renderPagination();
    }

    search(query) {
        if (!query) {
            this.filteredData = [...this.data];
        } else {
            this.filteredData = this.data.filter(item =>
                item.flow_id.toLowerCase().includes(query.toLowerCase()) ||
                item.flow_name.toLowerCase().includes(query.toLowerCase()) ||
                item.location.toLowerCase().includes(query.toLowerCase())
            );
        }
        this.currentPage = 1;
        this.renderTable();
        this.renderPagination();
    }

    sort(field) {
        if (this.sortField === field) {
            this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortField = field;
            this.sortOrder = 'asc';
        }

        this.filteredData.sort((a, b) => {
            let aVal = a[field];
            let bVal = b[field];

            if (typeof aVal === 'string') {
                aVal = aVal.toLowerCase();
                bVal = bVal.toLowerCase();
            }

            if (aVal < bVal) return this.sortOrder === 'asc' ? -1 : 1;
            if (aVal > bVal) return this.sortOrder === 'asc' ? 1 : -1;
            return 0;
        });

        this.renderTable();
        this.updateSortIndicators();
    }

    updateSortIndicators() {
        document.querySelectorAll('.sort-indicator').forEach(indicator => {
            indicator.textContent = '';
        });

        const currentHeader = document.querySelector(`th[onclick="sortTable('${this.sortField}')"] .sort-indicator`);
        if (currentHeader) {
            currentHeader.textContent = this.sortOrder === 'asc' ? '↑' : '↓';
        }
    }

    renderTable() {
        const tbody = document.getElementById('tableBody');
        const start = (this.currentPage - 1) * this.pageSize;
        const end = start + this.pageSize;
        const pageData = this.filteredData.slice(start, end);

        if (pageData.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="empty-state">
                        <div class="empty-state-icon">🔄</div>
                        <div class="empty-state-text">暂无数据</div>
                        <div class="empty-state-desc">没有找到符合条件的巡检流程</div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = pageData.map(item => `
            <tr>
                <td>${item.flow_id}</td>
                <td>${item.flow_name}</td>
                <td>${item.location}</td>
                <td><span class="status-badge ${item.status === '进行中' ? 'running' : item.status === '已完成' ? 'completed' : item.status === '已暂停' ? 'paused' : 'pending'}">${item.status}</span></td>
                <td>
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${item.progress}%"></div>
                        </div>
                        <span class="progress-text">${item.progress}%</span>
                    </div>
                </td>
                <td>${item.efficiency}%</td>
                <td>${item.start_time}</td>
                <td>${item.estimated_duration}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-action view" onclick="viewFlow('${item.flow_id}')">查看</button>
                        ${item.status === '进行中' ? `<button class="btn-action pause" onclick="pauseFlow('${item.flow_id}')">暂停</button>` : ''}
                        ${item.status === '已暂停' ? `<button class="btn-action resume" onclick="resumeFlow('${item.flow_id}')">恢复</button>` : ''}
                        ${item.status === '待执行' ? `<button class="btn-action start" onclick="startFlow('${item.flow_id}')">启动</button>` : ''}
                    </div>
                </td>
            </tr>
        `).join('');
    }

    renderPagination() {
        const container = document.getElementById('paginationContainer');
        const totalPages = Math.ceil(this.filteredData.length / this.pageSize);

        if (totalPages <= 1) {
            container.innerHTML = '';
            return;
        }

        let html = '<div class="pagination">';

        // 上一页
        html += `<button class="page-btn" ${this.currentPage === 1 ? 'disabled' : ''} onclick="changePage(${this.currentPage - 1})">上一页</button>`;

        // 页码
        for (let i = 1; i <= totalPages; i++) {
            if (i === this.currentPage) {
                html += `<button class="page-btn active">${i}</button>`;
            } else if (i === 1 || i === totalPages || Math.abs(i - this.currentPage) <= 2) {
                html += `<button class="page-btn" onclick="changePage(${i})">${i}</button>`;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                html += `<span class="page-ellipsis">...</span>`;
            }
        }

        // 下一页
        html += `<button class="page-btn" ${this.currentPage === totalPages ? 'disabled' : ''} onclick="changePage(${this.currentPage + 1})">下一页</button>`;

        html += '</div>';
        container.innerHTML = html;
    }

    changePage(page) {
        this.currentPage = page;
        this.renderTable();
        this.renderPagination();
    }

    showError(message) {
        showToast(message, 'error');
    }
}

// 全局变量
let dataManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    dataManager = new DataManager();
    dataManager.loadData();

    // 绑定事件监听器
    bindEventListeners();
});

// 绑定事件监听器
function bindEventListeners() {
    // 搜索框
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(function() {
            dataManager.search(this.value);
        }, 300));
    }

    // 筛选器
    const filters = ['statusFilter', 'locationFilter'];
    filters.forEach(filterId => {
        const filterElement = document.getElementById(filterId);
        if (filterElement) {
            filterElement.addEventListener('change', handleFilter);
        }
    });
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 处理筛选
function handleFilter() {
    const filters = {
        status: document.getElementById('statusFilter')?.value || '',
        location: document.getElementById('locationFilter')?.value || ''
    };
    dataManager.filter(filters);
}

// 排序表格
function sortTable(field) {
    dataManager.sort(field);
}

// 切换页面
function changePage(page) {
    dataManager.changePage(page);
}

// 刷新数据
function refreshData() {
    dataManager.loadData();
    showToast('数据已刷新', 'success');
}

// 导出数据
function exportData() {
    const csvContent = convertToCSV(dataManager.filteredData);
    downloadCSV(csvContent, 'auto_inspection.csv');
    showToast('数据导出成功', 'success');
}

// 转换为CSV格式
function convertToCSV(data) {
    const headers = ['流程编号', '流程名称', '位置', '状态', '进度', '效率', '开始时间', '预计时长'];
    const rows = data.map(item => [
        item.flow_id,
        item.flow_name,
        item.location,
        item.status,
        item.progress + '%',
        item.efficiency + '%',
        item.start_time,
        item.estimated_duration
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
}

// 下载CSV文件
function downloadCSV(content, filename) {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 显示新建流程模态框
function showNewFlowModal() {
    const modal = document.getElementById('newFlowModal');
    if (modal) {
        modal.style.display = 'block';
    }
}

// 查看流程详情
function viewFlow(flowId) {
    const flow = dataManager.data.find(item => item.flow_id === flowId);
    if (!flow) return;

    const modal = document.getElementById('viewFlowModal');
    if (modal) {
        // 填充详情数据
        document.getElementById('viewFlowId').textContent = flow.flow_id;
        document.getElementById('viewFlowName').textContent = flow.flow_name;
        document.getElementById('viewLocation').textContent = flow.location;
        document.getElementById('viewStatus').innerHTML = `<span class="status-badge ${flow.status === '进行中' ? 'running' : flow.status === '已完成' ? 'completed' : flow.status === '已暂停' ? 'paused' : 'pending'}">${flow.status}</span>`;
        document.getElementById('viewProgress').textContent = flow.progress + '%';
        document.getElementById('viewEfficiency').textContent = flow.efficiency + '%';
        document.getElementById('viewStartTime').textContent = flow.start_time;
        document.getElementById('viewDuration').textContent = flow.estimated_duration;

        modal.style.display = 'block';
    }
}

// 启动流程
function startFlow(flowId) {
    if (confirm('确定要启动此巡检流程吗？')) {
        const flow = dataManager.data.find(item => item.flow_id === flowId);
        if (flow) {
            flow.status = '进行中';
            flow.start_time = new Date().toLocaleString();
            dataManager.updateStats();
            dataManager.renderTable();
            showToast('流程已启动', 'success');
        }
    }
}

// 暂停流程
function pauseFlow(flowId) {
    if (confirm('确定要暂停此巡检流程吗？')) {
        const flow = dataManager.data.find(item => item.flow_id === flowId);
        if (flow) {
            flow.status = '已暂停';
            dataManager.updateStats();
            dataManager.renderTable();
            showToast('流程已暂停', 'warning');
        }
    }
}

// 恢复流程
function resumeFlow(flowId) {
    if (confirm('确定要恢复此巡检流程吗？')) {
        const flow = dataManager.data.find(item => item.flow_id === flowId);
        if (flow) {
            flow.status = '进行中';
            dataManager.updateStats();
            dataManager.renderTable();
            showToast('流程已恢复', 'success');
        }
    }
}

// 关闭模态框
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
    }
}

// 提交新建流程
function submitNewFlow() {
    const formData = new FormData(document.getElementById('newFlowForm'));
    const data = Object.fromEntries(formData);

    // 模拟提交操作
    console.log('提交新流程:', data);
    closeModal('newFlowModal');
    showToast('流程已创建', 'success');
}

// 显示提示消息
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;

    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 24px;
        border-radius: 4px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        animation: slideInRight 0.3s ease;
    `;

    switch (type) {
        case 'success':
            toast.style.backgroundColor = '#52c41a';
            break;
        case 'error':
            toast.style.backgroundColor = '#ff4d4f';
            break;
        case 'warning':
            toast.style.backgroundColor = '#faad14';
            break;
        default:
            toast.style.backgroundColor = '#1890ff';
    }

    document.body.appendChild(toast);

    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// 点击模态框外部关闭
window.onclick = function(event) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
};