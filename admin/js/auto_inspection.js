/**
 * 自动化巡检流程页面脚本
 */

// 数据管理类
class DataManager {
    constructor() {
        this.data = [];
        this.filteredData = [];
        this.currentPage = 1;
        this.pageSize = 10;
        this.sortField = 'id';
        this.sortDirection = 'asc';
        this.searchTerm = '';
        this.statusFilter = '';
        this.typeFilter = '';
    }

    // 加载数据
    async loadData() {
        try {
            const response = await fetch('../data/auto_inspection.json');
            if (!response.ok) {
                throw new Error('数据加载失败');
            }
            this.data = await response.json();
            this.applyFilters();
            this.updateStatistics();
            return true;
        } catch (error) {
            console.error('数据加载错误:', error);
            return false;
        }
    }

    // 应用过滤器和排序
    applyFilters() {
        // 应用搜索和筛选
        this.filteredData = this.data.filter(item => {
            const matchSearch = this.searchTerm === '' ||
                item.id.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                item.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                item.location.toLowerCase().includes(this.searchTerm.toLowerCase());

            const matchStatus = this.statusFilter === '' || item.status === this.statusFilter;
            const matchType = this.typeFilter === '' || item.type === this.typeFilter;

            return matchSearch && matchStatus && matchType;
        });

        // 应用排序
        this.filteredData.sort((a, b) => {
            let valA = a[this.sortField];
            let valB = b[this.sortField];

            // 处理数字类型的字段
            if (['duration', 'anomaly_count', 'route_efficiency'].includes(this.sortField)) {
                valA = parseFloat(valA);
                valB = parseFloat(valB);
            }

            if (valA < valB) {
                return this.sortDirection === 'asc' ? -1 : 1;
            }
            if (valA > valB) {
                return this.sortDirection === 'asc' ? 1 : -1;
            }
            return 0;
        });
    }

    // 获取当前页数据
    getCurrentPageData() {
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        return this.filteredData.slice(startIndex, endIndex);
    }

    // 获取总页数
    getTotalPages() {
        return Math.ceil(this.filteredData.length / this.pageSize);
    }

    // 更新统计信息
    updateStatistics() {
        // 总巡检数量
        document.getElementById('totalInspections').textContent = this.data.length;

        // 已完成巡检
        const completedCount = this.data.filter(item => item.status === '已完成').length;
        document.getElementById('completedInspections').textContent = completedCount;

        // 异常总数
        const anomalyCount = this.data.reduce((total, item) => total + parseInt(item.anomaly_count || 0), 0);
        document.getElementById('anomalyCount').textContent = anomalyCount;

        // 平均效率
        const efficiencyValues = this.data
            .filter(item => item.route_efficiency)
            .map(item => parseFloat(item.route_efficiency));

        const avgEfficiency = efficiencyValues.length > 0
            ? Math.round(efficiencyValues.reduce((sum, val) => sum + val, 0) / efficiencyValues.length)
            : 0;

        document.getElementById('avgEfficiency').textContent = avgEfficiency + '%';
    }

    // 搜索
    search(term) {
        this.searchTerm = term;
        this.currentPage = 1;
        this.applyFilters();
    }

    // 筛选
    filter(statusValue, typeValue) {
        this.statusFilter = statusValue;
        this.typeFilter = typeValue;
        this.currentPage = 1;
        this.applyFilters();
    }

    // 排序
    sort(field) {
        if (this.sortField === field) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortField = field;
            this.sortDirection = 'asc';
        }
        this.applyFilters();
    }

    // 分页
    goToPage(page) {
        if (page >= 1 && page <= this.getTotalPages()) {
            this.currentPage = page;
        }
    }

    // 设置每页显示数量
    setPageSize(size) {
        this.pageSize = size;
        this.currentPage = 1;
        this.applyFilters();
    }

    // 获取巡检详情
    getInspectionById(id) {
        return this.data.find(item => item.id === id);
    }

    // 添加新巡检
    addInspection(inspection) {
        // 生成新的ID
        const lastId = this.data.length > 0
            ? parseInt(this.data[this.data.length - 1].id.split('-')[1])
            : 0;

        inspection.id = `INSP-${(lastId + 1).toString().padStart(3, '0')}`;

        // 添加到数据集
        this.data.push(inspection);
        this.applyFilters();
        this.updateStatistics();
    }

    // 更新巡检
    updateInspection(id, updatedData) {
        const index = this.data.findIndex(item => item.id === id);
        if (index !== -1) {
            this.data[index] = { ...this.data[index], ...updatedData };
            this.applyFilters();
            this.updateStatistics();
            return true;
        }
        return false;
    }

    // 删除巡检
    deleteInspection(id) {
        const index = this.data.findIndex(item => item.id === id);
        if (index !== -1) {
            this.data.splice(index, 1);
            this.applyFilters();
            this.updateStatistics();
            return true;
        }
        return false;
    }
}

// 分页组件
class PaginationComponent {
    constructor(dataManager, containerId, onPageChange) {
        this.dataManager = dataManager;
        this.container = document.getElementById(containerId);
        this.onPageChange = onPageChange;
    }

    render() {
        const totalPages = this.dataManager.getTotalPages();
        const currentPage = this.dataManager.currentPage;
        const totalItems = this.dataManager.filteredData.length;
        const pageSize = this.dataManager.pageSize;

        // 计算显示的项目范围
        const startItem = Math.min((currentPage - 1) * pageSize + 1, totalItems);
        const endItem = Math.min(currentPage * pageSize, totalItems);

        let html = `
            <div class="pagination-info">
                显示 ${startItem} 到 ${endItem}，共 ${totalItems} 条
            </div>
            <div class="pagination-controls">
        `;

        // 上一页按钮
        html += `
            <button class="pagination-btn"
                ${currentPage === 1 ? 'disabled' : ''}
                onclick="paginationComponent.goToPage(${currentPage - 1})">
                &laquo;
            </button>
        `;

        // 页码按钮
        const maxPageButtons = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
        let endPage = Math.min(totalPages, startPage + maxPageButtons - 1);

        if (endPage - startPage + 1 < maxPageButtons) {
            startPage = Math.max(1, endPage - maxPageButtons + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            html += `
                <button class="pagination-btn ${i === currentPage ? 'active' : ''}"
                    onclick="paginationComponent.goToPage(${i})">
                    ${i}
                </button>
            `;
        }

        // 下一页按钮
        html += `
            <button class="pagination-btn"
                ${currentPage === totalPages || totalPages === 0 ? 'disabled' : ''}
                onclick="paginationComponent.goToPage(${currentPage + 1})">
                &raquo;
            </button>
        `;

        // 每页显示数量选择器
        html += `
            <div class="pagination-size">
                <select onchange="paginationComponent.setPageSize(this.value)">
                    <option value="10" ${pageSize === 10 ? 'selected' : ''}>10条/页</option>
                    <option value="20" ${pageSize === 20 ? 'selected' : ''}>20条/页</option>
                    <option value="50" ${pageSize === 50 ? 'selected' : ''}>50条/页</option>
                </select>
            </div>
        `;

        html += `</div>`;

        this.container.innerHTML = html;
    }

    goToPage(page) {
        this.dataManager.goToPage(page);
        if (this.onPageChange) {
            this.onPageChange();
        }
    }

    setPageSize(size) {
        this.dataManager.setPageSize(parseInt(size));
        if (this.onPageChange) {
            this.onPageChange();
        }
    }
}

// 表格组件
class TableComponent {
    constructor(dataManager, tableBodyId) {
        this.dataManager = dataManager;
        this.tableBody = document.getElementById(tableBodyId);
    }

    render() {
        const data = this.dataManager.getCurrentPageData();
        let html = '';

        if (data.length === 0) {
            html = `<tr><td colspan="10" style="text-align: center;">没有找到匹配的数据</td></tr>`;
        } else {
            data.forEach(item => {
                // 状态样式
                let statusClass = '';
                switch (item.status) {
                    case '已完成': statusClass = 'completed'; break;
                    case '进行中': statusClass = 'processing'; break;
                    case '计划中': statusClass = 'planned'; break;
                    default: statusClass = '';
                }

                // 效率等级
                let efficiencyClass = '';
                const efficiency = parseFloat(item.route_efficiency);
                if (efficiency >= 90) {
                    efficiencyClass = 'efficiency-high';
                } else if (efficiency >= 70) {
                    efficiencyClass = 'efficiency-medium';
                } else {
                    efficiencyClass = 'efficiency-low';
                }

                // 异常数样式
                const anomalyClass = parseInt(item.anomaly_count) === 0 ? 'zero' : '';

                html += `
                    <tr>
                        <td>${item.id}</td>
                        <td>${item.name}</td>
                        <td>${item.type}</td>
                        <td><span class="status-badge ${statusClass}">${item.status}</span></td>
                        <td>${item.location}</td>
                        <td>${item.start_time}</td>
                        <td>${item.duration}</td>
                        <td><span class="anomaly-count ${anomalyClass}">${item.anomaly_count}</span></td>
                        <td>
                            <div class="efficiency-indicator">
                                <span class="efficiency-value ${efficiencyClass}">${item.route_efficiency}%</span>
                                <div class="progress-bar">
                                    <div class="progress-fill ${efficiency >= 90 ? 'high' : efficiency >= 70 ? 'medium' : 'low'}"
                                        style="width: ${item.route_efficiency}%"></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn-sm btn-view" onclick="viewInspection('${item.id}')">查看</button>
                                <button class="btn-sm btn-edit" onclick="editInspection('${item.id}')">编辑</button>
                                <button class="btn-sm btn-delete" onclick="deleteInspection('${item.id}')">删除</button>
                            </div>
                        </td>
                    </tr>
                `;
            });
        }

        this.tableBody.innerHTML = html;
    }
}

// 初始化全局变量
let dataManager;
let tableComponent;
let paginationComponent;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async function() {
    // 初始化数据管理器
    dataManager = new DataManager();

    // 加载数据
    const dataLoaded = await dataManager.loadData();
    if (!dataLoaded) {
        alert('数据加载失败，请检查网络连接或刷新页面重试。');
        return;
    }

    // 初始化表格组件
    tableComponent = new TableComponent(dataManager, 'tableBody');

    // 初始化分页组件
    paginationComponent = new PaginationComponent(dataManager, 'paginationContainer', function() {
        tableComponent.render();
        paginationComponent.render();
    });

    // 渲染表格和分页
    tableComponent.render();
    paginationComponent.render();

    // 初始化表头排序指示器
    updateSortIndicators();

    // 初始化模态框关闭按钮
    document.querySelectorAll('.modal-close').forEach(button => {
        button.addEventListener('click', function() {
            const modalId = this.closest('.modal-overlay').id;
            closeModal(modalId);
        });
    });

    // 初始化侧边栏切换
    const sidebarToggle = document.getElementById('sidebarToggle');
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            document.querySelector('.app-container').classList.toggle('sidebar-collapsed');
        });
    }
});

// 搜索处理
function handleSearch() {
    const searchTerm = document.getElementById('searchInput').value;
    dataManager.search(searchTerm);
    tableComponent.render();
    paginationComponent.render();
}

// 筛选处理
function handleFilter() {
    const statusValue = document.getElementById('statusFilter').value;
    const typeValue = document.getElementById('typeFilter').value;
    dataManager.filter(statusValue, typeValue);
    tableComponent.render();
    paginationComponent.render();
}

// 表格排序
function sortTable(field) {
    dataManager.sort(field);
    tableComponent.render();
    updateSortIndicators();
}

// 更新排序指示器
function updateSortIndicators() {
    // 清除所有排序指示器
    document.querySelectorAll('th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
    });

    // 获取当前排序的列
    const field = dataManager.sortField;
    const direction = dataManager.sortDirection;

    // 找到对应的表头并添加排序类
    const headers = document.querySelectorAll('th');
    for (let i = 0; i < headers.length; i++) {
        const onclick = headers[i].getAttribute('onclick');
        if (onclick && onclick.includes(`sortTable('${field}')`)) {
            headers[i].classList.add(direction === 'asc' ? 'sort-asc' : 'sort-desc');
            break;
        }
    }
}

// 刷新数据
function refreshData() {
    dataManager.loadData().then(success => {
        if (success) {
            tableComponent.render();
            paginationComponent.render();
            updateSortIndicators();
            alert('数据已刷新');
        } else {
            alert('数据刷新失败');
        }
    });
}

// 导出CSV
function exportData() {
    const data = dataManager.filteredData;
    if (data.length === 0) {
        alert('没有数据可导出');
        return;
    }

    // 获取表头
    const headers = [
        'ID', '巡检名称', '巡检类型', '状态', '位置',
        '开始时间', '耗时(分钟)', '异常数', '路径效率'
    ];

    // 生成CSV内容
    let csvContent = headers.join(',') + '\n';

    data.forEach(item => {
        const row = [
            item.id,
            `"${item.name}"`,
            item.type,
            item.status,
            `"${item.location}"`,
            item.start_time,
            item.duration,
            item.anomaly_count,
            item.route_efficiency
        ];
        csvContent += row.join(',') + '\n';
    });

    // 创建下载链接
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `巡检数据_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 显示新建巡检模态框
function showNewInspectionModal() {
    // 设置默认时间为当前时间
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');

    document.getElementById('inspectionStartTime').value = `${year}-${month}-${day}T${hours}:${minutes}`;

    // 显示模态框
    openModal('newInspectionModal');
}

// 创建新巡检
function createInspection() {
    const form = document.getElementById('newInspectionForm');

    // 简单验证
    const name = document.getElementById('inspectionName').value;
    const type = document.getElementById('inspectionType').value;
    const location = document.getElementById('inspectionLocation').value;

    if (!name || !type || !location) {
        alert('请填写必填字段');
        return;
    }

    // 收集表单数据
    const newInspection = {
        name: name,
        type: type,
        location: location,
        executor: document.getElementById('inspectionExecutor').value,
        start_time: document.getElementById('inspectionStartTime').value.replace('T', ' '),
        status: document.getElementById('inspectionStatus').value,
        description: document.getElementById('inspectionDescription').value,
        route_points: document.getElementById('routePoints').value,
        duration: '0',
        anomaly_count: '0',
        route_efficiency: '0'
    };

    // 添加到数据管理器
    dataManager.addInspection(newInspection);

    // 更新UI
    tableComponent.render();
    paginationComponent.render();

    // 关闭模态框
    closeModal('newInspectionModal');

    // 重置表单
    form.reset();

    alert('巡检创建成功');
}

// 查看巡检详情
function viewInspection(id) {
    const inspection = dataManager.getInspectionById(id);
    if (!inspection) {
        alert('未找到巡检数据');
        return;
    }

    // 构建详情HTML
    let detailHtml = `
        <div class="inspection-detail">
            <div class="detail-header">
                <h4>${inspection.name}</h4>
                <span class="status-badge ${inspection.status === '已完成' ? 'completed' : inspection.status === '进行中' ? 'processing' : 'planned'}">
                    ${inspection.status}
                </span>
            </div>

            <div class="detail-section">
                <div class="detail-row">
                    <div class="detail-label">巡检ID:</div>
                    <div class="detail-value">${inspection.id}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">巡检类型:</div>
                    <div class="detail-value">${inspection.type}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">位置:</div>
                    <div class="detail-value">${inspection.location}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">执行者:</div>
                    <div class="detail-value">${inspection.executor || 'AI机器人'}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">开始时间:</div>
                    <div class="detail-value">${inspection.start_time}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">耗时(分钟):</div>
                    <div class="detail-value">${inspection.duration}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">异常数量:</div>
                    <div class="detail-value">
                        <span class="anomaly-count ${parseInt(inspection.anomaly_count) === 0 ? 'zero' : ''}">
                            ${inspection.anomaly_count}
                        </span>
                    </div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">路径效率:</div>
                    <div class="detail-value">
                        <div class="efficiency-indicator">
                            <span class="efficiency-value ${parseFloat(inspection.route_efficiency) >= 90 ? 'efficiency-high' : parseFloat(inspection.route_efficiency) >= 70 ? 'efficiency-medium' : 'efficiency-low'}">
                                ${inspection.route_efficiency}%
                            </span>
                            <div class="progress-bar">
                                <div class="progress-fill ${parseFloat(inspection.route_efficiency) >= 90 ? 'high' : parseFloat(inspection.route_efficiency) >= 70 ? 'medium' : 'low'}"
                                    style="width: ${inspection.route_efficiency}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="detail-section">
                <h5>巡检描述</h5>
                <p>${inspection.description || '无描述'}</p>
            </div>
    `;

    // 如果有路径点，显示路径可视化
    if (inspection.route_points) {
        const points = inspection.route_points.split(',').map(p => p.trim());
        detailHtml += `
            <div class="detail-section">
                <h5>巡检路径</h5>
                <div class="route-visualization">
                    <div class="route-points">
                        ${points.map((point, index) => `
                            <div class="route-point ${index === 0 ? 'active' : ''}">
                                ${point}
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
    }

    // 如果有异常，显示异常列表
    if (parseInt(inspection.anomaly_count) > 0) {
        detailHtml += `
            <div class="detail-section">
                <h5>发现的异常</h5>
                <p>此处可显示异常详情列表（示例数据）</p>
                <ul class="anomaly-list">
                    <li>温度传感器读数异常 - A区冷柜3号</li>
                    <li>货架商品摆放不规范 - B区货架5号</li>
                </ul>
            </div>
        `;
    }

    detailHtml += `</div>`;

    // 设置详情内容并显示模态框
    document.getElementById('inspectionDetailContent').innerHTML = detailHtml;
    openModal('viewInspectionModal');
}

// 编辑巡检
function editInspection(id) {
    const inspection = dataManager.getInspectionById(id);
    if (!inspection) {
        alert('未找到巡检数据');
        return;
    }

    // 填充表单
    document.getElementById('editInspectionId').value = inspection.id;
    document.getElementById('editInspectionName').value = inspection.name;
    document.getElementById('editInspectionType').value = inspection.type;
    document.getElementById('editInspectionLocation').value = inspection.location;
    document.getElementById('editInspectionExecutor').value = inspection.executor || 'AI机器人';
    document.getElementById('editInspectionStatus').value = inspection.status;
    document.getElementById('editAnomalyCount').value = inspection.anomaly_count;
    document.getElementById('editInspectionDescription').value = inspection.description || '';

    // 显示模态框
    openModal('editInspectionModal');
}

// 保存巡检编辑
function saveInspectionEdit() {
    const id = document.getElementById('editInspectionId').value;

    // 简单验证
    const name = document.getElementById('editInspectionName').value;
    const type = document.getElementById('editInspectionType').value;
    const location = document.getElementById('editInspectionLocation').value;

    if (!name || !type || !location) {
        alert('请填写必填字段');
        return;
    }

    // 收集表单数据
    const updatedData = {
        name: name,
        type: type,
        location: location,
        executor: document.getElementById('editInspectionExecutor').value,
        status: document.getElementById('editInspectionStatus').value,
        anomaly_count: document.getElementById('editAnomalyCount').value,
        description: document.getElementById('editInspectionDescription').value
    };

    // 如果状态变为已完成，自动设置一些值
    if (updatedData.status === '已完成') {
        // 随机生成一个30-120分钟的耗时
        updatedData.duration = Math.floor(Math.random() * 90 + 30).toString();

        // 随机生成70-100的路径效率
        updatedData.route_efficiency = Math.floor(Math.random() * 30 + 70).toString();
    }

    // 更新数据
    const success = dataManager.updateInspection(id, updatedData);

    if (success) {
        // 更新UI
        tableComponent.render();
        paginationComponent.render();

        // 关闭模态框
        closeModal('editInspectionModal');

        alert('巡检更新成功');
    } else {
        alert('巡检更新失败');
    }
}

// 删除巡检
function deleteInspection(id) {
    if (confirm('确定要删除此巡检吗？此操作不可撤销。')) {
        const success = dataManager.deleteInspection(id);

        if (success) {
            // 更新UI
            tableComponent.render();
            paginationComponent.render();

            alert('巡检已删除');
        } else {
            alert('巡检删除失败');
        }
    }
}

// 打开模态框
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('active');
    }
}

// 关闭模态框
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('active');
    }
}