// 生成mock数据的脚本
const mockDataGenerators = {
    // AI视觉识别巡检数据
    ai_inspection: () => {
        const data = [];
        const statuses = ['正常', '异常', '待处理'];
        const locations = ['A区货架1', 'A区货架2', 'B区货架1', 'B区货架2', 'C区货架1', 'C区货架2', 'D区冷藏区', 'E区仓储区'];

        for (let i = 1; i <= 120; i++) {
            data.push({
                id: i,
                任务编号: `AI${String(i).padStart(4, '0')}`,
                巡检时间: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
                巡检区域: locations[Math.floor(Math.random() * locations.length)],
                识别结果: statuses[Math.floor(Math.random() * statuses.length)],
                准确率: (85 + Math.random() * 15).toFixed(1) + '%',
                处理状态: Math.random() > 0.3 ? '已处理' : '待处理',
                操作员: `操作员${Math.ceil(Math.random() * 10)}`
            });
        }
        return data;
    },

    // 自动化巡检流程数据
    auto_inspection: () => {
        const data = [];
        const flowTypes = ['商品检查', '环境监测', '设备巡检', '安全检查'];
        const statuses = ['进行中', '已完成', '暂停', '故障'];

        for (let i = 1; i <= 100; i++) {
            data.push({
                id: i,
                流程编号: `AUTO${String(i).padStart(4, '0')}`,
                流程类型: flowTypes[Math.floor(Math.random() * flowTypes.length)],
                开始时间: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
                预计完成时间: new Date(Date.now() + Math.random() * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
                执行状态: statuses[Math.floor(Math.random() * statuses.length)],
                完成度: Math.floor(Math.random() * 100) + '%',
                负责设备: `设备${String(Math.ceil(Math.random() * 20)).padStart(2, '0')}`
            });
        }
        return data;
    },

    // 多终端协同操作数据
    multi_terminal: () => {
        const data = [];
        const terminals = ['PDA001', 'PDA002', 'AR001', 'AR002', 'PC001', 'PC002'];
        const operations = ['数据同步', '任务分发', '状态更新', '异常上报'];

        for (let i = 1; i <= 150; i++) {
            data.push({
                id: i,
                操作编号: `MT${String(i).padStart(4, '0')}`,
                终端设备: terminals[Math.floor(Math.random() * terminals.length)],
                操作类型: operations[Math.floor(Math.random() * operations.length)],
                操作时间: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
                同步状态: Math.random() > 0.2 ? '同步成功' : '同步失败',
                数据量: (Math.random() * 1000).toFixed(2) + 'KB',
                操作用户: `用户${Math.ceil(Math.random() * 15)}`
            });
        }
        return data;
    },

    // 异常闭环管理数据
    exception_management: () => {
        const data = [];
        const exceptionTypes = ['设备故障', '温度异常', '商品过期', '库存异常', '安全隐患'];
        const levels = ['低', '中', '高', '紧急'];
        const statuses = ['待处理', '处理中', '已解决', '已关闭'];

        for (let i = 1; i <= 80; i++) {
            data.push({
                id: i,
                异常编号: `EXC${String(i).padStart(4, '0')}`,
                异常类型: exceptionTypes[Math.floor(Math.random() * exceptionTypes.length)],
                发现时间: new Date(Date.now() - Math.random() * 15 * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
                严重级别: levels[Math.floor(Math.random() * levels.length)],
                处理状态: statuses[Math.floor(Math.random() * statuses.length)],
                责任人: `员工${Math.ceil(Math.random() * 12)}`,
                预计解决时间: new Date(Date.now() + Math.random() * 48 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' ')
            });
        }
        return data;
    },

    // 多维度实时监控数据
    realtime_monitoring: () => {
        const data = [];
        const devices = ['温湿度传感器', '摄像头', '门禁系统', '烟雾报警器', '人员定位器'];
        const zones = ['A区', 'B区', 'C区', 'D区', '冷藏室', '办公区'];

        for (let i = 1; i <= 200; i++) {
            data.push({
                id: i,
                设备编号: `MON${String(i).padStart(4, '0')}`,
                设备类型: devices[Math.floor(Math.random() * devices.length)],
                监控区域: zones[Math.floor(Math.random() * zones.length)],
                当前数值: (Math.random() * 100).toFixed(1),
                正常范围: '0-100',
                状态: Math.random() > 0.15 ? '正常' : '异常',
                最后更新: new Date(Date.now() - Math.random() * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' ')
            });
        }
        return data;
    },

    // 可视化看板与预警数据
    visual_dashboard: () => {
        const data = [];
        const alertTypes = ['温度预警', '湿度预警', '人员异常', '设备故障', '库存不足'];
        const levels = ['提醒', '警告', '严重', '紧急'];

        for (let i = 1; i <= 90; i++) {
            data.push({
                id: i,
                预警编号: `ALERT${String(i).padStart(4, '0')}`,
                预警类型: alertTypes[Math.floor(Math.random() * alertTypes.length)],
                预警级别: levels[Math.floor(Math.random() * levels.length)],
                触发时间: new Date(Date.now() - Math.random() * 48 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
                预警值: (Math.random() * 100).toFixed(1),
                阈值: (50 + Math.random() * 50).toFixed(1),
                处理状态: Math.random() > 0.4 ? '已处理' : '待处理'
            });
        }
        return data;
    },

    // 全要素数据采集数据
    data_collection: () => {
        const data = [];
        const dataTypes = ['RFID数据', 'IoT传感器数据', '图像数据', '定位数据', '环境数据'];
        const sources = ['RFID读写器', '温湿度传感器', '摄像设备', 'GPS定位器', '气体传感器'];

        for (let i = 1; i <= 180; i++) {
            data.push({
                id: i,
                采集编号: `DATA${String(i).padStart(4, '0')}`,
                数据类型: dataTypes[Math.floor(Math.random() * dataTypes.length)],
                数据源: sources[Math.floor(Math.random() * sources.length)],
                采集时间: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
                数据量: (Math.random() * 10).toFixed(2) + 'MB',
                质量评分: (80 + Math.random() * 20).toFixed(1),
                存储状态: Math.random() > 0.1 ? '已存储' : '存储失败'
            });
        }
        return data;
    },

    // 智能数据预处理数据
    data_preprocessing: () => {
        const data = [];
        const processTypes = ['数据清洗', '异常检测', '数据转换', '特征提取', '数据验证'];
        const statuses = ['等待处理', '处理中', '处理完成', '处理失败'];

        for (let i = 1; i <= 110; i++) {
            data.push({
                id: i,
                处理编号: `PREP${String(i).padStart(4, '0')}`,
                处理类型: processTypes[Math.floor(Math.random() * processTypes.length)],
                原始数据量: (Math.random() * 100).toFixed(2) + 'MB',
                处理后数据量: (Math.random() * 80).toFixed(2) + 'MB',
                处理状态: statuses[Math.floor(Math.random() * statuses.length)],
                开始时间: new Date(Date.now() - Math.random() * 12 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
                完成率: Math.floor(Math.random() * 100) + '%'
            });
        }
        return data;
    },

    // 分析模型与决策支持数据
    analysis_model: () => {
        const data = [];
        const modelTypes = ['库存预测模型', '故障预判模型', '需求分析模型', '风险评估模型', '优化推荐模型'];
        const accuracies = ['85%', '90%', '92%', '88%', '95%'];

        for (let i = 1; i <= 60; i++) {
            data.push({
                id: i,
                模型编号: `MODEL${String(i).padStart(4, '0')}`,
                模型类型: modelTypes[Math.floor(Math.random() * modelTypes.length)],
                模型版本: `v${Math.ceil(Math.random() * 5)}.${Math.ceil(Math.random() * 10)}`,
                准确率: accuracies[Math.floor(Math.random() * accuracies.length)],
                最后训练时间: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
                使用状态: Math.random() > 0.2 ? '使用中' : '待部署',
                预测结果: `预测${Math.ceil(Math.random() * 100)}项`
            });
        }
        return data;
    }
};

// 为每个页面生成对应的数据文件
const pageFileMapping = {
    'ai_inspection.html': 'ai_inspection',
    'auto_inspection.html': 'auto_inspection',
    'multi_terminal.html': 'multi_terminal',
    'exception_management.html': 'exception_management',
    'realtime_monitoring.html': 'realtime_monitoring',
    'visual_dashboard.html': 'visual_dashboard',
    'data_collection.html': 'data_collection',
    'data_preprocessing.html': 'data_preprocessing',
    'analysis_model.html': 'analysis_model'
};

// 生成所有mock数据文件
function generateAllMockData() {
    const allPages = getAllPageFiles();

    allPages.forEach(pageFile => {
        const dataKey = pageFileMapping[pageFile];
        if (dataKey && mockDataGenerators[dataKey]) {
            const data = mockDataGenerators[dataKey]();
            const jsonString = JSON.stringify(data, null, 2);

            // 这里需要在实际运行时保存到文件
            console.log(`生成 ${pageFile} 对应的数据文件: data/${dataKey}.json`);
            console.log(`数据条数: ${data.length}`);
        } else {
            // 生成通用数据
            const genericData = generateGenericData(pageFile);
            const jsonString = JSON.stringify(genericData, null, 2);
            console.log(`生成通用数据文件: data/${pageFile.replace('.html', '.json')}`);
            console.log(`数据条数: ${genericData.length}`);
        }
    });
}

// 生成通用数据
function generateGenericData(pageFile) {
    const data = [];
    const categories = ['设备管理', '系统配置', '用户管理', '日志记录', '报表统计'];
    const statuses = ['正常', '异常', '待处理', '已完成'];

    for (let i = 1; i <= 80; i++) {
        data.push({
            id: i,
            编号: `GEN${String(i).padStart(4, '0')}`,
            名称: `${pageFile.replace('.html', '')}项目${i}`,
            类别: categories[Math.floor(Math.random() * categories.length)],
            状态: statuses[Math.floor(Math.random() * statuses.length)],
            创建时间: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
            更新时间: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
            操作员: `操作员${Math.ceil(Math.random() * 8)}`
        });
    }
    return data;
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
    window.generateAllMockData = generateAllMockData;
    window.mockDataGenerators = mockDataGenerators;
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { generateAllMockData, mockDataGenerators };
}