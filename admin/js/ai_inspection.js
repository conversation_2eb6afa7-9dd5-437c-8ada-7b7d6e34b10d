// ========================================
// AI视觉识别巡检页面脚本 - 第一部分
// ========================================

// 全局变量
let dataManager;
let paginationComponent;
let inspectionData = [];

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
});

// 初始化页面
async function initializePage() {
    try {
        console.log('开始初始化页面...');

        // 初始化菜单 - 现在由menu.js统一处理
        // 菜单初始化已移至menu.js的DOMContentLoaded事件中

        // 加载数据
        await loadInspectionData();
        console.log('数据加载完成，记录数:', inspectionData.length);

        // 初始化数据管理器
        initializeDataManager();

        // 初始化分页组件
        initializePagination();

        // 更新统计信息
        updateStatistics();

        // 绑定事件
        bindEvents();

        // 显示成功消息
        showToast('页面加载完成', 'success');
    } catch (error) {
        console.error('页面初始化失败:', error);
        showToast('页面加载失败', 'error');
    }
}

// 简单的Toast提示函数
function showToast(message, type = 'info') {
    console.log(`[${type.toUpperCase()}] ${message}`);

    // 创建简单的toast提示
    const toast = document.createElement('div');
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        background: ${type === 'success' ? '#52c41a' : type === 'error' ? '#ff4d4f' : '#1890ff'};
        color: white;
        border-radius: 6px;
        z-index: 10000;
        font-size: 14px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    `;
    toast.textContent = message;

    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

// 加载巡检数据
async function loadInspectionData() {
    try {
        console.log('正在加载数据...');
        const response = await fetch('../data/ai_inspection.json');

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const jsonData = await response.json();
        console.log('原始数据:', jsonData);

        inspectionData = jsonData.ai_inspections || [];
        console.log('解析后的数据:', inspectionData);

        if (inspectionData.length === 0) {
            console.warn('数据文件为空或格式不正确');
        }
    } catch (error) {
        console.error('加载数据失败:', error);
        inspectionData = [];
        showToast('数据加载失败: ' + error.message, 'error');
    }
}

// 初始化数据管理器
function initializeDataManager() {
    console.log('初始化数据管理器...');

    // 如果没有DataManager类，创建一个简单的替代
    if (typeof DataManager === 'undefined') {
        console.log('DataManager未定义，使用简单实现');
        dataManager = createSimpleDataManager();
    } else {
        dataManager = new DataManager({
            data: inspectionData,
            pageSize: 10,
            onDataChange: updateTable,
            onPageChange: updateTable
        });
    }

    // 立即更新表格
    updateTable({ data: inspectionData.slice(0, 10), total: inspectionData.length });
}

// 创建简单的数据管理器
function createSimpleDataManager() {
    let currentData = inspectionData;
    let currentPage = 1;
    let pageSize = 10;

    return {
        data: currentData,
        pageSize: pageSize,
        currentPage: currentPage,

        setData: function(data) {
            currentData = data;
            this.data = data;
            updateTable({ data: data.slice(0, pageSize), total: data.length });
        },

        search: function(term) {
            if (!term) {
                currentData = inspectionData;
            } else {
                currentData = inspectionData.filter(item =>
                    Object.values(item).some(value =>
                        String(value).toLowerCase().includes(term.toLowerCase())
                    )
                );
            }
            updateTable({ data: currentData.slice(0, pageSize), total: currentData.length });
        },

        filter: function(filters) {
            currentData = inspectionData.filter(item => {
                return Object.keys(filters).every(key =>
                    !filters[key] || item[key] === filters[key]
                );
            });
            updateTable({ data: currentData.slice(0, pageSize), total: currentData.length });
        },

        sort: function(field, order) {
            currentData.sort((a, b) => {
                let aVal = a[field];
                let bVal = b[field];

                if (typeof aVal === 'string') {
                    aVal = aVal.toLowerCase();
                    bVal = bVal.toLowerCase();
                }

                if (order === 'desc') {
                    return bVal > aVal ? 1 : -1;
                } else {
                    return aVal > bVal ? 1 : -1;
                }
            });
            updateTable({ data: currentData.slice(0, pageSize), total: currentData.length });
        },

        getAllData: function() {
            return currentData;
        }
    };
}

// 初始化分页组件
function initializePagination() {
    console.log('初始化分页组件...');

    if (typeof PaginationComponent !== 'undefined') {
        paginationComponent = new PaginationComponent('paginationContainer', dataManager);
    } else {
        console.log('PaginationComponent未定义，创建简单分页');
        createSimplePagination();
    }
}

// 创建简单分页
function createSimplePagination() {
    const container = document.getElementById('paginationContainer');
    if (container) {
        container.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; padding: 20px; background: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);">
                <div style="color: #8c8c8c; font-size: 14px;">
                    共 ${inspectionData.length} 条记录，第 1 / 5 页
                </div>
                <div style="display: flex; gap: 8px; align-items: center;">
                    <button style="padding: 8px 12px; border: 1px solid #d9d9d9; background: white; border-radius: 4px; cursor: pointer;">首页</button>
                    <button style="padding: 8px 12px; border: 1px solid #d9d9d9; background: white; border-radius: 4px; cursor: pointer;">上一页</button>
                    <span style="padding: 8px 12px; background: #1890ff; color: white; border-radius: 4px;">1</span>
                    <span style="padding: 8px 12px; border: 1px solid #d9d9d9; background: white; border-radius: 4px; cursor: pointer;">2</span>
                    <span style="padding: 8px 12px; border: 1px solid #d9d9d9; background: white; border-radius: 4px; cursor: pointer;">3</span>
                    <button style="padding: 8px 12px; border: 1px solid #d9d9d9; background: white; border-radius: 4px; cursor: pointer;">下一页</button>
                    <button style="padding: 8px 12px; border: 1px solid #d9d9d9; background: white; border-radius: 4px; cursor: pointer;">末页</button>
                    <select style="padding: 6px 8px; border: 1px solid #d9d9d9; border-radius: 4px; background: white;">
                        <option>10条/页</option>
                        <option>20条/页</option>
                        <option>50条/页</option>
                    </select>
                </div>
            </div>
        `;
    }
}

// 更新表格数据
function updateTable(pageData) {
    const tableBody = document.getElementById('tableBody');

    console.log('updateTable called with:', pageData); // 调试信息

    if (!pageData || !pageData.data || pageData.data.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="9" style="text-align: center; padding: 40px; color: var(--td-text-color-placeholder);">
                    暂无数据
                </td>
            </tr>
        `;
        return;
    }

    tableBody.innerHTML = pageData.data.map(item => `
        <tr>
            <td>${item.task_id}</td>
            <td>${item.location}</td>
            <td>${item.inspection_type}</td>
            <td>${getStatusBadge(item.status)}</td>
            <td>${getResultBadge(item.result)}</td>
            <td>${getConfidenceBar(item.confidence)}</td>
            <td>${formatDate(item.start_time)}</td>
            <td>${item.duration || 0}</td>
            <td>${getActionButtons(item)}</td>
        </tr>
    `).join('');

    // 重新渲染分页组件
    if (paginationComponent) {
        paginationComponent.render();
    }
}

// 简单的日期格式化函数
function formatDate(dateString) {
    if (!dateString) return '-';
    try {
        const date = new Date(dateString);
        return `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
    } catch (error) {
        return dateString;
    }
}

// ========================================
// AI视觉识别巡检页面脚本 - 第二部分
// ========================================

// 获取状态标签
function getStatusBadge(status) {
    const statusMap = {
        '已完成': 'completed',
        '进行中': 'processing',
        '异常': 'error',
        '待处理': 'warning'
    };

    const className = statusMap[status] || 'completed';
    return `<span class="status-badge ${className}">${status}</span>`;
}

// 获取结果标签
function getResultBadge(result) {
    const resultMap = {
        '正常': 'completed',
        '异常': 'error',
        '温度过高': 'error',
        '发现过期商品': 'warning',
        '标签缺失': 'warning',
        '需要清洁': 'warning',
        '摆放不规范': 'warning'
    };

    const className = resultMap[result] || 'completed';
    return `<span class="status-badge ${className}">${result}</span>`;
}

// 获取置信度进度条
function getConfidenceBar(confidence) {
    if (!confidence || confidence === 0) {
        return '<span style="color: var(--td-text-color-placeholder);">-</span>';
    }

    let className = 'low';
    if (confidence >= 90) className = 'high';
    else if (confidence >= 75) className = 'medium';

    return `
        <div style="display: flex; align-items: center; gap: 8px;">
            <div class="confidence-bar">
                <div class="confidence-fill ${className}" style="width: ${confidence}%"></div>
            </div>
            <span style="font-size: 12px; color: var(--td-text-color-secondary);">${confidence}%</span>
        </div>
    `;
}

// 获取操作按钮
function getActionButtons(item) {
    return `
        <div class="action-buttons">
            <button class="btn-sm btn-view" onclick="viewInspectionDetail('${item.id}')">
                查看
            </button>
            <button class="btn-sm btn-edit" onclick="editInspection('${item.id}')">
                编辑
            </button>
            <button class="btn-sm btn-delete" onclick="deleteInspection('${item.id}')">
                删除
            </button>
        </div>
    `;
}

// 更新统计信息
function updateStatistics() {
    const total = inspectionData.length;
    const completed = inspectionData.filter(item => item.status === '已完成').length;
    const anomaly = inspectionData.filter(item =>
        item.result !== '正常' && item.result !== '处理中'
    ).length;

    // 计算平均置信度
    const validConfidences = inspectionData.filter(item => item.confidence > 0);
    const avgConfidence = validConfidences.length > 0
        ? (validConfidences.reduce((sum, item) => sum + item.confidence, 0) / validConfidences.length).toFixed(1)
        : 0;

    document.getElementById('totalTasks').textContent = total;
    document.getElementById('completedTasks').textContent = completed;
    document.getElementById('anomalyTasks').textContent = anomaly;
    document.getElementById('avgConfidence').textContent = avgConfidence + '%';
}

// 绑定事件
function bindEvents() {
    // 侧边栏切换
    const sidebarToggle = document.getElementById('sidebarToggle');
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            document.body.classList.toggle('sidebar-collapsed');
        });
    }

    // 搜索防抖
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                handleSearch();
            }, 300);
        });
    }
}

// 处理搜索
function handleSearch() {
    const searchTerm = document.getElementById('searchInput').value;
    dataManager.search(searchTerm);
    updateStatistics();
}

// 处理筛选
function handleFilter() {
    const statusFilter = document.getElementById('statusFilter').value;
    const resultFilter = document.getElementById('resultFilter').value;

    const filters = {};
    if (statusFilter) filters.status = statusFilter;
    if (resultFilter) filters.result = resultFilter;

    dataManager.filter(filters);
    updateStatistics();
}

// 表格排序
function sortTable(field) {
    const currentSort = dataManager.sortField;
    const currentOrder = dataManager.sortOrder;

    let newOrder = 'asc';
    if (currentSort === field && currentOrder === 'asc') {
        newOrder = 'desc';
    }

    dataManager.sort(field, newOrder);

    // 更新排序指示器
    updateSortIndicators(field, newOrder);
}

// 更新排序指示器
function updateSortIndicators(field, order) {
    // 清除所有排序指示器
    document.querySelectorAll('.data-table th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
    });

    // 设置当前排序指示器
    const targetTh = document.querySelector(`[onclick="sortTable('${field}')"]`);
    if (targetTh) {
        targetTh.classList.add(`sort-${order}`);
    }
}

// 刷新数据
async function refreshData() {
    showToast('正在刷新数据...', 'info');

    try {
        await loadInspectionData();
        dataManager.setData(inspectionData);
        updateStatistics();
        showToast('数据刷新成功', 'success');
    } catch (error) {
        console.error('刷新数据失败:', error);
        showToast('数据刷新失败', 'error');
    }
}

// 导出数据
function exportData() {
    const filteredData = dataManager.getAllData();

    if (filteredData.length === 0) {
        showToast('没有数据可导出', 'warning');
        return;
    }

    // 处理导出数据格式
    const exportData = filteredData.map(item => ({
        '任务编号': item.task_id,
        '检测位置': item.location,
        '设备名称': item.device_name,
        '检测类型': item.inspection_type,
        '状态': item.status,
        '检测结果': item.result,
        '置信度': item.confidence + '%',
        '检测对象': Array.isArray(item.detected_objects) ? item.detected_objects.join(', ') : '',
        '异常信息': Array.isArray(item.anomalies) ? item.anomalies.join(', ') : '',
        '开始时间': item.start_time,
        '结束时间': item.end_time || '',
        '耗时(秒)': item.duration || 0,
        '检查员': item.inspector,
        '温度(°C)': item.temperature,
        '湿度(%)': item.humidity,
        '光照条件': item.lighting,
        '备注': item.notes
    }));

    // 简单的CSV导出
    exportToCSV(exportData, `AI视觉识别巡检_${formatDateForFilename(new Date())}.csv`);
}

// 简单的CSV导出函数
function exportToCSV(data, filename) {
    if (data.length === 0) return;

    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
    ].join('\n');

    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    link.click();

    showToast('数据导出成功', 'success');
}

// 文件名日期格式化
function formatDateForFilename(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}${month}${day}_${hours}${minutes}${seconds}`;
}

// 显示新建任务模态框
function showNewTaskModal() {
    const modalContent = `
        <div class="form-group">
            <label class="form-label">检测位置</label>
            <input type="text" class="form-control" id="newLocation" placeholder="请输入检测位置">
        </div>
        <div class="form-row">
            <div class="form-col">
                <label class="form-label">设备名称</label>
                <input type="text" class="form-control" id="newDevice" placeholder="请输入设备名称">
            </div>
            <div class="form-col">
                <label class="form-label">检测类型</label>
                <select class="form-control" id="newType">
                    <option value="">请选择检测类型</option>
                    <option value="商品陈列检查">商品陈列检查</option>
                    <option value="温度异常检测">温度异常检测</option>
                    <option value="商品过期检测">商品过期检测</option>
                    <option value="人员行为分析">人员行为分析</option>
                    <option value="人流统计">人流统计</option>
                    <option value="库存盘点">库存盘点</option>
                    <option value="清洁状态检查">清洁状态检查</option>
                    <option value="安全检查">安全检查</option>
                </select>
            </div>
        </div>
        <div class="form-row">
            <div class="form-col">
                <label class="form-label">优先级</label>
                <select class="form-control" id="newPriority">
                    <option value="低">低</option>
                    <option value="中" selected>中</option>
                    <option value="高">高</option>
                </select>
            </div>
            <div class="form-col">
                <label class="form-label">检查员</label>
                <input type="text" class="form-control" id="newInspector" value="AI系统" readonly>
            </div>
        </div>
        <div class="form-group">
            <label class="form-label">备注</label>
            <textarea class="form-control" id="newNotes" rows="3" placeholder="请输入备注信息"></textarea>
        </div>
    `;

    const modalFooter = `
        <button class="btn btn-secondary" onclick="closeModal()">取消</button>
        <button class="btn btn-primary" onclick="createNewTask()">创建任务</button>
    `;

    showModal('新建AI巡检任务', modalContent, modalFooter);
}

// 创建新任务
function createNewTask() {
    const location = document.getElementById('newLocation').value.trim();
    const device = document.getElementById('newDevice').value.trim();
    const type = document.getElementById('newType').value;
    const priority = document.getElementById('newPriority').value;
    const notes = document.getElementById('newNotes').value.trim();

    if (!location || !device || !type) {
        showToast('请填写必填字段', 'warning');
        return;
    }

    // 模拟创建任务
    const newTask = {
        id: Date.now(),
        task_id: `AI-${formatDateForFilename(new Date()).substring(0, 8)}-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`,
        location: location,
        device_name: device,
        inspection_type: type,
        status: '待执行',
        result: '等待处理',
        confidence: 0,
        detected_objects: [],
        anomalies: [],
        start_time: new Date().toISOString(),
        end_time: null,
        duration: 0,
        inspector: 'AI系统',
        images: [],
        temperature: 0,
        humidity: 0,
        lighting: '待检测',
        notes: notes || '新建巡检任务'
    };

    // 添加到数据中
    inspectionData.unshift(newTask);
    dataManager.setData(inspectionData);
    updateStatistics();

    closeModal();
    showToast('巡检任务创建成功', 'success');
}

// 查看巡检详情
function viewInspectionDetail(id) {
    const item = inspectionData.find(i => i.id == id);
    if (!item) {
        showToast('数据不存在', 'error');
        return;
    }

    const modalContent = `
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
                <h4 style="margin-bottom: 16px; color: var(--td-text-color-primary);">基本信息</h4>
                <div class="detail-item">
                    <strong>任务编号:</strong> ${item.task_id}
                </div>
                <div class="detail-item">
                    <strong>检测位置:</strong> ${item.location}
                </div>
                <div class="detail-item">
                    <strong>设备名称:</strong> ${item.device_name}
                </div>
                <div class="detail-item">
                    <strong>检测类型:</strong> ${item.inspection_type}
                </div>
                <div class="detail-item">
                    <strong>状态:</strong> ${getStatusBadge(item.status)}
                </div>
                <div class="detail-item">
                    <strong>检测结果:</strong> ${getResultBadge(item.result)}
                </div>
                <div class="detail-item">
                    <strong>置信度:</strong> ${item.confidence}%
                </div>
            </div>
            <div>
                <h4 style="margin-bottom: 16px; color: var(--td-text-color-primary);">环境信息</h4>
                <div class="detail-item">
                    <strong>温度:</strong> ${item.temperature}°C
                </div>
                <div class="detail-item">
                    <strong>湿度:</strong> ${item.humidity}%
                </div>
                <div class="detail-item">
                    <strong>光照条件:</strong> ${item.lighting}
                </div>
                <div class="detail-item">
                    <strong>开始时间:</strong> ${item.start_time}
                </div>
                <div class="detail-item">
                    <strong>结束时间:</strong> ${item.end_time || '未完成'}
                </div>
                <div class="detail-item">
                    <strong>耗时:</strong> ${item.duration}秒
                </div>
                <div class="detail-item">
                    <strong>检查员:</strong> ${item.inspector}
                </div>
            </div>
        </div>

        ${item.detected_objects && item.detected_objects.length > 0 ? `
        <div style="margin-top: 20px;">
            <h4 style="margin-bottom: 12px; color: var(--td-text-color-primary);">检测对象</h4>
            <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                ${item.detected_objects.map(obj => `
                    <span class="status-badge completed">${obj}</span>
                `).join('')}
            </div>
        </div>
        ` : ''}

        ${item.anomalies && item.anomalies.length > 0 ? `
        <div style="margin-top: 20px;">
            <h4 style="margin-bottom: 12px; color: var(--td-text-color-primary);">异常信息</h4>
            <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                ${item.anomalies.map(anomaly => `
                    <span class="status-badge error">${anomaly}</span>
                `).join('')}
            </div>
        </div>
        ` : ''}

        <div style="margin-top: 20px;">
            <h4 style="margin-bottom: 12px; color: var(--td-text-color-primary);">备注</h4>
            <p style="background: var(--td-bg-color-secondarycontainer); padding: 12px; border-radius: 6px; margin: 0;">
                ${item.notes || '无备注信息'}
            </p>
        </div>

        <style>
        .detail-item {
            padding: 8px 0;
            border-bottom: 1px solid var(--td-border-color-light);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .detail-item:last-child {
            border-bottom: none;
        }
        </style>
    `;

    const modalFooter = `
        <button class="btn btn-secondary" onclick="closeModal()">关闭</button>
        <button class="btn btn-primary" onclick="editInspection('${item.id}')">编辑</button>
    `;

    showModal(`巡检详情 - ${item.task_id}`, modalContent, modalFooter);
}

// 编辑巡检
function editInspection(id) {
    const item = inspectionData.find(i => i.id == id);
    if (!item) {
        showToast('数据不存在', 'error');
        return;
    }

    closeModal();

    setTimeout(() => {
        const modalContent = `
            <div class="form-row">
                <div class="form-col">
                    <label class="form-label">任务编号</label>
                    <input type="text" class="form-control" id="editTaskId" value="${item.task_id}" readonly>
                </div>
                <div class="form-col">
                    <label class="form-label">状态</label>
                    <select class="form-control" id="editStatus">
                        <option value="待执行" ${item.status === '待执行' ? 'selected' : ''}>待执行</option>
                        <option value="进行中" ${item.status === '进行中' ? 'selected' : ''}>进行中</option>
                        <option value="已完成" ${item.status === '已完成' ? 'selected' : ''}>已完成</option>
                        <option value="异常" ${item.status === '异常' ? 'selected' : ''}>异常</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">检测位置</label>
                <input type="text" class="form-control" id="editLocation" value="${item.location}">
            </div>
            <div class="form-row">
                <div class="form-col">
                    <label class="form-label">设备名称</label>
                    <input type="text" class="form-control" id="editDevice" value="${item.device_name}">
                </div>
                <div class="form-col">
                    <label class="form-label">检测类型</label>
                    <select class="form-control" id="editType">
                        <option value="商品陈列检查" ${item.inspection_type === '商品陈列检查' ? 'selected' : ''}>商品陈列检查</option>
                        <option value="温度异常检测" ${item.inspection_type === '温度异常检测' ? 'selected' : ''}>温度异常检测</option>
                        <option value="商品过期检测" ${item.inspection_type === '商品过期检测' ? 'selected' : ''}>商品过期检测</option>
                        <option value="人员行为分析" ${item.inspection_type === '人员行为分析' ? 'selected' : ''}>人员行为分析</option>
                        <option value="人流统计" ${item.inspection_type === '人流统计' ? 'selected' : ''}>人流统计</option>
                        <option value="库存盘点" ${item.inspection_type === '库存盘点' ? 'selected' : ''}>库存盘点</option>
                        <option value="清洁状态检查" ${item.inspection_type === '清洁状态检查' ? 'selected' : ''}>清洁状态检查</option>
                        <option value="安全检查" ${item.inspection_type === '安全检查' ? 'selected' : ''}>安全检查</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-col">
                    <label class="form-label">检测结果</label>
                    <input type="text" class="form-control" id="editResult" value="${item.result}">
                </div>
                <div class="form-col">
                    <label class="form-label">置信度(%)</label>
                    <input type="number" class="form-control" id="editConfidence" value="${item.confidence}" min="0" max="100">
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">备注</label>
                <textarea class="form-control" id="editNotes" rows="3">${item.notes}</textarea>
            </div>
        `;

        const modalFooter = `
            <button class="btn btn-secondary" onclick="CommonUtils.closeModal()">取消</button>
            <button class="btn btn-primary" onclick="saveInspectionEdit('${item.id}')">保存</button>
        `;

        CommonUtils.showModal({
            title: `编辑巡检 - ${item.task_id}`,
            content: modalContent,
            footer: modalFooter,
            width: '600px'
        });
    }, 100);
}

// 保存编辑
function saveInspectionEdit(id) {
    const item = inspectionData.find(i => i.id == id);
    if (!item) {
        CommonUtils.showToast('数据不存在', 'error');
        return;
    }

    // 获取表单数据
    const status = document.getElementById('editStatus').value;
    const location = document.getElementById('editLocation').value.trim();
    const device = document.getElementById('editDevice').value.trim();
    const type = document.getElementById('editType').value;
    const result = document.getElementById('editResult').value.trim();
    const confidence = parseInt(document.getElementById('editConfidence').value) || 0;
    const notes = document.getElementById('editNotes').value.trim();

    if (!location || !device || !type || !result) {
        CommonUtils.showToast('请填写必填字段', 'warning');
        return;
    }

    // 更新数据
    item.status = status;
    item.location = location;
    item.device_name = device;
    item.inspection_type = type;
    item.result = result;
    item.confidence = confidence;
    item.notes = notes;

    // 如果状态改为已完成，设置结束时间
    if (status === '已完成' && !item.end_time) {
        item.end_time = CommonUtils.formatDate(new Date());
        if (item.start_time) {
            const startTime = new Date(item.start_time);
            const endTime = new Date(item.end_time);
            item.duration = Math.round((endTime - startTime) / 1000);
        }
    }

    // 刷新数据
    dataManager.setData(inspectionData);
    updateStatistics();

    CommonUtils.closeModal();
    CommonUtils.showToast('巡检信息更新成功', 'success');
}

// 删除巡检
function deleteInspection(id) {
    const item = inspectionData.find(i => i.id == id);
    if (!item) {
        CommonUtils.showToast('数据不存在', 'error');
        return;
    }

    if (confirm(`确定要删除巡检任务 "${item.task_id}" 吗？`)) {
        const index = inspectionData.findIndex(i => i.id == id);
        if (index > -1) {
            inspectionData.splice(index, 1);
            dataManager.setData(inspectionData);
            updateStatistics();
            showToast('巡检任务删除成功', 'success');
        }
    }
}

// 简单的模态框函数
function showModal(title, content, footer) {
    // 创建模态框HTML
    const modalHTML = `
        <div class="modal-overlay" id="modalOverlay" onclick="closeModal()">
            <div class="modal-container" onclick="event.stopPropagation()">
                <div class="modal-header">
                    <h3 class="modal-title">${title}</h3>
                    <button class="modal-close" onclick="closeModal()">×</button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
                <div class="modal-footer">
                    ${footer}
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

function closeModal() {
    const modal = document.getElementById('modalOverlay');
    if (modal) {
        modal.remove();
    }
}

// 简单菜单函数已移除，现在统一使用menu.js中的MenuRenderer