// ========================================
// 零售巡检系统后台首页脚本
// ========================================

// 图表实例存储
let chartInstances = {};

// 页面数据管理
const HomePageData = {
  // 统计数据
  stats: {
    totalDevices: 142,
    onlineDevices: 125,
    offlineDevices: 17,
    inspectionCount: 1580,
    exceptionCount: 23,
    completedTasks: 1557,
    totalLocations: 38,
    activeLocations: 35,
    todayInspections: 86,
    avgEfficiency: 92.5,
    monthlyTrend: 12.8,
    alertCount: 5
  },

  // 设备状态分布数据
  deviceStatus: {
    labels: ['在线设备', '离线设备', '故障设备', '维护中'],
    data: [125, 17, 8, 12],
    colors: ['#00a870', '#ed7b2f', '#d54941', '#0052d9']
  },

  // 巡检趋势数据（最近7天）
  inspectionTrend: {
    labels: ['01-09', '01-10', '01-11', '01-12', '01-13', '01-14', '01-15'],
    datasets: [{
      label: '巡检次数',
      data: [156, 142, 178, 165, 189, 176, 186],
      borderColor: '#0052d9',
      backgroundColor: 'rgba(0, 82, 217, 0.1)',
      tension: 0.4
    }]
  },

  // 异常类型分布
  exceptionTypes: {
    labels: ['商品有效期异常', '货架陈列问题', '设备故障', '环境异常', '其他'],
    data: [8, 6, 4, 3, 2],
    colors: ['#d54941', '#ed7b2f', '#0052d9', '#00a870', '#666666']
  },

  // 热点区域巡检数据
  hotAreas: {
    labels: ['A区', 'B区', 'C区', 'D区', 'E区', 'F区'],
    datasets: [{
      label: '巡检频次',
      data: [95, 87, 78, 92, 84, 76],
      backgroundColor: [
        'rgba(0, 82, 217, 0.8)',
        'rgba(0, 168, 112, 0.8)',
        'rgba(237, 123, 47, 0.8)',
        'rgba(213, 73, 65, 0.8)',
        'rgba(102, 102, 102, 0.8)',
        'rgba(0, 82, 217, 0.6)'
      ]
    }]
  }
};

// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
  initHomePage();
});

// 页面初始化函数
function initHomePage() {
  updateStatsCards();
  initCharts();
  startAutoRefresh();
  bindEventListeners();
}

// 更新统计卡片
function updateStatsCards() {
  const stats = HomePageData.stats;

  // 更新各个统计卡片的数值
  updateStatCard('totalDevices', stats.totalDevices, '台');
  updateStatCard('onlineDevices', stats.onlineDevices, '台', calculateTrend(stats.onlineDevices, 118));
  updateStatCard('inspectionCount', stats.inspectionCount, '次', stats.monthlyTrend + '%');
  updateStatCard('exceptionCount', stats.exceptionCount, '个', calculateTrend(stats.exceptionCount, 28, true));
  updateStatCard('todayInspections', stats.todayInspections, '次');
  updateStatCard('avgEfficiency', stats.avgEfficiency, '%', '****%');
}

// 更新单个统计卡片
function updateStatCard(cardId, value, unit, trend = null) {
  const card = document.getElementById(cardId);
  if (!card) return;

  const valueElement = card.querySelector('.stat-value');
  const trendElement = card.querySelector('.stat-trend');

  if (valueElement) {
    valueElement.textContent = formatNumber(value) + unit;
  }

  if (trendElement && trend) {
    trendElement.textContent = trend;
    // 根据趋势值设置颜色
    if (trend.startsWith('+')) {
      trendElement.className = 'stat-trend up';
    } else if (trend.startsWith('-')) {
      trendElement.className = 'stat-trend down';
    }
  }
}

// 计算趋势
function calculateTrend(current, previous, isNegative = false) {
  const change = ((current - previous) / previous * 100).toFixed(1);
  const sign = isNegative ? (change > 0 ? '-' : '+') : (change > 0 ? '+' : '');
  return sign + Math.abs(change) + '%';
}

// 格式化数字
function formatNumber(num) {
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
}

// 初始化图表
function initCharts() {
  initDeviceStatusChart();
  initInspectionTrendChart();
  initExceptionTypesChart();
  initHotAreasChart();
}

// 初始化设备状态饼图
function initDeviceStatusChart() {
  const ctx = document.getElementById('deviceStatusChart');
  if (!ctx) return;

  const data = HomePageData.deviceStatus;

  if (chartInstances.deviceStatus) {
    chartInstances.deviceStatus.destroy();
  }

  chartInstances.deviceStatus = new Chart(ctx, {
    type: 'doughnut',
    data: {
      labels: data.labels,
      datasets: [{
        data: data.data,
        backgroundColor: data.colors,
        borderWidth: 2,
        borderColor: '#fff'
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom',
          labels: {
            padding: 20,
            usePointStyle: true
          }
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              const total = context.dataset.data.reduce((a, b) => a + b, 0);
              const percentage = ((context.parsed / total) * 100).toFixed(1);
              return context.label + ': ' + context.parsed + '台 (' + percentage + '%)';
            }
          }
        }
      }
    }
  });
}

// 初始化巡检趋势折线图
function initInspectionTrendChart() {
  const ctx = document.getElementById('inspectionTrendChart');
  if (!ctx) return;

  const data = HomePageData.inspectionTrend;

  if (chartInstances.inspectionTrend) {
    chartInstances.inspectionTrend.destroy();
  }

  chartInstances.inspectionTrend = new Chart(ctx, {
    type: 'line',
    data: data,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          grid: {
            color: 'rgba(0, 0, 0, 0.1)'
          }
        },
        x: {
          grid: {
            display: false
          }
        }
      },
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          mode: 'index',
          intersect: false
        }
      },
      interaction: {
        mode: 'nearest',
        axis: 'x',
        intersect: false
      }
    }
  });
}

// 初始化异常类型饼图
function initExceptionTypesChart() {
  const ctx = document.getElementById('exceptionTypesChart');
  if (!ctx) return;

  const data = HomePageData.exceptionTypes;

  if (chartInstances.exceptionTypes) {
    chartInstances.exceptionTypes.destroy();
  }

  chartInstances.exceptionTypes = new Chart(ctx, {
    type: 'pie',
    data: {
      labels: data.labels,
      datasets: [{
        data: data.data,
        backgroundColor: data.colors,
        borderWidth: 2,
        borderColor: '#fff'
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom',
          labels: {
            padding: 15,
            usePointStyle: true,
            font: {
              size: 12
            }
          }
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              const total = context.dataset.data.reduce((a, b) => a + b, 0);
              const percentage = ((context.parsed / total) * 100).toFixed(1);
              return context.label + ': ' + context.parsed + '个 (' + percentage + '%)';
            }
          }
        }
      }
    }
  });
}

// 初始化热点区域柱状图
function initHotAreasChart() {
  const ctx = document.getElementById('hotAreasChart');
  if (!ctx) return;

  const data = HomePageData.hotAreas;

  if (chartInstances.hotAreas) {
    chartInstances.hotAreas.destroy();
  }

  chartInstances.hotAreas = new Chart(ctx, {
    type: 'bar',
    data: data,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          grid: {
            color: 'rgba(0, 0, 0, 0.1)'
          }
        },
        x: {
          grid: {
            display: false
          }
        }
      },
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              return '巡检频次: ' + context.parsed.y + '次';
            }
          }
        }
      }
    }
  });
}

// 绑定事件监听器
function bindEventListeners() {
  // 刷新按钮事件
  const refreshButtons = document.querySelectorAll('.btn-refresh');
  refreshButtons.forEach(btn => {
    btn.addEventListener('click', function() {
      refreshData();
    });
  });

  // 导出按钮事件
  const exportButtons = document.querySelectorAll('.btn-export');
  exportButtons.forEach(btn => {
    btn.addEventListener('click', function() {
      exportData();
    });
  });

  // 功能卡片点击事件
  const functionCards = document.querySelectorAll('.function-card');
  functionCards.forEach(card => {
    card.addEventListener('click', function(e) {
      e.preventDefault();
      const url = this.getAttribute('href');
      if (url && url !== '#') {
        window.location.href = url;
      }
    });
  });
}

// 刷新数据
function refreshData() {
  // 模拟数据更新
  const stats = HomePageData.stats;

  // 随机更新一些数值
  stats.onlineDevices = Math.max(115, Math.min(140, stats.onlineDevices + Math.floor(Math.random() * 6) - 3));
  stats.offlineDevices = stats.totalDevices - stats.onlineDevices;
  stats.todayInspections = Math.max(70, Math.min(100, stats.todayInspections + Math.floor(Math.random() * 6) - 3));
  stats.exceptionCount = Math.max(15, Math.min(35, stats.exceptionCount + Math.floor(Math.random() * 4) - 2));

  // 更新统计卡片
  updateStatsCards();

  // 显示刷新成功提示
  showToast('数据刷新成功', 'success');
}

// 导出数据
function exportData() {
  // 准备导出数据
  const exportData = {
    统计概览: HomePageData.stats,
    设备状态: HomePageData.deviceStatus,
    巡检趋势: HomePageData.inspectionTrend,
    异常分布: HomePageData.exceptionTypes,
    热点区域: HomePageData.hotAreas,
    导出时间: new Date().toLocaleString('zh-CN')
  };

  // 转换为JSON并下载
  const dataStr = JSON.stringify(exportData, null, 2);
  const dataBlob = new Blob([dataStr], { type: 'application/json' });

  const link = document.createElement('a');
  link.href = URL.createObjectURL(dataBlob);
  link.download = `巡检系统数据_${new Date().toISOString().split('T')[0]}.json`;
  link.click();

  showToast('数据导出成功', 'success');
}

// 自动刷新
function startAutoRefresh() {
  // 每5分钟自动刷新一次数据
  setInterval(() => {
    refreshData();
  }, 300000);
}

// 显示提示消息
function showToast(message, type = 'info') {
  // 创建提示元素
  const toast = document.createElement('div');
  toast.className = `toast toast-${type}`;
  toast.textContent = message;

  // 样式
  Object.assign(toast.style, {
    position: 'fixed',
    top: '20px',
    right: '20px',
    padding: '12px 24px',
    borderRadius: '6px',
    color: '#fff',
    fontSize: '14px',
    fontWeight: '500',
    zIndex: '9999',
    opacity: '0',
    transform: 'translateX(100%)',
    transition: 'all 0.3s ease'
  });

  // 根据类型设置背景色
  switch (type) {
    case 'success':
      toast.style.backgroundColor = '#00a870';
      break;
    case 'warning':
      toast.style.backgroundColor = '#ed7b2f';
      break;
    case 'error':
      toast.style.backgroundColor = '#d54941';
      break;
    default:
      toast.style.backgroundColor = '#0052d9';
  }

  // 添加到页面
  document.body.appendChild(toast);

  // 显示动画
  setTimeout(() => {
    toast.style.opacity = '1';
    toast.style.transform = 'translateX(0)';
  }, 100);

  // 3秒后自动隐藏
  setTimeout(() => {
    toast.style.opacity = '0';
    toast.style.transform = 'translateX(100%)';
    setTimeout(() => {
      document.body.removeChild(toast);
    }, 300);
  }, 3000);
}

// 销毁所有图表实例（页面离开时调用）
function destroyAllCharts() {
  Object.values(chartInstances).forEach(chart => {
    if (chart && typeof chart.destroy === 'function') {
      chart.destroy();
    }
  });
  chartInstances = {};
}

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
  destroyAllCharts();
});

// 窗口大小改变时重新调整图表
window.addEventListener('resize', function() {
  Object.values(chartInstances).forEach(chart => {
    if (chart && typeof chart.resize === 'function') {
      chart.resize();
    }
  });
});

// 导出主要函数供外部使用
window.HomePageUtils = {
  refreshData,
  exportData,
  showToast,
  updateStatsCards,
  destroyAllCharts
};